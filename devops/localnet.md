# Bittensor 本地网络部署指南

本指南将帮助您部署一个本地的Bittensor区块链实例，配置钱包，并创建子网。

## 目录

1. [前置条件](#前置条件)
2. [部署本地Subtensor实例](#部署本地subtensor实例)
3. [配置钱包](#配置钱包)
4. [创建子网](#创建子网)
5. [验证部署](#验证部署)
6. [故障排除](#故障排除)

## 前置条件

在开始之前，请确保您的机器上已安装以下软件：
操作系统使用ubuntu

- **Docker** - 用于运行本地区块链实例
- **Bittensor SDK** - Python SDK用于与区块链交互
- **Bittensor CLI** - 命令行工具

### 安装Bittensor SDK和CLI

```bash

pipx install bittensor
pipx install bittensor-cli
pipx inject bittensor-cli torch
```

## 部署本地Subtensor实例

### 方法1：使用Docker（推荐）

Docker是最简单的部署方式，只需要几分钟就能启动本地区块链实例。

#### 1. 拉取Docker镜像

```bash
docker pull ghcr.io/opentensor/subtensor-localnet:devnet-ready
```

#### 2. 运行容器

**快速区块模式**（推荐用于开发和测试）：
- 区块处理时间：250ms/区块
- 适合快速反馈循环操作

```bash
docker run -d --rm \
  --name test_local_chain_ \
  -p 9944:9944 \
  -p 9945:9945 \
  -e SUBTENSOR_CHAIN=local \
  -e SUBTENSOR_FAST_BLOCKS=true \
  -e SUBTENSOR_POW_DIFFICULTY=0.0001 \
  -e SUBTENSOR_POW_REGISTER_DIFFICULTY=0.0001 \
  -e SUBTENSOR_POW_REGISTER_NUM_WORKERS=1 \
  ghcr.io/opentensor/subtensor-localnet:devnet-ready
```

**标准区块模式**：
- 区块处理时间：12秒/区块
- 与主网区块间隔一致

```bash
docker run -d --rm \
  --name test_local_chain_ \
  -p 9944:9944 \
  -p 9945:9945 \
  -e SUBTENSOR_CHAIN=local \
  -e SUBTENSOR_FAST_BLOCKS=true \
  -e SUBTENSOR_POW_DIFFICULTY=1000 \
  ghcr.io/opentensor/subtensor-localnet:devnet-ready False
```



## 配置钱包

### 激活账户
```bash
btcli wallet new-coldkey --wallet.name alice --uri Alice --overwrite

btcli wallet overview --wallet.name alice --subtensor.network ws://127.0.0.1:9944

 ```

### 转账
```bash
 btcli wallet transfer \
  --wallet.name alice \
  --dest 5HawU6zg5khgWiwzsFRVMSZDHxmPpom1BA3eejDjdaDXdhXP \
  --amount 100 \
  --subtensor.network ws://127.0.0.1:9944
```
### 1. 创建钱包

```bash
# 创建冷钱包（用于存储大量资金）
btcli wallet new_coldkey --wallet.name my_coldkey

# 创建热钱包（用于日常操作） 热钱包的创建一定得有冷钱包***
btcli wallet new_hotkey --wallet.name my_hotkey
```

### 2. 为钱包充值

在本地网络中，您需要为钱包充值以便进行子网操作：

```bash
# 为冷钱包充值
btcli wallet faucet --wallet.name my_coldkey --subtensor.network ws://127.0.0.1:9944

# 为热钱包充值
btcli wallet faucet --wallet.name my_hotkey --subtensor.network ws://127.0.0.1:9944
```


### 3. 注册钱包Wallet
18.139.113.94
```bash
# 注册冷钱包
btcli wallet register --wallet.name my_coldkey --subtensor.network ws://127.0.0.1:9944

# 注册热钱包
btcli wallet register --wallet.name my_hotkey --subtensor.network ws://127.0.0.1:9944
```

## 创建子网

### 1. 创建子网

```bash
btcli subnet create --wallet.name taohash_wallet --subtensor.network ws://127.0.0.1:9944
```

### 2. 注册为子网验证者

```bash
btcli subnet register --wallet.name my_hotkey --subtensor.network ws://127.0.0.1:9944 --subtensor.netuid <NETUID>
```

### 3. 注册为子网矿工

```bash
btcli subnet register --wallet.name my_hotkey --subtensor.network ws://127.0.0.1:9944 --subtensor.netuid <NETUID> --subtensor.register_using_derived_hotkey
```

## 验证部署

### 1. 检查子网列表

```bash
btcli subnet list --network ws://127.0.0.1:9944
```

预期输出：
```
                                                           Subnets
                                                         Network: custom

        ┃        ┃ Price       ┃ Market Cap  ┃              ┃                        ┃               ┃              ┃
 Netuid ┃ Name   ┃ (τ_in/α_in) ┃ (α * Price) ┃ Emission (τ) ┃ P (τ_in, α_in)         ┃ Stake (α_out) ┃ Supply (α)   ┃ Tempo (k/n)
━━━━━━━━╇━━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━
   0    │ τ root │ 1.0000 τ/Τ  │ τ 0.00      │ τ 0.0000     │ -, -                   │ Τ 0.00        │ 0.00 Τ /21M  │ -/-
   1    │ α apex │ 1.0000 τ/α  │ τ 11.00     │ τ 0.0000     │ τ 10.00, 10.00 α       │ 1.00 α        │ 11.00 α /21M │ 77/100
```

### 2. 检查钱包余额

```bash
btcli wallet overview --wallet.name my_coldkey --subtensor.network ws://127.0.0.1:9944
```

### 3. 检查子网状态

```bash
btcli subnet list --subtensor.network ws://127.0.0.1:9944 --subtensor.netuid <NETUID>
```

### 有用的命令

```bash
# 检查本地网络状态
btcli subnet list --network ws://127.0.0.1:9944

# 查看钱包信息
btcli wallet overview --wallet.name my_coldkey --subtensor.network ws://127.0.0.1:9944

# 查看子网详细信息
btcli subnet metagraph --subtensor.network ws://127.0.0.1:9944 --subtensor.netuid <NETUID>

# 停止Docker容器
docker stop test_local_chain_
```


### 查看余额
```bash
btcli wallet balance --wallet.name taohash_wallet --subtensor.network ws://127.0.0.1:9944
```
