version: '3.8'

services:
  # TAO Hash Web 前端服务
  web:
    image: coinflow/dogemine-web:latest
    container_name: taohash-web
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # API 服务地址
      - NEXT_PUBLIC_API_URL=http://api:8000
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - taohash-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # TAO Hash API 后端服务
  api:
    image: coinflow/dogehash-api:latest
    container_name: taohash-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=production
      # 数据库配置（如果需要）
      # - DATABASE_URL=**********************************/taohash
      # Redis 配置（如果需要）
      # - REDIS_URL=redis://redis:6379/0
    volumes:
      # 如果需要持久化数据
      - api_data:/app/data
      # 如果需要挂载配置文件
      # - ./config:/app/config:ro
    restart: unless-stopped
    networks:
      - taohash-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：Redis 服务（如果API需要缓存）
  # redis:
  #   image: redis:7-alpine
  #   container_name: taohash-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - taohash-network
  #   command: redis-server --appendonly yes

  # 可选：PostgreSQL 数据库（如果需要）
  # db:
  #   image: postgres:15-alpine
  #   container_name: taohash-db
  #   environment:
  #     - POSTGRES_DB=taohash
  #     - POSTGRES_USER=taohash
  #     - POSTGRES_PASSWORD=taohash_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   restart: unless-stopped
  #   networks:
  #     - taohash-network

volumes:
  api_data:
    driver: local
  # redis_data:
  #   driver: local
  # postgres_data:
  #   driver: local

networks:
  taohash-network:
    driver: bridge 