# Web3 钱包认证指南

## 🚀 完整的钱包登录流程

本指南将展示如何使用 TaoHash API 实现完整的 Web3 钱包认证。

## 📋 API 端点

### 1. 获取登录随机数

```http
POST /accounts/nonce
Content-Type: application/json

{
  "address": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
}
```

**响应:**
```json
{
  "success": true,
  "nonce": "TaoHash 登录验证\n\n地址: 5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY\n时间: **********\n随机数: a1b2c3d4e5f6...\n\n请签名此消息以验证钱包所有权。",
  "message": "请使用您的钱包签名此消息以完成登录"
}
```

### 2. 钱包认证

```http
POST /accounts/auth
Content-Type: application/json

{
  "address": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
  "signature": "0x1234567890abcdef...",
  "message": "TaoHash 登录验证\n\n地址: 5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY\n时间: **********\n随机数: a1b2c3d4e5f6...\n\n请签名此消息以验证钱包所有权。",
  "source": "polkadot-js"
}
```

**响应:**
```json
{
  "success": true,
  "token": "abcd1234567890efgh..."
}
```

### 3. 使用认证 Token

在后续的 API 请求中，在 `Authorization` 头部包含 Bearer token:

```http
GET /dashboard/overview
Authorization: Bearer abcd1234567890efgh...
```

## 🌐 前端集成示例

### JavaScript/TypeScript 实现

```javascript
class WalletAuth {
  constructor(apiBaseUrl) {
    this.apiBaseUrl = apiBaseUrl;
    this.token = localStorage.getItem('taohash_token');
  }

  async connectWallet(walletType = 'polkadot-js') {
    try {
      // 1. 检查钱包是否可用
      const wallet = await this.getWallet(walletType);
      if (!wallet) {
        throw new Error(`${walletType} 钱包未安装或不可用`);
      }

      // 2. 连接钱包并获取账户
      await wallet.enable('TaoHash');
      const accounts = await wallet.accounts.get();
      if (accounts.length === 0) {
        throw new Error('未找到钱包账户');
      }

      const account = accounts[0];
      console.log('选择的账户:', account.address);

      // 3. 获取登录随机数
      const nonceResponse = await this.getNonce(account.address);
      if (!nonceResponse.success) {
        throw new Error('获取登录随机数失败');
      }

      // 4. 签名消息
      const signature = await this.signMessage(
        account, 
        nonceResponse.nonce,
        walletType
      );

      // 5. 验证签名并获取 token
      const authResponse = await this.authenticate({
        address: account.address,
        signature: signature,
        message: nonceResponse.nonce,
        source: walletType
      });

      if (authResponse.success) {
        this.token = authResponse.token;
        localStorage.setItem('taohash_token', this.token);
        console.log('登录成功!');
        return {
          success: true,
          address: account.address,
          token: this.token
        };
      } else {
        throw new Error(authResponse.message || '认证失败');
      }

    } catch (error) {
      console.error('钱包连接失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getWallet(walletType) {
    switch (walletType) {
      case 'polkadot-js':
        return window.injectedWeb3?.['polkadot-js'];
      case 'talisman':
        return window.injectedWeb3?.['talisman'];
      case 'subwallet':
        return window.injectedWeb3?.['subwallet-js'];
      default:
        return null;
    }
  }

  async getNonce(address) {
    const response = await fetch(`${this.apiBaseUrl}/accounts/nonce`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address })
    });
    return await response.json();
  }

  async signMessage(account, message, walletType) {
    const wallet = await this.getWallet(walletType);
    const injector = await wallet.accounts.get();
    
    if (wallet.signer?.signRaw) {
      // 使用 signRaw 方法签名
      const result = await wallet.signer.signRaw({
        address: account.address,
        data: message,
        type: 'bytes'
      });
      return result.signature;
    } else {
      throw new Error('钱包不支持消息签名');
    }
  }

  async authenticate(authData) {
    const response = await fetch(`${this.apiBaseUrl}/accounts/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(authData)
    });
    return await response.json();
  }

  async makeAuthenticatedRequest(url, options = {}) {
    if (!this.token) {
      throw new Error('未登录，请先连接钱包');
    }

    const headers = {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const response = await fetch(`${this.apiBaseUrl}${url}`, {
      ...options,
      headers
    });

    if (response.status === 401) {
      // Token 过期，清理本地存储
      this.logout();
      throw new Error('登录已过期，请重新连接钱包');
    }

    return response;
  }

  logout() {
    this.token = null;
    localStorage.removeItem('taohash_token');
    console.log('已登出');
  }

  isLoggedIn() {
    return !!this.token;
  }
}

// 使用示例
const auth = new WalletAuth('http://localhost:8000');

// 连接钱包
document.getElementById('connect-wallet').addEventListener('click', async () => {
  const result = await auth.connectWallet('polkadot-js');
  if (result.success) {
    console.log('连接成功:', result);
    // 更新 UI 状态
    updateUIForLoggedInUser(result.address);
  } else {
    console.error('连接失败:', result.error);
    alert(`连接失败: ${result.error}`);
  }
});

// 调用需要认证的 API
async function fetchDashboard() {
  try {
    const response = await auth.makeAuthenticatedRequest('/dashboard/overview');
    const data = await response.json();
    console.log('仪表板数据:', data);
  } catch (error) {
    console.error('获取仪表板数据失败:', error);
  }
}

function updateUIForLoggedInUser(address) {
  document.getElementById('wallet-address').textContent = address;
  document.getElementById('login-section').style.display = 'none';
  document.getElementById('dashboard-section').style.display = 'block';
}
```

### React 集成示例

```jsx
import React, { useState, useEffect } from 'react';

const WalletConnect = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [address, setAddress] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const auth = new WalletAuth('http://localhost:8000');

  useEffect(() => {
    // 检查是否已有保存的 token
    if (auth.isLoggedIn()) {
      setIsConnected(true);
      // 可以通过 API 验证 token 是否仍然有效
    }
  }, []);

  const handleConnect = async (walletType) => {
    setLoading(true);
    setError('');

    try {
      const result = await auth.connectWallet(walletType);
      if (result.success) {
        setIsConnected(true);
        setAddress(result.address);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDisconnect = () => {
    auth.logout();
    setIsConnected(false);
    setAddress('');
  };

  if (isConnected) {
    return (
      <div className="wallet-connected">
        <p>已连接: {address}</p>
        <button onClick={handleDisconnect}>断开连接</button>
      </div>
    );
  }

  return (
    <div className="wallet-connect">
      <h3>连接钱包</h3>
      {error && <p className="error">{error}</p>}
      
      <div className="wallet-options">
        <button 
          onClick={() => handleConnect('polkadot-js')}
          disabled={loading}
        >
          {loading ? '连接中...' : 'Polkadot.js'}
        </button>
        
        <button 
          onClick={() => handleConnect('talisman')}
          disabled={loading}
        >
          {loading ? '连接中...' : 'Talisman'}
        </button>
        
        <button 
          onClick={() => handleConnect('subwallet')}
          disabled={loading}
        >
          {loading ? '连接中...' : 'SubWallet'}
        </button>
      </div>
    </div>
  );
};

export default WalletConnect;
```

## 🔐 安全注意事项

1. **Token 存储**: 使用 `localStorage` 存储 token，但要注意 XSS 攻击风险
2. **HTTPS**: 生产环境必须使用 HTTPS
3. **Token 过期**: 实现自动刷新或重新认证机制
4. **签名验证**: 确保后端正确验证签名

## 🛠️ 故障排除

### 常见问题

1. **钱包未安装**
   ```javascript
   if (!window.injectedWeb3) {
     alert('请安装 Polkadot.js 扩展程序');
   }
   ```

2. **签名失败**
   ```javascript
   try {
     const signature = await signMessage(account, message, walletType);
   } catch (error) {
     console.error('签名失败:', error);
     // 用户可能取消了签名
   }
   ```

3. **Token 过期**
   ```javascript
   if (response.status === 401) {
     // 清理过期 token 并重新登录
     auth.logout();
     window.location.href = '/login';
   }
   ```

## 📝 开发建议

1. **用户体验**: 提供清晰的连接状态反馈
2. **错误处理**: 友好的错误提示信息
3. **兼容性**: 支持多种钱包扩展
4. **性能**: 合理的 token 缓存策略