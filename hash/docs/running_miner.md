# TAO Hash 挖矿指南
TAO Hash 子网挖矿完整指南 - Bittensor 的比特币挖矿池。

## 概述
TAO Hash 使比特币矿工能够向子网的挖矿池贡献算力。所有矿工将其算力指向单一的子网池，验证者根据他们产生的份额价值来评估和排名矿工。
矿工在 Bittensor 中获得 Alpha 代币奖励，可以兑换为 TAO 或保留作为池所有权的代表。

### 什么是份额价值？
份额价值是矿工解决区块哈希的难度。解决的难度越高，矿工获得的激励就越多。
一般来说，算力越高，提交的份额价值就越高。

## 前置要求
- 比特币挖矿硬件（ASIC）
- Python 3.10 或更高版本
- Bittensor 钱包

## 快速开始

### 步骤 1：钱包设置
在子网 14 上创建并注册 Bittensor 钱包：

```bash
# 安装 Bittensor CLI
pip install bittensor-cli

# 创建新钱包
btcli wallet create

# 在子网 14 上注册（主网）
btcli subnet register --netuid 14 --wallet.name YOUR_WALLET --wallet.hotkey YOUR_HOTKEY --network finney
```

### 步骤 2：获取池信息
获取您的挖矿池配置：

```bash
# 克隆仓库
git clone https://github.com/latent-to/taohash.git
cd taohash

# 安装依赖
pip install -e .

# 获取您的挖矿配置
python taohash/miner/miner.py \
    --wallet.name YOUR_WALLET \
    --wallet.hotkey YOUR_HOTKEY \
    --subtensor.network finney \
    --btc_address YOUR_BTC_ADDRESS
```

这将输出您的池配置：
```
=== 子网池配置 ===

普通池：
  URL: ***************:3331
  工作名: YOUR_BTC_ADDRESS.5EX7d4Eu
  密码: x

高难度池：
  URL: ***************:3332
  工作名: YOUR_BTC_ADDRESS.5EX7d4Eu
  密码: x
```

### 步骤 3：配置您的矿工
使用池信息配置您的 ASIC 矿工：
- **Stratum URL**：使用步骤 2 中的池 URL
- **工作名**：使用提供的确切工作名
- **密码**：x

输入池信息后，子网的代理将自动根据您的热键注册您的贡献，您将开始累积 alpha。

### 步骤 4：监控性能
在以下地址跟踪您的挖矿性能：**https://taohash.com/leaderboard**

排行榜显示：
- 当前算力贡献
- 产生的份额价值
- 在所有矿工中的排名
- 历史性能

## 设置最低难度

高性能 ASIC 可能需要最低难度设置。在密码后附加最低难度：

```
x;md=100000;
```
注意：遵循设置难度的格式很重要。

![最低难度设置](./images/miner_difficulties.png)

## 传统挖矿脚本

`miner_with_proxy.py` 脚本仍然受支持，并自动挖矿到子网池：

```bash
python taohash/miner/miner_with_proxy.py \
    --wallet.name YOUR_WALLET \
    --wallet.hotkey YOUR_HOTKEY \
    --subtensor.network finney \
    --btc_address YOUR_BTC_ADDRESS
```

## 故障排除

**无法连接到池**
- 使用 `miner.py` 验证池信息
- 检查防火墙设置
- 确保钱包在子网 14 上注册

**高拒绝率**
- 检查网络延迟
- 更新矿工固件

**未在排行榜上显示**
- 使用池信息中的确切工作名
- 允许 30 分钟统计时间
- 验证份额是否被接受

---

## 高级：代理设置

当您希望将所有矿工的算力指向一个点时，可以使用矿工代理。
从该点开始，它可以与子网的代理通信。
这种方法效果很好，过去一直在子网级别进行开发。

### 优势：
- 带统计信息的实时仪表板
- 无需手动工作配置

### 安装：

1. **启动代理服务：**
```bash
cd taohash/miner/proxy/taohash_proxy
docker compose up -d
```
确保配置了正确的端口，并更新 `config.toml` 以包含子网代理信息。

2. **配置矿工连接到代理：**
- Stratum URL：`<your_local_ip>:3331`
- 工作名：任意名称
- 密码：x

3. **访问仪表板：**
访问 `http://<your_local_ip>:8100` 查看实时统计信息。

4. **运行矿工脚本**
- 接下来您需要运行矿工脚本，以便它可以为您热加载配置。
- 您可以选择跳过此步骤并自己更新 config.toml（无论如何它都会保持静态）。

---

如需支持：https://github.com/latent-to/taohash/issues

祝您 TAO Hash 挖矿愉快！