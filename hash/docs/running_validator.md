# TAO Hash 验证者设置

本指南将指导您在 Bittensor 网络上设置和运行 TAO Hash 验证者。

## 前置要求

1. Bittensor 钱包
2. 子网代理凭证（由子网所有者提供）
3. Python 3.9 或更高版本

## 设置步骤

### 1. 获取子网代理凭证

联系子网所有者以获取：
- **代理 API URL**：用于检索矿工统计信息的端点
- **API 令牌**：代理 API 的身份验证令牌

这些凭证允许您的验证者根据矿工的挖矿贡献来评估他们。

### 2. Bittensor 钱包设置

确保您已创建 Bittensor 钱包：
```bash
pip install bittensor-cli
btcli wallet create
```

### 3. 克隆仓库并安装

```bash
# 克隆仓库
git clone https://github.com/latent-to/taohash.git
cd taohash

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装包
pip install -e .
```

### 4. 配置

在验证者目录中创建 `.env` 文件：

```bash
cd taohash/validator
cp .env.validator.example .env
nano .env
```

使用您的凭证更新 `.env` 文件：
```env
# Bittensor 配置
NETUID=14
SUBTENSOR_NETWORK=finney
BT_WALLET_NAME="your_wallet_name"
BT_WALLET_HOTKEY="your_hotkey_name"

# 子网代理配置（来自子网所有者）
SUBNET_PROXY_API_URL="http://proxy.example.com:8888"
SUBNET_PROXY_API_TOKEN="your-api-token-here"

# 恢复配置
RECOVERY_FILE_PATH=~/.bittensor/data/taohash/validator
RECOVERY_FILE_NAME=validator_state.json
```

### 5. 运行验证者

#### 使用 PM2（推荐）

1. 安装 PM2：
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install nodejs npm -y
sudo npm install pm2@latest -g

# macOS
brew install node
npm install pm2@latest -g
```

2. 启动验证者：
```bash
pm2 start python3 --name "taohash-validator" -- taohash/validator/validator.py run \
    --subtensor.network finney \
    --logging.info
注意：启动验证者脚本时若需发布矿池，需要保证当前该账户是子网owner

# 保存 PM2 配置
pm2 save
pm2 startup
```

#### 直接执行

```bash
python3 taohash/validator/validator.py run \
    --subtensor.network finney \
    --logging.info
```

## 重要参数

- `netuid`：设置为 14（TAO Hash 子网）
- `subtensor.network`：设置为 `finney`（主网）
- `wallet.name`：您的 Bittensor 钱包名称
- `wallet.hotkey`：您钱包的热键

## 验证者评估过程

1. 验证者每 5 分钟（25 个区块）从子网代理获取矿工统计信息
2. 他们根据矿工贡献计算份额价值
3. 权重每 `tempo` 个区块（每个纪元）基于移动平均值设置
4. 所有验证者使用相同的代理端点进行一致评估

## PM2 管理

```bash
# 查看进程
pm2 list

# 实时监控
pm2 monit

# 查看日志
pm2 logs taohash-validator
pm2 logs taohash-validator --lines 100

# 控制验证者
pm2 stop taohash-validator
pm2 restart taohash-validator
pm2 delete taohash-validator

# 日志轮转
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
```

## 故障排除

**无法连接到子网代理**
- 验证 SUBNET_PROXY_API_URL 是否正确
- 检查您的 API 令牌是否有效
- 确保与代理的网络连接

**未收到矿工数据**
- 确认矿工正在积极挖矿
- 检查代理日志是否有问题
- 验证时间同步

**钱包问题**
- 确保钱包已正确创建和注册
- 检查钱包路径是否正确
- 验证您使用的是正确的网络

## 支持

- GitHub Issues：https://github.com/latent-to/taohash/issues
- Bittensor Discord：Subnet 14 频道

祝您验证愉快！
