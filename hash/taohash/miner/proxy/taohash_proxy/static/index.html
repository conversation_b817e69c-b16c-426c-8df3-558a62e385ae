<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TaoHash Proxy Dashboard</title>
  <link rel="stylesheet" href="/static/styles.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
  <div class="container">
    <header>
      <div class="logo-title">
        <img src="/static/logos/taohash-logo.png" alt="TaoHash" style="height: 40px; width: auto;">
      </div>
      <div class="refresh-time">
        Last updated: <span id="lastRefreshed">-</span>
      </div>
    </header>


    <div class="pools-section">
      <h2>Mining Pools</h2>
      <div id="poolsContainer" class="pools-grid">
        <!-- Pool cards will be dynamically inserted here -->
      </div>
    </div>

    <div class="summary-stats">
      <div class="stat-card">
        <h3>Total Hashrate</h3>
        <div class="value" id="totalHashrate">0 H/s</div>
      </div>
      <div class="stat-card">
        <h3>Active Miners</h3>
        <div class="value" id="activeMiners">0</div>
      </div>
      <div class="stat-card">
        <h3>Total Shares</h3>
        <div class="value" id="totalShares">0</div>
      </div>
      <div class="stat-card">
        <h3>Acceptance Rate</h3>
        <div class="value" id="acceptanceRate">0%</div>
      </div>
    </div>

    <div class="chart-container">
      <h2>Hashrate History</h2>
      <div style="height: 300px;">
        <canvas id="hashrateChart"></canvas>
      </div>
    </div>

    <div style="display: flex; gap: 20px; margin-bottom: 30px;">
      <div class="chart-container" style="flex: 3;">
        <h2>Connected Miners</h2>
        <table id="minersTable">
            <thead>
              <tr>
                <th>Miner</th>
                <th>Worker</th>
                <th>Pool Type</th>
                <th>Hashrate</th>
                <th>Accepted</th>
                <th>Rejected</th>
                <th>Accept Rate</th>
                <th>Pool Diff</th>
                <th>Miner Diff</th>
              </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
      </div>
      <div class="chart-container" style="flex: 1;">
        <h2>Share Distribution</h2>
        <div style="height: 200px;">
          <canvas id="sharesChart"></canvas>
        </div>
      </div>
    </div>

    <footer>
      <img src="/static/logos/taohash-logo-with-text.png" alt="TaoHash" style="height: 30px; width: auto; margin-bottom: 10px;">
      <p>taohash.com · Powered by Bittensor</p>
    </footer>
  </div>

  <script src="/static/dashboard.js"></script>
</body>
</html> 