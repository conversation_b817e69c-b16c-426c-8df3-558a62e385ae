
# Bittensor variables
NETUID=14 # Netuid 14 for mainnet finney

# Bittensor wallet
BT_WALLET_NAME="WALLET NAME"
BT_WALLET_HOTKEY="WALLET HOTKEY NAME"
# BT_WALLET_PATH=/custom/path/to/wallets

# Bitcoin address for receiving mining rewards
BTC_ADDRESS="your_btc_address"







# ========================LEGACY===================
# Storage: Redis
# REDIS_HOST="localhost"          # Redis server host
# REDIS_PORT=6379              # Redis server port
# REDIS_TTL=7200              # TTL for pool data in seconds (2 hours)

# Storage: Json
# JSON_PATH="Enter path here"

# Proxy setup
# USE_PROXY=true              # Enable/disable proxy usage
# PROXY_PORT=3333            # Port for the proxy to listen on
# PROXY_PORT_HIGH=3322        # Port for high difficulty
# PROXY_TYPE=taohash

# By default, base path for controller managed by service. Only use if you are spinning up your own proxy.
# PROXY_BASE_PATH=/path/to/braiins-proxy  

# Mining config
# SYNC_FREQUENCY=6           # Number of times to sync per epoch (1-359)
# RECOVER_SCHEDULE=true      # Enable/disable schedule recovery between restarts
