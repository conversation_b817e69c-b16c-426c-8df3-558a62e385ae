"""
Braiins pool metrics implementation.
"""

from dataclasses import dataclass

from taohash.core.pool.pool import PoolBase
from .base import BaseMetrics


@dataclass
class BraiinsMetrics(BaseMetrics):
    """
    Mining Metrics for Braiins pool.
    Contains data about the miner's hashrate and shares for standard time windows.
    """

    hash_rate_5m: float = 0.0  # In 5 minutes, hash rate in Gh/s
    hash_rate_60m: float = 0.0
    shares_5m: float = 0.0
    shares_60m: float = 0.0

    def get_value_last_5m(self, hash_price: float) -> float:
        """
        Estimates the value generated by the miner in the last 5 minutes.
        It takes into account:
            - Miner's hashrate provided in 5 minutes
            - Hash price in USD/TH/day
        Returns the estimated value in USD per 5 minutes.
        """
        hash_rate_th = (
            self.hash_rate_5m / 1000
            if self.hash_rate_unit == "Gh/s"
            else self.hash_rate_5m
        )
        # Value per day: hash_rate_th * hash_price (USD/TH/day)
        est_value_per_day = hash_rate_th * hash_price
        est_value_per_5m = est_value_per_day * (5 / 1440)  # 1440 mins per day
        return est_value_per_5m

    def get_value_last_day(self, hash_price: float) -> float:
        """
        Estimates the value generated by the miner in the last 24 hours.
        It takes into account:
            - Miner's hashrate provided in 60 minutes
            - Hash price in USD/TH/day
        Returns the estimated value in USD per day.
        """
        hash_rate_th = (
            self.hash_rate_60m / 1000
            if self.hash_rate_unit == "Gh/s"
            else self.hash_rate_60m
        )
        est_value_per_day = hash_rate_th * hash_price
        return est_value_per_day

    def get_value_past_hour(self, hash_price: float) -> float:
        """
        Estimates the value generated by the miner in the past hour.
        It takes into account:
            - Miner's hashrate provided in 60 minutes
            - Hash price in USD/TH/day
        Returns the estimated value in USD per hour.
        """
        hash_rate_th = (
            self.hash_rate_60m / 1000
            if self.hash_rate_unit == "Gh/s"
            else self.hash_rate_60m
        )
        est_value_per_day = hash_rate_th * hash_price
        est_value_per_hour = est_value_per_day * (1 / 24)
        return est_value_per_hour


def get_metrics_for_miners(
    pool: PoolBase, hotkeys: list[str], block_at_registration: list[int], coin: str
) -> list[BraiinsMetrics]:
    """
    Retrieves the mining metrics for all miners active in the Braiins pool.
    """
    metrics = []
    all_workers = pool.get_all_miner_contributions(coin)

    hotkeys_to_workers = {}
    worker_ids_to_hotkey_idx = {}
    for i, hotkey in enumerate(hotkeys):
        worker_id = pool._get_worker_id_for_hotkey(hotkey)
        if worker_id in worker_ids_to_hotkey_idx:
            # Duplicate, choose older miner
            other_hotkey_idx = worker_ids_to_hotkey_idx[worker_id]
            if block_at_registration[other_hotkey_idx] > block_at_registration[i]:
                # Our miner is older, replace the other one
                other_hotkey = hotkeys[other_hotkey_idx]
                del hotkeys_to_workers[other_hotkey]

                worker_ids_to_hotkey_idx[worker_id] = i
                hotkeys_to_workers[hotkey] = worker_id
            else:
                # Other miner is older, ignore
                continue
        else:
            # First time we see this worker ID
            worker_ids_to_hotkey_idx[worker_id] = i
            hotkeys_to_workers[hotkey] = worker_id

    for hotkey in hotkeys:
        worker_id = hotkeys_to_workers.get(hotkey, None)
        if worker_id is None:
            metrics.append(BraiinsMetrics(hotkey=hotkey))
            continue

        worker_metrics = all_workers.get(worker_id, None)
        if worker_metrics is None:
            metrics.append(BraiinsMetrics(hotkey=hotkey))
        else:
            metrics.append(
                BraiinsMetrics(
                    hotkey=hotkey,
                    hash_rate_5m=worker_metrics.get("hash_rate_5m", 0),
                    hash_rate_60m=worker_metrics.get("hash_rate_60m", 0),
                    hash_rate_unit=worker_metrics.get("hash_rate_unit", "Gh/s"),
                    shares_5m=worker_metrics.get("shares_5m", 0),
                    shares_60m=worker_metrics.get("shares_60m", 0),
                )
            )
    return metrics
