"""
Staking Data Models

Pydantic models for staking-related API endpoints.
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class StakableValidator(BaseModel):
    """Stakable validator information."""
    
    address: str = Field(..., description="验证者地址")
    status: str = Field(..., description="状态（Active等）")
    hashrate_24h: str = Field(..., description="24小时算力")
    hashrate_1h: str = Field(..., description="1小时算力")
    total_stake: str = Field(..., description="总质押")
    weight: int = Field(..., description="权重")
    emission: int = Field(..., description="xxx产出")


class StakableValidatorsResponse(BaseModel):
    """Response model for stakable validators list."""
    
    total: int = Field(..., description="总验证者数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量", alias="pageSize")
    validators: List[StakableValidator] = Field(..., description="验证者信息列表")


class StakeRequest(BaseModel):
    """Request model for staking operation."""
    
    address: str = Field(..., description="验证者地址")
    amount: str = Field(..., description="质押数量")


class StakeResponse(BaseModel):
    """Response model for staking operation."""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")


class MyStakedNode(BaseModel):
    """User's staked node information."""
    
    address: str = Field(..., description="节点地址")
    lock_status: str = Field(..., description="锁定状态（Staking/Locked/Unlocked）")
    staked: str = Field(..., description="质押数量")
    earn: str = Field(..., description="可领取收益")
    can_claim: bool = Field(..., description="是否可领取收益")
    can_unstake: bool = Field(..., description="是否可解押")
    can_stake: bool = Field(..., description="是否可追加质押")
    can_withdraw: bool = Field(..., description="是否可提现")


class MyStakedNodesResponse(BaseModel):
    """Response model for user's staked nodes."""
    
    nodes: List[MyStakedNode] = Field(..., description="用户质押的节点列表")


class ClaimRequest(BaseModel):
    """Request model for claiming rewards."""
    
    address: str = Field(..., description="节点地址")


class ClaimResponse(BaseModel):
    """Response model for claim operation."""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")


class UnstakeRequest(BaseModel):
    """Request model for unstaking operation."""
    
    address: str = Field(..., description="节点地址")


class UnstakeResponse(BaseModel):
    """Response model for unstake operation."""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")


class WithdrawRequest(BaseModel):
    """Request model for withdraw operation."""
    
    address: str = Field(..., description="节点地址")


class WithdrawResponse(BaseModel):
    """Response model for withdraw operation."""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")
