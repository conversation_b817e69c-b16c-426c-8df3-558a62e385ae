"""
API Data Models

Pydantic models for the TAO Hash API endpoints.
"""

from .dashboard import (
    DashboardResponse,
    HashrateHistoryResponse,
    HashrateDataPoint,
    AccountsResponse,
    AccountInfo,
)
from .miners import (
    MinerListResponse,
    MinerInfo,
    MinerDetailResponse,
    NodesResponse,
    NodeInfo,
    ChangeNodeRequest,
    ChangeNodeResponse,
    EarningsOverviewResponse,
    WorkersEarningsResponse,
    WorkerEarning,
    EarningsHistoryResponse,
    EarningHistoryRecord,
    HashrateDataPoint as MinerHashrateDataPoint,
    SharesDataPoint,
)
from .validators import (
    ValidatorOverviewResponse,
    ValidatorNodeInfo,
    ValidatorMinerInfo,
    StakeRequest,
    UnstakeRequest,
    StakeResponse,
    ValidatorDetailResponse,
    ValidatorEarningsOverviewResponse,
    ValidatorEarningsChartResponse,
    EarningsChartDataPoint,
    ValidatorEarningsHistoryResponse,
    ValidatorEarningHistoryRecord,
    ValidatorWalletsResponse,
    ValidatorWallet,
    AddWalletRequest,
    EditWalletRequest,
    WalletOperationResponse,
)
from .staking import (
    StakableValidatorsResponse,
    StakableValidator,
    StakeRequest as StakingStakeRequest,
    StakeResponse as StakingStakeResponse,
    MyStakedNodesResponse,
    MyStakedNode,
    ClaimRequest,
    ClaimResponse,
    UnstakeRequest as StakingUnstakeRequest,
    UnstakeResponse,
    WithdrawRequest,
    WithdrawResponse,
)
from .accounts import (
    WalletAuthRequest,
    WalletAuthResponse,
    AccountSelectRequest,
    AccountSelectResponse,
    DisconnectResponse,
)

__all__ = [
    # Dashboard models
    "DashboardResponse",
    "HashrateHistoryResponse",
    "HashrateDataPoint",
    "AccountsResponse",
    "AccountInfo",

    # Miner models
    "MinerListResponse",
    "MinerInfo",
    "MinerDetailResponse",
    "NodesResponse",
    "NodeInfo",
    "ChangeNodeRequest",
    "ChangeNodeResponse",
    "EarningsOverviewResponse",
    "WorkersEarningsResponse",
    "WorkerEarning",
    "EarningsHistoryResponse",
    "EarningHistoryRecord",
    "MinerHashrateDataPoint",
    "SharesDataPoint",

    # Validator models
    "ValidatorOverviewResponse",
    "ValidatorNodeInfo",
    "ValidatorMinerInfo",
    "StakeRequest",
    "UnstakeRequest",
    "StakeResponse",
    "ValidatorDetailResponse",
    "ValidatorEarningsOverviewResponse",
    "ValidatorEarningsChartResponse",
    "EarningsChartDataPoint",
    "ValidatorEarningsHistoryResponse",
    "ValidatorEarningHistoryRecord",
    "ValidatorWalletsResponse",
    "ValidatorWallet",
    "AddWalletRequest",
    "EditWalletRequest",
    "WalletOperationResponse",

    # Staking models
    "StakableValidatorsResponse",
    "StakableValidator",
    "StakingStakeRequest",
    "StakingStakeResponse",
    "MyStakedNodesResponse",
    "MyStakedNode",
    "ClaimRequest",
    "ClaimResponse",
    "StakingUnstakeRequest",
    "UnstakeResponse",
    "WithdrawRequest",
    "WithdrawResponse",

    # Account models
    "WalletAuthRequest",
    "WalletAuthResponse",
    "AccountSelectRequest",
    "AccountSelectResponse",
    "DisconnectResponse",
]
