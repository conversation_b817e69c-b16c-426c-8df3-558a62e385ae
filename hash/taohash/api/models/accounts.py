"""
Account Data Models

Pydantic models for account and authentication-related API endpoints.
"""

from typing import Optional
from pydantic import BaseModel, Field


class WalletAuthRequest(BaseModel):
    """Request model for wallet authentication."""
    
    address: str = Field(..., description="钱包地址")
    signature: str = Field(..., description="签名")
    message: str = Field(..., description="签名消息")
    source: str = Field(..., description="钱包来源")


class WalletAuthResponse(BaseModel):
    """Response model for wallet authentication."""
    
    success: bool = Field(..., description="认证是否成功")
    token: Optional[str] = Field(None, description="认证令牌")
    message: Optional[str] = Field(None, description="错误消息")


class AccountSelectRequest(BaseModel):
    """Request model for account selection."""
    
    address: str = Field(..., description="要选择的账户地址")
    source: str = Field(..., description="钱包来源")


class AccountSelectResponse(BaseModel):
    """Response model for account selection."""
    
    success: bool = Field(..., description="操作是否成功")
    selected_account: Optional[dict] = Field(None, description="选中的账户信息")


class DisconnectResponse(BaseModel):
    """Response model for disconnect operation."""
    
    success: bool = Field(..., description="操作是否成功")


class NonceRequest(BaseModel):
    """Request model for nonce generation."""
    
    address: str = Field(..., description="钱包地址")


class NonceResponse(BaseModel):
    """Response model for nonce generation."""
    
    success: bool = Field(..., description="操作是否成功")
    nonce: Optional[str] = Field(None, description="用于签名的随机数")
    message: Optional[str] = Field(None, description="提示消息")
