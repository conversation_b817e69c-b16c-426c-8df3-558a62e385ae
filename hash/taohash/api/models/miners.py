"""
Miner Data Models

Pydantic models for miner-related API endpoints.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class MinerInfo(BaseModel):
    """Basic miner information."""
    
    address: str = Field(..., description="矿工地址")
    status: str = Field(..., description="状态（Active等）")
    hashrate_1h: str = Field(..., description="1小时算力")
    hashrate_24h: str = Field(..., description="24小时算力")
    shares_1h: str = Field(..., description="1小时份额")
    shares_24h: str = Field(..., description="24小时份额")
    node: str = Field(..., description="节点名称")
    stake: str = Field(..., description="质押数量")
    score: int = Field(..., description="得分")
    earning: str = Field(..., description="收益")


class MinerListResponse(BaseModel):
    """Response model for miner list."""
    
    total: int = Field(..., description="矿工总数")
    miners: List[MinerInfo] = Field(..., description="矿工信息列表")


class HashrateDataPoint(BaseModel):
    """Hashrate data point for history."""
    
    timestamp: int = Field(..., description="时间戳（秒）")
    hashrate: float = Field(..., description="算力值")


class SharesDataPoint(BaseModel):
    """Shares data point for history."""
    
    timestamp: int = Field(..., description="时间戳（秒）")
    shares: float = Field(..., description="份额值")


class MinerDetailResponse(BaseModel):
    """Response model for detailed miner information."""
    
    address: str = Field(..., description="矿工地址")
    status: str = Field(..., description="状态（Active等）")
    node: str = Field(..., description="节点名称")
    hashrate_5m: str = Field(..., description="5分钟平均算力")
    hashrate_1h: str = Field(..., description="1小时平均算力")
    hashrate_24h: str = Field(..., description="24小时平均算力")
    hashrate_max: str = Field(..., description="最大算力")
    shares_5m: str = Field(..., description="5分钟份额")
    shares_1h: str = Field(..., description="1小时份额")
    shares_24h: str = Field(..., description="24小时份额")
    shares_total: str = Field(..., description="总份额")
    hashrate_history: List[HashrateDataPoint] = Field(..., description="算力历史数据")
    shares_history: List[SharesDataPoint] = Field(..., description="份额历史数据")


class NodeInfo(BaseModel):
    """Node information."""
    
    name: str = Field(..., description="节点名称")
    url: str = Field(..., description="节点地址")


class NodesResponse(BaseModel):
    """Response model for available nodes."""
    
    nodes: List[NodeInfo] = Field(..., description="可用节点列表")


class ChangeNodeRequest(BaseModel):
    """Request model for changing miner node."""
    
    from_node: str = Field(..., description="当前节点名称")
    to_node: str = Field(..., description="目标节点名称")


class ChangeNodeResponse(BaseModel):
    """Response model for node change operation."""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")
    miner: Dict[str, Any] = Field(..., description="更新后的矿工信息")


class EarningsOverviewResponse(BaseModel):
    """Response model for earnings overview."""
    
    today_rewards: str = Field(..., description="今日收益")
    total_rewards: str = Field(..., description="累计收益")


class WorkerEarning(BaseModel):
    """Worker earning information."""
    
    address: str = Field(..., description="矿工地址")
    status: str = Field(..., description="状态")
    node: str = Field(..., description="节点")
    stake: str = Field(..., description="质押")
    score: int = Field(..., description="得分")
    earning: str = Field(..., description="收益")


class WorkersEarningsResponse(BaseModel):
    """Response model for workers earnings."""
    
    workers: List[WorkerEarning] = Field(..., description="矿工收益列表")


class EarningHistoryRecord(BaseModel):
    """Earning history record."""
    
    time: str = Field(..., description="时间")
    worker: str = Field(..., description="矿工地址")
    earning: str = Field(..., description="收益")
    wallet_address: str = Field(..., description="钱包地址")
    hash: str = Field(..., description="交易哈希")


class EarningsHistoryResponse(BaseModel):
    """Response model for earnings history."""
    
    history: List[EarningHistoryRecord] = Field(..., description="收益历史记录")
