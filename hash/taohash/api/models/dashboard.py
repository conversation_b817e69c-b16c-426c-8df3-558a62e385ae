"""
Dashboard Data Models

Pydantic models for dashboard-related API endpoints.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class DashboardResponse(BaseModel):
    """Response model for dashboard data."""
    
    hashrate_24h: str = Field(..., description="24小时平均算力")
    shares_24h: str = Field(..., description="24小时份额")
    hashrate_1h: str = Field(..., description="1小时平均算力")
    shares_1h: str = Field(..., description="1小时份额")
    total_staked: str = Field(..., description="总质押")
    tao_earnings: str = Field(..., description="TAO收益")
    doge_earnings: str = Field(..., description="Doge收益")
    ltc_earnings: str = Field(..., description="LTC收益")
    active_miners: int = Field(..., description="活跃矿工数")


class HashrateDataPoint(BaseModel):
    """Single hashrate data point."""
    
    timestamp: int = Field(..., description="时间戳（秒）")
    hashrate: float = Field(..., description="算力值")


class HashrateHistoryResponse(BaseModel):
    """Response model for hashrate history data."""
    
    range: str = Field(..., description="时间范围")
    unit: str = Field(..., description="单位")
    data: List[HashrateDataPoint] = Field(..., description="历史数据数组")


class AccountInfo(BaseModel):
    """Account information model."""
    
    name: str = Field(..., description="账户名称")
    source: str = Field(..., description="钱包来源")
    address: str = Field(..., description="钱包地址")
    selected: Optional[bool] = Field(False, description="是否为当前选中账户")


class AccountsResponse(BaseModel):
    """Response model for accounts list."""
    
    accounts: List[AccountInfo] = Field(..., description="账户列表")
