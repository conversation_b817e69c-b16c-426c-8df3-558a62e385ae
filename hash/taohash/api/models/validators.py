"""
Validator Data Models

Pydantic models for validator-related API endpoints.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class ValidatorNodeInfo(BaseModel):
    """Validator node information."""
    
    name: str = Field(..., description="节点名称")
    status: str = Field(..., description="节点状态")


class ValidatorMinerInfo(BaseModel):
    """Miner information under validator."""
    
    address: str = Field(..., description="矿工地址")
    status: str = Field(..., description="矿工状态")
    hashrate_1h: str = Field(..., description="1小时算力")
    hashrate_24h: str = Field(..., description="24小时算力")
    shares_1h: str = Field(..., description="1小时份额")
    shares_24h: str = Field(..., description="24小时份额")
    score: int = Field(..., description="得分")


class ValidatorOverviewResponse(BaseModel):
    """Response model for validator overview."""
    
    total_staked: str = Field(..., description="总质押")
    staking_status: str = Field(..., description="质押状态")
    node: ValidatorNodeInfo = Field(..., description="节点信息")
    miner_count: int = Field(..., description="矿工数量")
    latency: str = Field(..., description="网络延迟")
    cpu: str = Field(..., description="CPU使用率")
    memory: str = Field(..., description="内存使用率")
    miners: List[ValidatorMinerInfo] = Field(..., description="矿工列表")


class StakeRequest(BaseModel):
    """Request model for staking operation."""
    
    amount: str = Field(..., description="质押数量")


class UnstakeRequest(BaseModel):
    """Request model for unstaking operation."""
    
    amount: str = Field(..., description="解押数量")


class StakeResponse(BaseModel):
    """Response model for stake/unstake operations."""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")


class HashrateDataPoint(BaseModel):
    """Hashrate data point."""
    
    timestamp: int = Field(..., description="时间戳（秒）")
    hashrate: float = Field(..., description="算力值")


class SharesDataPoint(BaseModel):
    """Shares data point."""
    
    timestamp: int = Field(..., description="时间戳（秒）")
    shares: float = Field(..., description="份额值")


class ValidatorDetailResponse(BaseModel):
    """Response model for validator detail."""
    
    address: str = Field(..., description="验证者地址")
    status: str = Field(..., description="状态（Active等）")
    node: str = Field(..., description="节点名称")
    hashrate_5m: str = Field(..., description="5分钟平均算力")
    hashrate_1h: str = Field(..., description="1小时平均算力")
    hashrate_24h: str = Field(..., description="24小时平均算力")
    hashrate_max: str = Field(..., description="最大算力")
    shares_5m: str = Field(..., description="5分钟份额")
    shares_1h: str = Field(..., description="1小时份额")
    shares_24h: str = Field(..., description="24小时份额")
    shares_total: str = Field(..., description="总份额")
    hashrate_history: List[HashrateDataPoint] = Field(..., description="算力历史数据")
    shares_history: List[SharesDataPoint] = Field(..., description="份额历史数据")


class ValidatorEarningsOverviewResponse(BaseModel):
    """Response model for validator earnings overview."""
    
    yesterday_xxx_rewards: str = Field(..., description="昨日xxx收益")
    yesterday_ltc_rewards: str = Field(..., description="昨日LTC收益")
    yesterday_doge_rewards: str = Field(..., description="昨日Doge收益")
    total_xxx_rewards: str = Field(..., description="累计xxx收益")
    total_ltc_rewards: str = Field(..., description="累计LTC收益")
    total_doge_rewards: str = Field(..., description="累计Doge收益")
    total_rewards: str = Field(..., description="累计总收益")


class EarningsChartDataPoint(BaseModel):
    """Earnings chart data point."""
    
    timestamp: int = Field(..., description="时间戳（秒）")
    xxx: float = Field(..., description="xxx收益")
    ltc: float = Field(..., description="LTC收益")
    doge: float = Field(..., description="Doge收益")


class ValidatorEarningsChartResponse(BaseModel):
    """Response model for validator earnings chart."""
    
    range: str = Field(..., description="时间范围")
    unit: str = Field(..., description="单位")
    data: List[EarningsChartDataPoint] = Field(..., description="历史数据数组")


class ValidatorEarningHistoryRecord(BaseModel):
    """Validator earning history record."""
    
    time: str = Field(..., description="时间")
    avg_hashrate: str = Field(..., description="平均算力")
    earning: str = Field(..., description="收益")
    wallet_address: str = Field(..., description="钱包地址")
    hash: str = Field(..., description="交易哈希")


class ValidatorEarningsHistoryResponse(BaseModel):
    """Response model for validator earnings history."""
    
    history: List[ValidatorEarningHistoryRecord] = Field(..., description="收益历史记录")


class ValidatorWallet(BaseModel):
    """Validator wallet information."""
    
    coin: str = Field(..., description="币种")
    wallet_name: str = Field(..., description="钱包名称")
    address: str = Field(..., description="钱包地址")


class ValidatorWalletsResponse(BaseModel):
    """Response model for validator wallets."""
    
    wallets: List[ValidatorWallet] = Field(..., description="钱包列表")


class AddWalletRequest(BaseModel):
    """Request model for adding wallet."""
    
    coin: str = Field(..., description="币种")
    wallet_name: str = Field(..., description="钱包名称")
    address: str = Field(..., description="钱包地址")


class EditWalletRequest(BaseModel):
    """Request model for editing wallet."""
    
    coin: str = Field(..., description="币种（如LTC、Doge）")
    wallet_name: Optional[str] = Field(None, description="新钱包名称")
    address: Optional[str] = Field(None, description="新钱包地址")


class WalletOperationResponse(BaseModel):
    """Response model for wallet operations."""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")


# For compatibility with the models/__init__.py imports
ValidatorInfo = ValidatorMinerInfo
ValidatorListResponse = ValidatorOverviewResponse
