"""
API Dependencies

Shared dependency providers for the FastAPI application.
"""

from taohash.api.services import AccountService

# 全局共享的 AccountService 实例
_account_service_instance = None


def get_account_service() -> AccountService:
    """
    获取共享的 AccountService 实例
    
    这确保在整个应用程序中使用相同的实例，
    避免 nonce 和会话数据在不同请求间丢失。
    
    Returns:
        AccountService: 共享的账户服务实例
    """
    global _account_service_instance
    if _account_service_instance is None:
        _account_service_instance = AccountService()
    return _account_service_instance