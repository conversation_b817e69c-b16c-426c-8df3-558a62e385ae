version: '3.8'

services:
  taohash-api:
    build:
      context: ../../..  # 从项目根目录构建
      dockerfile: hash/taohash/api/Dockerfile.dev
    container_name: taohash-api-dev
    ports:
      - "8000:8000"
    volumes:
      # 挂载源代码以支持热重载
      - ../../../hash/taohash:/app/taohash:rw
      - ../../../hash/api_server.py:/app/api_server.py:rw
      - ../../../hash/start_api.py:/app/start_api.py:rw
      # 挂载日志目录
      - ../../../hash/logs:/app/logs:rw
      # 挂载数据目录（如果需要）
      - ../../../hash/data:/app/data:rw
    environment:
      - PYTHONPATH=/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=development
      - LOG_LEVEL=debug
    networks:
      - taohash-network
    restart: unless-stopped
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: taohash-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - taohash-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  taohash-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
