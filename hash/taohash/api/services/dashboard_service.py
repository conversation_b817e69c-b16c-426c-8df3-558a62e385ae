"""
Dashboard Service

Business logic for dashboard-related operations.
"""

import time
from typing import Dict, Any, List, Optional
from taohash.core.pricing import CoinPriceAPI
from taohash.core.storage import BaseRedisStorage, BaseJsonStorage
from taohash.api.models.dashboard import (
    DashboardResponse,
    HashrateHistoryResponse,
    HashrateDataPoint,
    AccountsResponse,
    AccountInfo,
)


class DashboardService:
    """Service for dashboard operations."""
    
    def __init__(self, storage_type: str = "memory"):
        """Initialize dashboard service."""
        self.price_api = CoinPriceAPI("coingecko", None)

        # For now, use in-memory storage to avoid configuration issues
        # In production, this should be replaced with proper Redis/JSON storage
        self.storage = None  # Not used in current implementation
    
    async def get_dashboard_data(self, user_address: str) -> DashboardResponse:
        """
        Get dashboard data for a user.
        
        Args:
            user_address: User's wallet address
            
        Returns:
            Dashboard data response
        """
        try:
            # Get aggregated mining data
            hashrate_24h = await self._get_hashrate_24h(user_address)
            hashrate_1h = await self._get_hashrate_1h(user_address)
            shares_24h = await self._get_shares_24h(user_address)
            shares_1h = await self._get_shares_1h(user_address)
            
            # Get staking data
            total_staked = await self._get_total_staked(user_address)
            
            # Get earnings data
            tao_earnings = await self._get_tao_earnings(user_address)
            doge_earnings = await self._get_doge_earnings(user_address)
            ltc_earnings = await self._get_ltc_earnings(user_address)
            
            # Get active miners count
            active_miners = await self._get_active_miners_count(user_address)
            
            return DashboardResponse(
                hashrate_24h=hashrate_24h,
                shares_24h=shares_24h,
                hashrate_1h=hashrate_1h,
                shares_1h=shares_1h,
                total_staked=total_staked,
                tao_earnings=tao_earnings,
                doge_earnings=doge_earnings,
                ltc_earnings=ltc_earnings,
                active_miners=active_miners,
            )
        except Exception as e:
            # Return default values on error
            return DashboardResponse(
                hashrate_24h="0 Ph/s",
                shares_24h="0 sh",
                hashrate_1h="0 Ph/s", 
                shares_1h="0 sh",
                total_staked="0 TAO",
                tao_earnings="0 TAO",
                doge_earnings="0 DOGE",
                ltc_earnings="0 LTC",
                active_miners=0,
            )
    
    async def get_hashrate_history(self, range_param: str = "24h") -> HashrateHistoryResponse:
        """
        Get hashrate history data.
        
        Args:
            range_param: Time range (24h, 7d, 30d)
            
        Returns:
            Hashrate history response
        """
        try:
            # Generate sample data based on range
            data_points = await self._generate_hashrate_history(range_param)
            
            return HashrateHistoryResponse(
                range=range_param,
                unit="Ph/s",
                data=data_points,
            )
        except Exception:
            # Return empty data on error
            return HashrateHistoryResponse(
                range=range_param,
                unit="Ph/s",
                data=[],
            )
    
    async def get_accounts(self, user_address: str) -> AccountsResponse:
        """
        Get user accounts list.
        
        Args:
            user_address: User's wallet address
            
        Returns:
            Accounts response
        """
        try:
            # Get stored accounts for user
            accounts_data = await self._get_user_accounts(user_address)
            
            accounts = [
                AccountInfo(
                    name=account.get("name", "Unknown"),
                    source=account.get("source", "unknown"),
                    address=account.get("address", ""),
                    selected=account.get("selected", False),
                )
                for account in accounts_data
            ]
            
            return AccountsResponse(accounts=accounts)
        except Exception:
            # Return empty accounts on error
            return AccountsResponse(accounts=[])
    
    # Private helper methods
    
    async def _get_hashrate_24h(self, user_address: str) -> str:
        """Get 24h hashrate for user."""
        # Implementation would query storage for user's miners
        return "115.39 Ph/s"
    
    async def _get_hashrate_1h(self, user_address: str) -> str:
        """Get 1h hashrate for user."""
        return "62.38 Ph/s"
    
    async def _get_shares_24h(self, user_address: str) -> str:
        """Get 24h shares for user."""
        return "2.31T sh"
    
    async def _get_shares_1h(self, user_address: str) -> str:
        """Get 1h shares for user."""
        return "52.29B sh"
    
    async def _get_total_staked(self, user_address: str) -> str:
        """Get total staked amount for user."""
        return "115.39 TAO"
    
    async def _get_tao_earnings(self, user_address: str) -> str:
        """Get TAO earnings for user."""
        return "115.39 TAO"
    
    async def _get_doge_earnings(self, user_address: str) -> str:
        """Get DOGE earnings for user."""
        return "115.39 DOGE"
    
    async def _get_ltc_earnings(self, user_address: str) -> str:
        """Get LTC earnings for user."""
        return "115.39 LTC"
    
    async def _get_active_miners_count(self, user_address: str) -> int:
        """Get active miners count for user."""
        return 158
    
    async def _generate_hashrate_history(self, range_param: str) -> List[HashrateDataPoint]:
        """Generate hashrate history data points."""
        current_time = int(time.time())
        data_points = []
        
        if range_param == "24h":
            # 24 data points for 24 hours
            for i in range(24):
                timestamp = current_time - (23 - i) * 3600
                hashrate = 800.0 - (i * 10) + (i % 3) * 50  # Sample varying data
                data_points.append(HashrateDataPoint(timestamp=timestamp, hashrate=hashrate))
        elif range_param == "7d":
            # 7 data points for 7 days
            for i in range(7):
                timestamp = current_time - (6 - i) * 86400
                hashrate = 750.0 + (i * 20)
                data_points.append(HashrateDataPoint(timestamp=timestamp, hashrate=hashrate))
        elif range_param == "30d":
            # 30 data points for 30 days
            for i in range(30):
                timestamp = current_time - (29 - i) * 86400
                hashrate = 700.0 + (i * 5) + (i % 7) * 30
                data_points.append(HashrateDataPoint(timestamp=timestamp, hashrate=hashrate))
        
        return data_points
    
    async def _get_user_accounts(self, user_address: str) -> List[Dict[str, Any]]:
        """Get user accounts from storage."""
        # This would query the storage for user's connected accounts
        return [
            {
                "name": "ccbaka",
                "source": "talisman",
                "address": "5Gnzm...cbsul",
                "selected": False,
            },
            {
                "name": "ccbaka", 
                "source": "@opentensor/bittensor extension",
                "address": user_address,
                "selected": True,
            },
            {
                "name": "ccbaka",
                "source": "polkadot",
                "address": "5a02k...gsoo2",
                "selected": False,
            },
        ]
