"""
Staking Service

Business logic for staking-related operations.
"""

from typing import Dict, Any, List, Optional
from taohash.core.storage import BaseRedisStorage, BaseJsonStorage
from taohash.api.models.staking import (
    StakableValidatorsResponse,
    StakableValidator,
    StakeResponse,
    MyStakedNodesResponse,
    MyStakedNode,
    ClaimResponse,
    UnstakeResponse,
    WithdrawResponse,
)


class StakingService:
    """Service for staking operations."""
    
    def __init__(self, storage_type: str = "memory"):
        """Initialize staking service."""
        # For now, use in-memory storage to avoid configuration issues
        # In production, this should be replaced with proper Redis/JSON storage
        self.storage = None  # Not used in current implementation
    
    async def get_stakable_validators(
        self,
        search: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
        node: Optional[str] = None,
    ) -> StakableValidatorsResponse:
        """
        Get list of stakable validators.
        
        Args:
            search: Optional search term for validator address
            page: Page number
            page_size: Number of validators per page
            node: Optional node filter
            
        Returns:
            Stakable validators response
        """
        try:
            # Get validators from storage
            validators_data = await self._get_stakable_validators_data(
                search, page, page_size, node
            )
            
            validators = [
                StakableValidator(
                    address=validator.get("address", ""),
                    status=validator.get("status", "Unknown"),
                    hashrate_24h=validator.get("hashrate_24h", "0 Ph/s"),
                    hashrate_1h=validator.get("hashrate_1h", "0 Ph/s"),
                    total_stake=validator.get("total_stake", "0 TAO"),
                    weight=validator.get("weight", 0),
                    emission=validator.get("emission", 0),
                )
                for validator in validators_data["validators"]
            ]
            
            return StakableValidatorsResponse(
                total=validators_data["total"],
                page=page,
                page_size=page_size,
                validators=validators,
            )
        except Exception:
            # Return empty list on error
            return StakableValidatorsResponse(
                total=0,
                page=page,
                page_size=page_size,
                validators=[],
            )
    
    async def stake(self, user_address: str, validator_address: str, amount: str) -> StakeResponse:
        """
        Perform staking operation.
        
        Args:
            user_address: User's wallet address
            validator_address: Validator address to stake to
            amount: Amount to stake
            
        Returns:
            Stake response
        """
        try:
            # Perform staking operation
            await self._perform_stake_operation(user_address, validator_address, amount)
            
            return StakeResponse(success=True, message="质押成功")
        except Exception as e:
            return StakeResponse(success=False, message=f"质押失败: {str(e)}")
    
    async def get_my_staked_nodes(
        self, user_address: str, status: Optional[str] = None
    ) -> MyStakedNodesResponse:
        """
        Get user's staked nodes.
        
        Args:
            user_address: User's wallet address
            status: Optional status filter (Staking/Locked/Unlocked)
            
        Returns:
            My staked nodes response
        """
        try:
            # Get user's staked nodes from storage
            nodes_data = await self._get_user_staked_nodes(user_address, status)
            
            nodes = [
                MyStakedNode(
                    address=node.get("address", ""),
                    lock_status=node.get("lock_status", "Unknown"),
                    staked=node.get("staked", "0 TAO"),
                    earn=node.get("earn", "0"),
                    can_claim=node.get("can_claim", False),
                    can_unstake=node.get("can_unstake", False),
                    can_stake=node.get("can_stake", False),
                    can_withdraw=node.get("can_withdraw", False),
                )
                for node in nodes_data
            ]
            
            return MyStakedNodesResponse(nodes=nodes)
        except Exception:
            # Return empty list on error
            return MyStakedNodesResponse(nodes=[])
    
    async def claim_rewards(self, user_address: str, node_address: str) -> ClaimResponse:
        """
        Claim rewards from a staked node.
        
        Args:
            user_address: User's wallet address
            node_address: Node address to claim from
            
        Returns:
            Claim response
        """
        try:
            # Perform claim operation
            await self._perform_claim_operation(user_address, node_address)
            
            return ClaimResponse(success=True, message="收益领取成功")
        except Exception as e:
            return ClaimResponse(success=False, message=f"收益领取失败: {str(e)}")
    
    async def unstake(self, user_address: str, node_address: str) -> UnstakeResponse:
        """
        Unstake from a node.
        
        Args:
            user_address: User's wallet address
            node_address: Node address to unstake from
            
        Returns:
            Unstake response
        """
        try:
            # Perform unstake operation
            await self._perform_unstake_operation(user_address, node_address)
            
            return UnstakeResponse(success=True, message="解押成功")
        except Exception as e:
            return UnstakeResponse(success=False, message=f"解押失败: {str(e)}")
    
    async def withdraw(self, user_address: str, node_address: str) -> WithdrawResponse:
        """
        Withdraw from an unlocked node.
        
        Args:
            user_address: User's wallet address
            node_address: Node address to withdraw from
            
        Returns:
            Withdraw response
        """
        try:
            # Perform withdraw operation
            await self._perform_withdraw_operation(user_address, node_address)
            
            return WithdrawResponse(success=True, message="提现成功")
        except Exception as e:
            return WithdrawResponse(success=False, message=f"提现失败: {str(e)}")
    
    # Private helper methods
    
    async def _get_stakable_validators_data(
        self,
        search: Optional[str],
        page: int,
        page_size: int,
        node: Optional[str],
    ) -> Dict[str, Any]:
        """Get stakable validators data from storage."""
        # Sample data - in real implementation, query storage
        validators = [
            {
                "address": "5dhds...k9a",
                "status": "Active",
                "hashrate_24h": "320.05 Ph/s",
                "hashrate_1h": "320.05 Ph/s",
                "total_stake": "500 TAO",
                "weight": 98,
                "emission": 100,
            }
        ]
        
        # Apply search filter if provided
        if search:
            validators = [v for v in validators if search.lower() in v["address"].lower()]
        
        # Apply node filter if provided
        if node:
            # In real implementation, filter by node
            pass
        
        # Apply pagination
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_validators = validators[start_idx:end_idx]
        
        return {"total": len(validators), "validators": paginated_validators}
    
    async def _perform_stake_operation(
        self, user_address: str, validator_address: str, amount: str
    ) -> None:
        """Perform staking operation."""
        # Implementation would interact with blockchain
        pass
    
    async def _get_user_staked_nodes(
        self, user_address: str, status: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Get user's staked nodes from storage."""
        # Sample data - in real implementation, query storage
        nodes = [
            {
                "address": "5dhds...k9a",
                "lock_status": "Staking",
                "staked": "500 TAO",
                "earn": "100 xxx",
                "can_claim": True,
                "can_unstake": True,
                "can_stake": True,
                "can_withdraw": False,
            },
            {
                "address": "5dhds...k9b",
                "lock_status": "Locked",
                "staked": "500 TAO",
                "earn": "100 xxx",
                "can_claim": True,
                "can_unstake": False,
                "can_stake": False,
                "can_withdraw": False,
            },
            {
                "address": "5dhds...k9c",
                "lock_status": "Unlocked",
                "staked": "500 TAO",
                "earn": "100 xxx",
                "can_claim": True,
                "can_unstake": False,
                "can_stake": False,
                "can_withdraw": True,
            },
        ]
        
        # Apply status filter if provided
        if status:
            nodes = [n for n in nodes if n["lock_status"] == status]
        
        return nodes
    
    async def _perform_claim_operation(self, user_address: str, node_address: str) -> None:
        """Perform claim operation."""
        # Implementation would interact with blockchain
        pass
    
    async def _perform_unstake_operation(self, user_address: str, node_address: str) -> None:
        """Perform unstake operation."""
        # Implementation would interact with blockchain
        pass
    
    async def _perform_withdraw_operation(self, user_address: str, node_address: str) -> None:
        """Perform withdraw operation."""
        # Implementation would interact with blockchain
        pass
