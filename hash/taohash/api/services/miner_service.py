"""
Miner Service

Business logic for miner-related operations.
"""

import time
from typing import Dict, Any, List, Optional
from taohash.miner import BaseMiner
from taohash.core.storage import BaseRedisStorage, BaseJsonStorage
from taohash.api.models.miners import (
    MinerListResponse,
    MinerInfo,
    MinerDetailResponse,
    HashrateDataPoint,
    SharesDataPoint,
    NodesResponse,
    NodeInfo,
    ChangeNodeResponse,
    EarningsOverviewResponse,
    WorkersEarningsResponse,
    WorkerEarning,
    EarningsHistoryResponse,
    EarningHistoryRecord,
)


class MinerService:
    """Service for miner operations."""
    
    def __init__(self, storage_type: str = "memory", init_miner: bool = False):
        """Initialize miner service.

        Note: BaseMiner initialization is expensive (connects to chain, loads metagraph).
        Keep it disabled by default to ensure API responsiveness.
        """
        self.miner = None
        if init_miner:
            try:
                self.miner = BaseMiner()
            except Exception:
                self.miner = None

        # For now, use in-memory storage to avoid configuration issues
        # In production, this should be replaced with proper Redis/JSON storage
        self.storage = None  # Not used in current implementation
    
    async def get_miner_list(
        self,
        user_address: str,
        search: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
    ) -> MinerListResponse:
        """
        Get miner list for a user.
        
        Args:
            user_address: User's wallet address
            search: Optional search term for miner address
            page: Page number
            page_size: Number of miners per page
            
        Returns:
            Miner list response
        """
        try:
            # Get miners from storage
            miners_data = await self._get_user_miners(user_address, search, page, page_size)
            
            miners = [
                MinerInfo(
                    address=miner.get("address", ""),
                    status=miner.get("status", "Unknown"),
                    hashrate_1h=miner.get("hashrate_1h", "0 Ph/s"),
                    hashrate_24h=miner.get("hashrate_24h", "0 Ph/s"),
                    shares_1h=miner.get("shares_1h", "0 sh"),
                    shares_24h=miner.get("shares_24h", "0 sh"),
                    node=miner.get("node", "unknown"),
                    stake=miner.get("stake", "0 TAO"),
                    score=miner.get("score", 0),
                    earning=miner.get("earning", "0"),
                )
                for miner in miners_data["miners"]
            ]
            
            return MinerListResponse(
                total=miners_data["total"],
                miners=miners,
            )
        except Exception:
            # Return empty list on error
            return MinerListResponse(total=0, miners=[])
    
    async def get_miner_detail(self, address: str) -> MinerDetailResponse:
        """
        Get detailed information for a specific miner.
        
        Args:
            address: Miner address
            
        Returns:
            Miner detail response
        """
        try:
            # Get miner data from storage
            miner_data = await self._get_miner_data(address)
            
            # Generate history data
            hashrate_history = await self._generate_hashrate_history()
            shares_history = await self._generate_shares_history()
            
            return MinerDetailResponse(
                address=address,
                status=miner_data.get("status", "Active"),
                node=miner_data.get("node", "node1"),
                hashrate_5m=miner_data.get("hashrate_5m", "$0.00/Hour"),
                hashrate_1h=miner_data.get("hashrate_1h", "$0.00/Hour"),
                hashrate_24h=miner_data.get("hashrate_24h", "$0.00/Hour"),
                hashrate_max=miner_data.get("hashrate_max", "$0.00/Hour"),
                shares_5m=miner_data.get("shares_5m", "3.19T sh"),
                shares_1h=miner_data.get("shares_1h", "3.19T sh"),
                shares_24h=miner_data.get("shares_24h", "3.19T sh"),
                shares_total=miner_data.get("shares_total", "3.19T sh"),
                hashrate_history=hashrate_history,
                shares_history=shares_history,
            )
        except Exception:
            # Return default data on error
            return MinerDetailResponse(
                address=address,
                status="Unknown",
                node="unknown",
                hashrate_5m="$0.00/Hour",
                hashrate_1h="$0.00/Hour",
                hashrate_24h="$0.00/Hour",
                hashrate_max="$0.00/Hour",
                shares_5m="0 sh",
                shares_1h="0 sh",
                shares_24h="0 sh",
                shares_total="0 sh",
                hashrate_history=[],
                shares_history=[],
            )
    
    async def get_available_nodes(self) -> NodesResponse:
        """
        Get list of available nodes.
        
        Returns:
            Available nodes response
        """
        nodes = [
            NodeInfo(name="Node1", url="stratum+tcp://node1.taohash.com:3333"),
            NodeInfo(name="Node2", url="stratum+tcp://node2.taohash.com:3333"),
            NodeInfo(name="Node3", url="stratum+tcp://node3.taohash.com:3333"),
            NodeInfo(name="Node4", url="stratum+tcp://node4.taohash.com:3333"),
        ]
        
        return NodesResponse(nodes=nodes)
    
    async def change_miner_node(
        self, address: str, from_node: str, to_node: str
    ) -> ChangeNodeResponse:
        """
        Change miner's node.
        
        Args:
            address: Miner address
            from_node: Current node name
            to_node: Target node name
            
        Returns:
            Change node response
        """
        try:
            # Update miner's node in storage
            await self._update_miner_node(address, to_node)
            
            return ChangeNodeResponse(
                success=True,
                message="节点切换成功",
                miner={"address": address, "current_node": to_node},
            )
        except Exception as e:
            return ChangeNodeResponse(
                success=False,
                message=f"节点切换失败: {str(e)}",
                miner={"address": address, "current_node": from_node},
            )
    
    async def get_earnings_overview(self, user_address: str) -> EarningsOverviewResponse:
        """
        Get earnings overview for user.
        
        Args:
            user_address: User's wallet address
            
        Returns:
            Earnings overview response
        """
        try:
            earnings_data = await self._get_user_earnings(user_address)
            
            return EarningsOverviewResponse(
                today_rewards=earnings_data.get("today_rewards", "0 USD"),
                total_rewards=earnings_data.get("total_rewards", "0 USD"),
            )
        except Exception:
            return EarningsOverviewResponse(
                today_rewards="0 USD",
                total_rewards="0 USD",
            )
    
    async def get_workers_earnings(self, user_address: str) -> WorkersEarningsResponse:
        """
        Get workers earnings for user.
        
        Args:
            user_address: User's wallet address
            
        Returns:
            Workers earnings response
        """
        try:
            workers_data = await self._get_workers_earnings_data(user_address)
            
            workers = [
                WorkerEarning(
                    address=worker.get("address", ""),
                    status=worker.get("status", "Unknown"),
                    node=worker.get("node", "unknown"),
                    stake=worker.get("stake", "0 TAO"),
                    score=worker.get("score", 0),
                    earning=worker.get("earning", "0"),
                )
                for worker in workers_data
            ]
            
            return WorkersEarningsResponse(workers=workers)
        except Exception:
            return WorkersEarningsResponse(workers=[])
    
    async def get_earnings_history(self, user_address: str) -> EarningsHistoryResponse:
        """
        Get earnings history for user.
        
        Args:
            user_address: User's wallet address
            
        Returns:
            Earnings history response
        """
        try:
            history_data = await self._get_earnings_history_data(user_address)
            
            history = [
                EarningHistoryRecord(
                    time=record.get("time", ""),
                    worker=record.get("worker", ""),
                    earning=record.get("earning", "0"),
                    wallet_address=record.get("wallet_address", ""),
                    hash=record.get("hash", ""),
                )
                for record in history_data
            ]
            
            return EarningsHistoryResponse(history=history)
        except Exception:
            return EarningsHistoryResponse(history=[])
    
    # Private helper methods
    
    async def _get_user_miners(
        self, user_address: str, search: Optional[str], page: int, page_size: int
    ) -> Dict[str, Any]:
        """Get user miners from storage."""
        # Sample data - in real implementation, query storage
        miners = [
            {
                "address": "5EewuHxax...",
                "status": "Active",
                "hashrate_1h": "320.05 Ph/s",
                "hashrate_24h": "320.05 Ph/s",
                "shares_1h": "66.78B",
                "shares_24h": "66.78B",
                "node": "node1",
                "stake": "500 TAO",
                "score": 98,
                "earning": "100 xxx",
            }
        ]
        
        # Apply search filter if provided
        if search:
            miners = [m for m in miners if search.lower() in m["address"].lower()]
        
        # Apply pagination
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_miners = miners[start_idx:end_idx]
        
        return {"total": len(miners), "miners": paginated_miners}
    
    async def _get_miner_data(self, address: str) -> Dict[str, Any]:
        """Get miner data from storage."""
        return {
            "status": "Active",
            "node": "node1",
            "hashrate_5m": "$0.00/Hour",
            "hashrate_1h": "$0.00/Hour",
            "hashrate_24h": "$0.00/Hour",
            "hashrate_max": "$0.00/Hour",
            "shares_5m": "3.19T sh",
            "shares_1h": "3.19T sh",
            "shares_24h": "3.19T sh",
            "shares_total": "3.19T sh",
        }
    
    async def _generate_hashrate_history(self) -> List[HashrateDataPoint]:
        """Generate sample hashrate history."""
        current_time = int(time.time())
        return [
            HashrateDataPoint(timestamp=current_time - 7200, hashrate=800.0),
            HashrateDataPoint(timestamp=current_time - 3600, hashrate=600.0),
            HashrateDataPoint(timestamp=current_time, hashrate=400.0),
        ]
    
    async def _generate_shares_history(self) -> List[SharesDataPoint]:
        """Generate sample shares history."""
        current_time = int(time.time())
        return [
            SharesDataPoint(timestamp=current_time - 7200, shares=400.0),
            SharesDataPoint(timestamp=current_time - 3600, shares=800.0),
            SharesDataPoint(timestamp=current_time, shares=600.0),
        ]
    
    async def _update_miner_node(self, address: str, node: str) -> None:
        """Update miner's node in storage."""
        # Implementation would update storage
        pass
    
    async def _get_user_earnings(self, user_address: str) -> Dict[str, str]:
        """Get user earnings data."""
        return {"today_rewards": "158.80 USD", "total_rewards": "158.80 USD"}
    
    async def _get_workers_earnings_data(self, user_address: str) -> List[Dict[str, Any]]:
        """Get workers earnings data."""
        return [
            {
                "address": "5EewuHxax...",
                "status": "Active",
                "node": "node1",
                "stake": "500 TAO",
                "score": 98,
                "earning": "100 xxx",
            }
        ]
    
    async def _get_earnings_history_data(self, user_address: str) -> List[Dict[str, Any]]:
        """Get earnings history data."""
        return [
            {
                "time": "2025/6/6 11:11",
                "worker": "5EewuHxax...",
                "earning": "200xxx",
                "wallet_address": "0x4c6924...11a0fa",
                "hash": "0x4c6924...11a0fa",
            }
        ]
