# TAO Hash API

TAO Hash API是一个基于FastAPI的Web API，为TAO Hash生态系统提供仪表盘功能、矿工管理、验证者管理和质押系统功能。

## 功能特性

- **仪表盘模块**: 提供算力、份额、质押和收益的综合概览
- **矿工管理**: 矿工列表、详细信息、节点切换和收益跟踪
- **验证者管理**: 验证者概览、质押操作、收益管理和钱包配置
- **质押系统**: 质押验证者、管理质押节点、领取收益和提现
- **账户认证**: Web3钱包认证和会话管理

## 安装和启动

### 1. 安装依赖

```bash
cd hash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -e .[api]
```

### 2. 启动API服务器

```bash
# 使用简单启动脚本
python start_api.py

# 或使用完整的API服务器
python api_server.py --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问API

- **API服务器**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

## API端点

### 认证端点

- `POST /api/v1/accounts/auth` - 钱包认证
- `POST /api/v1/accounts/select` - 选择账户
- `POST /api/v1/accounts/disconnect` - 断开连接

### 仪表盘端点

- `GET /api/v1/dashboard/` - 获取仪表盘数据
- `GET /api/v1/dashboard/hashrate-history` - 获取算力历史
- `GET /api/v1/dashboard/accounts` - 获取账户列表

### 矿工端点

- `GET /api/v1/miners/` - 获取矿工列表
- `GET /api/v1/miners/{address}` - 获取矿工详情
- `GET /api/v1/miners/nodes/available` - 获取可用节点
- `POST /api/v1/miners/{address}/change-node` - 切换矿工节点
- `GET /api/v1/miners/earnings/overview` - 获取收益概览
- `GET /api/v1/miners/earnings/workers` - 获取工作者收益
- `GET /api/v1/miners/earnings/history` - 获取收益历史

### 验证者端点

- `GET /api/v1/validators/overview` - 获取验证者概览
- `POST /api/v1/validators/stake` - 质押操作
- `POST /api/v1/validators/unstake` - 解押操作
- `GET /api/v1/validators/{address}` - 获取验证者详情
- `GET /api/v1/validators/earnings/overview` - 获取收益概览
- `GET /api/v1/validators/earnings/chart` - 获取收益图表
- `GET /api/v1/validators/earnings/history` - 获取收益历史
- `GET /api/v1/validators/wallets` - 获取钱包列表
- `POST /api/v1/validators/wallets` - 添加钱包
- `PUT /api/v1/validators/wallets` - 编辑钱包

### 质押端点

- `GET /api/v1/staking/validators` - 获取可质押验证者
- `POST /api/v1/staking/stake` - 质押到验证者
- `GET /api/v1/staking/my-nodes` - 获取我的质押节点
- `POST /api/v1/staking/claim` - 领取收益
- `POST /api/v1/staking/unstake` - 解押
- `POST /api/v1/staking/withdraw` - 提现

## 认证

API使用Web3钱包签名进行认证。认证流程：

1. 客户端使用钱包对消息进行签名
2. 发送签名到 `/api/v1/accounts/auth` 端点
3. 服务器验证签名并返回JWT令牌
4. 后续请求在Authorization头中携带Bearer令牌

### 认证示例

```bash
# 1. 钱包认证
curl -X POST "http://localhost:8000/api/v1/accounts/auth" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "5EewuHxax...",
    "signature": "0x...",
    "message": "Sign this message to authenticate",
    "source": "polkadot"
  }'

# 2. 使用令牌访问受保护端点
curl -X GET "http://localhost:8000/api/v1/dashboard/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 数据模型

API使用Pydantic模型进行数据验证和序列化。主要模型包括：

- **DashboardResponse**: 仪表盘数据
- **MinerInfo**: 矿工信息
- **ValidatorOverviewResponse**: 验证者概览
- **StakableValidator**: 可质押验证者
- **WalletAuthRequest**: 钱包认证请求

详细的数据模型定义请参考API文档。

## 配置

API服务器支持以下配置选项：

- `--host`: 绑定主机地址（默认: 0.0.0.0）
- `--port`: 绑定端口（默认: 8000）
- `--reload`: 启用自动重载（开发模式）
- `--log-level`: 日志级别（debug, info, warning, error）

## 开发

### 项目结构

```
hash/
├── taohash/
│   └── api/
│       ├── models/          # Pydantic数据模型
│       ├── services/        # 业务逻辑服务
│       ├── routes/          # FastAPI路由
│       └── utils/           # 工具函数
├── api_server.py           # 主API服务器
├── start_api.py           # 简单启动脚本
└── API_README.md          # 本文档
```

### 添加新端点

1. 在 `models/` 中定义数据模型
2. 在 `services/` 中实现业务逻辑
3. 在 `routes/` 中添加FastAPI路由
4. 更新相应的 `__init__.py` 文件

## 故障排除

### 常见问题

1. **依赖冲突**: 确保使用虚拟环境并安装正确的依赖版本
2. **端口占用**: 更改端口或停止占用端口的进程
3. **认证失败**: 检查钱包签名和消息格式

### 日志

API服务器会输出详细的日志信息，包括：
- 请求/响应日志
- 错误信息
- 性能指标

## 许可证

本项目遵循与TAO Hash主项目相同的许可证。



```bash
docker run --rm -d \
  --name dogehash-api \
  -p 8000:8000 \
  coinflow/dogehash-api

```