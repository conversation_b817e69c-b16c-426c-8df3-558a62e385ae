#!/bin/bash

# TAO Hash API 开发环境启动脚本

set -e

echo "🚀 启动 TAO Hash API 开发环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 创建必要的目录
mkdir -p ../../../hash/logs
mkdir -p ../../../hash/data

# 构建并启动服务
echo "📦 构建 Docker 镜像..."
docker-compose build

echo "🔄 启动服务..."
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

echo ""
echo "✅ 开发环境启动完成！"
echo ""
echo "🌐 API 服务地址: http://localhost:8000"
echo "📚 API 文档地址: http://localhost:8000/docs"
echo "🔍 Redis 地址: localhost:6379"
echo ""
echo "📝 常用命令:"
echo "  查看日志: docker-compose logs -f taohash-api"
echo "  重启服务: docker-compose restart taohash-api"
echo "  停止服务: docker-compose down"
echo "  进入容器: docker-compose exec taohash-api bash"
echo ""
echo "🔥 代码热重载已启用，修改代码后服务会自动重启"
