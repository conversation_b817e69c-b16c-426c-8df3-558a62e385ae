# TAO Hash API Docker 开发环境

这个目录包含了用于 TAO Hash API 开发环境的 Docker 配置文件。

## 文件说明

- `Dockerfile.dev` - 开发环境专用的 Docker 镜像定义
- `Dockerfile` - 生产环境的 Docker 镜像定义
- `docker-compose.yml` - Docker Compose 配置文件
- `start-dev.sh` - 开发环境启动脚本
- `.dockerignore` - Docker 构建忽略文件

## 快速开始

### 1. 启动开发环境

```bash
# 使用启动脚本（推荐）
./start-dev.sh

# 或者手动启动
docker-compose up -d
```

### 2. 访问服务

- **API 服务**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **Redis**: localhost:6379

### 3. 查看日志

```bash
# 查看 API 服务日志
docker-compose logs -f taohash-api

# 查看所有服务日志
docker-compose logs -f
```

## 开发特性

### 🔥 热重载

代码修改后服务会自动重启，无需手动重新构建镜像。以下目录被挂载到容器中：

- `hash/taohash/` - API 源代码
- `hash/api_server.py` - API 服务器主文件
- `hash/start_api.py` - 启动脚本
- `hash/logs/` - 日志目录
- `hash/data/` - 数据目录

### 📦 依赖缓存

Docker 镜像使用分层构建，`pyproject.toml` 文件单独复制并安装依赖，只有当依赖文件改变时才会重新安装依赖。

### 🏥 健康检查

容器包含健康检查，可以监控服务状态：

```bash
docker-compose ps
```

## 常用命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启 API 服务
docker-compose restart taohash-api

# 重新构建镜像
docker-compose build

# 进入容器
docker-compose exec taohash-api bash

# 查看容器状态
docker-compose ps

# 清理所有容器和卷
docker-compose down -v
```

## 环境变量

可以通过修改 `docker-compose.yml` 中的环境变量来配置服务：

- `PYTHONPATH=/app` - Python 路径
- `ENVIRONMENT=development` - 运行环境
- `LOG_LEVEL=debug` - 日志级别

## 故障排除

### 端口冲突

如果 8000 或 6379 端口被占用，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "8001:8000"  # 将本地端口改为 8001
```

### 权限问题

如果遇到文件权限问题，确保当前用户有权限访问项目目录：

```bash
sudo chown -R $USER:$USER .
```

### 依赖问题

如果依赖安装失败，可以清理并重新构建：

```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## 生产环境

生产环境请使用 `Dockerfile` 而不是 `Dockerfile.dev`，并相应调整 docker-compose 配置。
