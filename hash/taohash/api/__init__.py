"""
TAO Hash API Module

This module provides a comprehensive Web API for the TAO Hash ecosystem,
including dashboard, miner management, validator management, and staking functionality.
"""

from typing import Optional
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

__version__ = "0.1.0"


def create_app(config: Optional[dict] = None) -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured FastAPI application instance
    """
    # Create FastAPI application
    app = FastAPI(
        title="TAO Hash API",
        description="Web API for the TAO Hash ecosystem providing dashboard functionality, miner management, validator management, and staking system capabilities.",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, specify allowed origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Import and include routers
    from .routes.dashboard import router as dashboard_router
    from .routes.miners import router as miners_router
    from .routes.validators import router as validators_router
    from .routes.staking import router as staking_router
    from .routes.accounts import router as accounts_router

    # Include routers with API prefix
    app.include_router(accounts_router, prefix="/api/v1")
    app.include_router(dashboard_router, prefix="/api/v1")
    app.include_router(miners_router, prefix="/api/v1")
    app.include_router(validators_router, prefix="/api/v1")
    app.include_router(staking_router, prefix="/api/v1")

    # Also include routers without prefix for backward compatibility with tests
    app.include_router(accounts_router, prefix="", tags=["Accounts (Legacy)"])
    app.include_router(dashboard_router, prefix="", tags=["Dashboard (Legacy)"])
    app.include_router(miners_router, prefix="", tags=["Miners (Legacy)"])
    app.include_router(validators_router, prefix="", tags=["Validators (Legacy)"])
    app.include_router(staking_router, prefix="", tags=["Staking (Legacy)"])

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy", "service": "TAO Hash API"}

    # API version endpoint
    @app.get("/version")
    async def get_version():
        """Get API version."""
        return {"version": "1.0.0", "service": "TAO Hash API"}

    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Welcome to TAO Hash API",
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/health",
        }

    return app
