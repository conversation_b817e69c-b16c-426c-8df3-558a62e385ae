# 开发环境专用Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV ENVIRONMENT=development

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 升级pip
RUN pip install --no-cache-dir --upgrade pip

# 首先复制pyproject.toml和taohash目录以利用Docker层缓存
COPY hash/pyproject.toml /app/
COPY hash/taohash /app/taohash

# 安装Python依赖（这一层会被缓存，除非pyproject.toml或taohash目录改变）
RUN pip install --no-cache-dir -e ".[api,dev]"

# 安装开发工具
RUN pip install --no-cache-dir watchdog

# 创建必要的目录
RUN mkdir -p /app/taohash/api /app/logs /app/data

# 暴露端口
EXPOSE 8000

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令（开发环境使用热重载）
CMD ["python", "start_api.py"]
