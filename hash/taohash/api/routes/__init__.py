"""
API Routes

This module contains FastAPI route definitions for the TAO Hash API.
"""

from .dashboard import router as dashboard_router
from .miners import router as miners_router
from .validators import router as validators_router
from .staking import router as staking_router
from .accounts import router as accounts_router

__all__ = [
    "dashboard_router",
    "miners_router",
    "validators_router", 
    "staking_router",
    "accounts_router",
]
