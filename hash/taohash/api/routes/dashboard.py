"""
Dashboard Routes

FastAPI routes for dashboard-related endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional
from taohash.api.services import DashboardService
from taohash.api.models.dashboard import (
    DashboardResponse,
    HashrateHistoryResponse,
    AccountsResponse,
)
from taohash.api.utils.auth import get_current_user

router = APIRouter(prefix="/dashboard", tags=["Dashboard"])


@router.get("/", response_model=DashboardResponse)
async def get_dashboard(
    current_user: dict = Depends(get_current_user),
    service: DashboardService = Depends(lambda: DashboardService()),
):
    """
    获取仪表盘数据

    Returns:
        Dashboard data including hashrate, shares, staking, and earnings information
    """
    try:
        user_address = current_user["address"]
        return await service.get_dashboard_data(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard data: {str(e)}")


@router.get("/overview", response_model=DashboardResponse)
async def get_dashboard_overview(
    current_user: dict = Depends(get_current_user),
    service: DashboardService = Depends(lambda: DashboardService()),
):
    """
    获取仪表盘概览数据 (别名端点)

    Returns:
        Dashboard data including hashrate, shares, staking, and earnings information
    """
    try:
        user_address = current_user["address"]
        return await service.get_dashboard_data(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard data: {str(e)}")


@router.get("/hashrate-history", response_model=HashrateHistoryResponse)
async def get_hashrate_history(
    range_param: str = Query("24h", alias="range", description="时间范围 (24h, 7d, 30d)"),
    current_user: dict = Depends(get_current_user),
    service: DashboardService = Depends(lambda: DashboardService()),
):
    """
    获取算力历史数据
    
    Args:
        range_param: Time range (24h, 7d, 30d)
        
    Returns:
        Hashrate history data with timestamps and values
    """
    try:
        return await service.get_hashrate_history(range_param)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get hashrate history: {str(e)}")


@router.get("/accounts", response_model=AccountsResponse)
async def get_accounts(
    current_user: dict = Depends(get_current_user),
    service: DashboardService = Depends(lambda: DashboardService()),
):
    """
    获取用户账户列表
    
    Returns:
        List of user accounts with their sources and selection status
    """
    try:
        user_address = current_user["address"]
        return await service.get_accounts(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get accounts: {str(e)}")
