"""
Accounts Routes

FastAPI routes for account and authentication-related endpoints.
"""

import time
import secrets
from fastapi import APIRouter, Depends, HTTPException
from taohash.api.services import AccountService
from taohash.api.models.accounts import (
    WalletAuthRequest,
    WalletAuthResponse,
    AccountSelectRequest,
    AccountSelectResponse,
    DisconnectResponse,
    NonceRequest,
    NonceResponse,
)
from taohash.api.utils.auth import get_current_user
from taohash.api.dependencies import get_account_service

router = APIRouter(prefix="/accounts", tags=["Accounts"])


@router.post("/nonce", response_model=NonceResponse)
async def get_nonce(
    request: NonceRequest,
    service: AccountService = Depends(get_account_service),
):
    """
    获取登录随机数
    
    Args:
        request: Nonce request with wallet address
        
    Returns:
        Nonce string for signing
    """
    try:
        nonce = await service.generate_nonce(request.address)
        return NonceResponse(
            success=True,
            nonce=nonce,
            message="请使用您的钱包签名此消息以完成登录"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate nonce: {str(e)}")


@router.post("/auth", response_model=WalletAuthResponse)
async def authenticate_wallet(
    request: WalletAuthRequest,
    service: AccountService = Depends(get_account_service),
):
    """
    钱包认证
    
    Args:
        request: Wallet authentication request with address, signature, message, and source
        
    Returns:
        Authentication result with token if successful
    """
    try:
        return await service.authenticate_wallet(
            request.address, request.signature, request.message, request.source
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")


@router.post("/select", response_model=AccountSelectResponse)
async def select_account(
    request: AccountSelectRequest,
    current_user: dict = Depends(get_current_user),
    service: AccountService = Depends(get_account_service),
):
    """
    选择账户
    
    Args:
        request: Account selection request with address and source
        
    Returns:
        Result of the account selection operation
    """
    try:
        user_address = current_user["address"]
        return await service.select_account(user_address, request.address, request.source)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to select account: {str(e)}")


@router.post("/disconnect", response_model=DisconnectResponse)
async def disconnect(
    current_user: dict = Depends(get_current_user),
    service: AccountService = Depends(get_account_service),
):
    """
    断开连接
    
    Returns:
        Result of the disconnect operation
    """
    try:
        user_address = current_user["address"]
        return await service.disconnect(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to disconnect: {str(e)}")
