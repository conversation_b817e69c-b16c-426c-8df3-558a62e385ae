"""
Staking Routes

FastAPI routes for staking-related endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from typing import Optional
from taohash.api.services import StakingService
from taohash.api.models.staking import (
    StakableValidatorsResponse,
    StakeRequest,
    StakeResponse,
    MyStakedNodesResponse,
    ClaimRequest,
    ClaimResponse,
    UnstakeRequest,
    UnstakeResponse,
    WithdrawRequest,
    WithdrawResponse,
)
from taohash.api.utils.auth import get_current_user

router = APIRouter(prefix="/staking", tags=["Staking"])


@router.get("/validators", response_model=StakableValidatorsResponse)
async def get_stakable_validators(
    search: Optional[str] = Query(None, description="搜索验证者地址"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    node: Optional[str] = Query(None, description="节点筛选"),
    current_user: dict = Depends(get_current_user),
    service: StakingService = Depends(lambda: StakingService()),
):
    """
    获取可质押验证者列表
    
    Args:
        search: Optional search term for validator address
        page: Page number (starting from 1)
        page_size: Number of validators per page (1-100)
        node: Optional node filter
        
    Returns:
        Paginated list of stakable validators with their performance data
    """
    try:
        return await service.get_stakable_validators(search, page, page_size, node)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stakable validators: {str(e)}")


@router.post("/stake", response_model=StakeResponse)
async def stake(
    request: StakeRequest,
    current_user: dict = Depends(get_current_user),
    service: StakingService = Depends(lambda: StakingService()),
):
    """
    质押到验证者
    
    Args:
        request: Stake request with validator address and amount
        
    Returns:
        Result of the staking operation
    """
    try:
        user_address = current_user["address"]
        return await service.stake(user_address, request.address, request.amount)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stake: {str(e)}")


@router.get("/my-nodes", response_model=MyStakedNodesResponse)
async def get_my_staked_nodes(
    status: Optional[str] = Query(None, description="状态筛选 (Staking/Locked/Unlocked)"),
    current_user: dict = Depends(get_current_user),
    service: StakingService = Depends(lambda: StakingService()),
):
    """
    获取我的质押节点
    
    Args:
        status: Optional status filter (Staking/Locked/Unlocked)
        
    Returns:
        List of user's staked nodes with their status and available actions
    """
    try:
        user_address = current_user["address"]
        return await service.get_my_staked_nodes(user_address, status)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get staked nodes: {str(e)}")


@router.post("/claim", response_model=ClaimResponse)
async def claim_rewards(
    request: ClaimRequest,
    current_user: dict = Depends(get_current_user),
    service: StakingService = Depends(lambda: StakingService()),
):
    """
    领取收益
    
    Args:
        request: Claim request with node address
        
    Returns:
        Result of the claim operation
    """
    try:
        user_address = current_user["address"]
        return await service.claim_rewards(user_address, request.address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to claim rewards: {str(e)}")


@router.post("/unstake", response_model=UnstakeResponse)
async def unstake(
    request: UnstakeRequest,
    current_user: dict = Depends(get_current_user),
    service: StakingService = Depends(lambda: StakingService()),
):
    """
    解押
    
    Args:
        request: Unstake request with node address
        
    Returns:
        Result of the unstake operation
    """
    try:
        user_address = current_user["address"]
        return await service.unstake(user_address, request.address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to unstake: {str(e)}")


@router.post("/withdraw", response_model=WithdrawResponse)
async def withdraw(
    request: WithdrawRequest,
    current_user: dict = Depends(get_current_user),
    service: StakingService = Depends(lambda: StakingService()),
):
    """
    提现
    
    Args:
        request: Withdraw request with node address
        
    Returns:
        Result of the withdraw operation
    """
    try:
        user_address = current_user["address"]
        return await service.withdraw(user_address, request.address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to withdraw: {str(e)}")
