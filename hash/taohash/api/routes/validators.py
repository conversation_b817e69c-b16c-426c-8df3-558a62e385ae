"""
Validators Routes

FastAPI routes for validator-related endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from typing import Optional
from taohash.api.services import ValidatorService
from taohash.api.models.validators import (
    ValidatorOverviewResponse,
    StakeRequest,
    StakeResponse,
    UnstakeRequest,
    ValidatorDetailResponse,
    ValidatorEarningsOverviewResponse,
    ValidatorEarningsChartResponse,
    ValidatorEarningsHistoryResponse,
    ValidatorWalletsResponse,
    AddWalletRequest,
    EditWalletRequest,
    WalletOperationResponse,
)
from taohash.api.utils.auth import get_current_user

router = APIRouter(prefix="/validators", tags=["Validators"])


@router.get("/overview", response_model=ValidatorOverviewResponse)
async def get_validator_overview(
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    获取验证者概览
    
    Returns:
        Validator overview including staking status, node info, and miners
    """
    try:
        user_address = current_user["address"]
        return await service.get_validator_overview(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get validator overview: {str(e)}")


@router.post("/stake", response_model=StakeResponse)
async def stake(
    request: StakeRequest,
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    质押操作
    
    Args:
        request: Stake request with amount
        
    Returns:
        Result of the staking operation
    """
    try:
        user_address = current_user["address"]
        return await service.stake(user_address, request.amount)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stake: {str(e)}")


@router.post("/unstake", response_model=StakeResponse)
async def unstake(
    request: UnstakeRequest,
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    解押操作
    
    Args:
        request: Unstake request with amount
        
    Returns:
        Result of the unstaking operation
    """
    try:
        user_address = current_user["address"]
        return await service.unstake(user_address, request.amount)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to unstake: {str(e)}")


@router.get("/{address}", response_model=ValidatorDetailResponse)
async def get_validator_detail(
    address: str = Path(..., description="验证者地址"),
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    获取验证者详细信息
    
    Args:
        address: Validator address
        
    Returns:
        Detailed validator information including hashrate and shares history
    """
    try:
        return await service.get_validator_detail(address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get validator detail: {str(e)}")


@router.get("/earnings/overview", response_model=ValidatorEarningsOverviewResponse)
async def get_earnings_overview(
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    获取验证者收益概览
    
    Returns:
        Overview of validator earnings across different cryptocurrencies
    """
    try:
        user_address = current_user["address"]
        return await service.get_earnings_overview(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get earnings overview: {str(e)}")


@router.get("/earnings/chart", response_model=ValidatorEarningsChartResponse)
async def get_earnings_chart(
    range_param: str = Query("24h", alias="range", description="时间范围 (24h, 7d, 30d)"),
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    获取验证者收益图表数据
    
    Args:
        range_param: Time range (24h, 7d, 30d)
        
    Returns:
        Chart data for validator earnings over time
    """
    try:
        return await service.get_earnings_chart(range_param)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get earnings chart: {str(e)}")


@router.get("/earnings/history", response_model=ValidatorEarningsHistoryResponse)
async def get_earnings_history(
    type_filter: Optional[str] = Query(None, description="币种筛选"),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    获取验证者收益历史
    
    Args:
        type_filter: Optional coin type filter
        start_time: Optional start time
        end_time: Optional end time
        
    Returns:
        Historical earnings records for the validator
    """
    try:
        user_address = current_user["address"]
        return await service.get_earnings_history(user_address, type_filter, start_time, end_time)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get earnings history: {str(e)}")


@router.get("/wallets", response_model=ValidatorWalletsResponse)
async def get_wallets(
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    获取验证者钱包列表
    
    Returns:
        List of validator wallets for different cryptocurrencies
    """
    try:
        user_address = current_user["address"]
        return await service.get_wallets(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get wallets: {str(e)}")


@router.post("/wallets", response_model=WalletOperationResponse)
async def add_wallet(
    request: AddWalletRequest,
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    添加钱包
    
    Args:
        request: Add wallet request with coin, wallet_name, and address
        
    Returns:
        Result of the wallet addition operation
    """
    try:
        user_address = current_user["address"]
        return await service.add_wallet(
            user_address, request.coin, request.wallet_name, request.address
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add wallet: {str(e)}")


@router.put("/wallets", response_model=WalletOperationResponse)
async def edit_wallet(
    request: EditWalletRequest,
    current_user: dict = Depends(get_current_user),
    service: ValidatorService = Depends(lambda: ValidatorService()),
):
    """
    编辑钱包
    
    Args:
        request: Edit wallet request with coin and optional new wallet_name and address
        
    Returns:
        Result of the wallet edit operation
    """
    try:
        user_address = current_user["address"]
        return await service.edit_wallet(
            user_address, request.coin, request.wallet_name, request.address
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to edit wallet: {str(e)}")
