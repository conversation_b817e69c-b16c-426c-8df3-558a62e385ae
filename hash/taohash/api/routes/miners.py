"""
Miners Routes

FastAPI routes for miner-related endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from typing import Optional
from taohash.api.services import MinerService
from taohash.api.models.miners import (
    MinerListResponse,
    MinerDetailResponse,
    NodesResponse,
    ChangeNodeRequest,
    ChangeNodeResponse,
    EarningsOverviewResponse,
    WorkersEarningsResponse,
    EarningsHistoryResponse,
)
from taohash.api.utils.auth import get_current_user

router = APIRouter(prefix="/miners", tags=["Miners"])


@router.get("/", response_model=MinerListResponse)
async def get_miners(
    search: Optional[str] = Query(None, description="搜索矿工地址"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    current_user: dict = Depends(get_current_user),
    service: MinerService = Depends(lambda: MinerService(init_miner=False)),
):
    """
    获取矿工列表
    
    Args:
        search: Optional search term for miner address
        page: Page number (starting from 1)
        page_size: Number of miners per page (1-100)
        
    Returns:
        Paginated list of miners with their status and performance data
    """
    try:
        user_address = current_user["address"]
        return await service.get_miner_list(user_address, search, page, page_size)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get miners: {str(e)}")


@router.get("/{address}", response_model=MinerDetailResponse)
async def get_miner_detail(
    address: str = Path(..., description="矿工地址"),
    current_user: dict = Depends(get_current_user),
    service: MinerService = Depends(lambda: MinerService(init_miner=False)),
):
    """
    获取矿工详细信息
    
    Args:
        address: Miner address
        
    Returns:
        Detailed miner information including hashrate and shares history
    """
    try:
        return await service.get_miner_detail(address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get miner detail: {str(e)}")


@router.get("/nodes/available", response_model=NodesResponse)
async def get_available_nodes(
    current_user: dict = Depends(get_current_user),
    service: MinerService = Depends(lambda: MinerService(init_miner=False)),
):
    """
    获取可用节点列表
    
    Returns:
        List of available mining nodes with their URLs
    """
    try:
        return await service.get_available_nodes()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get available nodes: {str(e)}")


@router.post("/{address}/change-node", response_model=ChangeNodeResponse)
async def change_miner_node(
    address: str = Path(..., description="矿工地址"),
    request: ChangeNodeRequest = ...,
    current_user: dict = Depends(get_current_user),
    service: MinerService = Depends(lambda: MinerService(init_miner=False)),
):
    """
    切换矿工节点
    
    Args:
        address: Miner address
        request: Node change request with from_node and to_node
        
    Returns:
        Result of the node change operation
    """
    try:
        return await service.change_miner_node(address, request.from_node, request.to_node)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to change miner node: {str(e)}")


@router.get("/earnings/overview", response_model=EarningsOverviewResponse)
async def get_earnings_overview(
    current_user: dict = Depends(get_current_user),
    service: MinerService = Depends(lambda: MinerService(init_miner=False)),
):
    """
    获取收益概览
    
    Returns:
        Overview of today's and total earnings
    """
    try:
        user_address = current_user["address"]
        return await service.get_earnings_overview(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get earnings overview: {str(e)}")


@router.get("/earnings/workers", response_model=WorkersEarningsResponse)
async def get_workers_earnings(
    current_user: dict = Depends(get_current_user),
    service: MinerService = Depends(lambda: MinerService(init_miner=False)),
):
    """
    获取工作者收益
    
    Returns:
        Earnings data for all workers
    """
    try:
        user_address = current_user["address"]
        return await service.get_workers_earnings(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get workers earnings: {str(e)}")


@router.get("/earnings/history", response_model=EarningsHistoryResponse)
async def get_earnings_history(
    current_user: dict = Depends(get_current_user),
    service: MinerService = Depends(lambda: MinerService(init_miner=False)),
):
    """
    获取收益历史
    
    Returns:
        Historical earnings records
    """
    try:
        user_address = current_user["address"]
        return await service.get_earnings_history(user_address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get earnings history: {str(e)}")
