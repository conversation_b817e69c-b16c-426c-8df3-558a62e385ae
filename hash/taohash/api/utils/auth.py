"""
Authentication Utilities

Utilities for Web3 wallet authentication and session management.
"""

from typing import Dict, Any, Optional
from fastapi import HTT<PERSON>Exception, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from taohash.api.services import AccountService
from taohash.api.dependencies import get_account_service

# Security scheme for Bearer token
security = HTTPBearer()


async def verify_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    account_service: AccountService = Depends(get_account_service),
) -> Optional[Dict[str, Any]]:
    """
    Verify authentication token.
    
    Args:
        credentials: HTTP authorization credentials
        account_service: Account service instance
        
    Returns:
        Session data if token is valid
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    try:
        token = credentials.credentials
        session = await account_service.verify_session(token)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return session
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(
    session: Dict[str, Any] = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Get current authenticated user.
    
    Args:
        session: Session data from token verification
        
    Returns:
        Current user information
    """
    return {
        "address": session["address"],
        "source": session["source"],
        "created_at": session["created_at"],
    }
