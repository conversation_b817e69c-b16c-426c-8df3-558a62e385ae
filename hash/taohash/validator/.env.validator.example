#=========================================================================================
# Bittensor variables

NETUID=14                      # Netuid 14 for mainnet finney
SUBTENSOR_NETWORK=finney       # Mainnet network
BT_WALLET_NAME="WALLET NAME"   
BT_WALLET_HOTKEY="WALLET HOTKEY NAME"
# BT_WALLET_PATH=/custom/path/to/wallets

#=========================================================================================
# Subnet Proxy Configuration (provided by subnet maintainers)

# API endpoint for retrieving miner statistics
SUBNET_PROXY_API_URL="http://proxy.taohash.com:8888"  # Example URL - get actual URL from subnet maintainers 
SUBNET_PROXY_API_TOKEN="your-api-token-here"          # Get token from subnet maintainers 

#=========================================================================================
# Recovery Configuration

RECOVERY_FILE_PATH=~/.bittensor/data/taohash/validator  # Path to save validator state
RECOVERY_FILE_NAME=validator_state.json                 # Filename for validator state

#=========================================================================================
# For Subnet Pool

# PROXY_DOMAIN="192.168.100.66"
# PROXY_PORT="3331"
# PROXY_HIGH_DIFF_PORT="3332"
# PROXY_API_PORT="8888"
# PROXY_USERNAME="latent-to"
# PROXY_PASSWORD="x"
# PROXY_API_TOKEN="proxy_token"