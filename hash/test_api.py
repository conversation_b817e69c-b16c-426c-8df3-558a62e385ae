#!/usr/bin/env python3
"""
TAO Hash API Test Script

Simple test script to verify API endpoints are working correctly.
"""

import requests
import json
import sys
import time

BASE_URL = "http://localhost:8000"

def test_endpoint(method, endpoint, data=None, headers=None, expected_status=200):
    """Test a single API endpoint."""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        if response.status_code == expected_status:
            print(f"✅ {method} {endpoint} - Status: {response.status_code}")
            return True
        else:
            print(f"❌ {method} {endpoint} - Expected: {expected_status}, Got: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {method} {endpoint} - Connection failed (is server running?)")
        return False
    except Exception as e:
        print(f"❌ {method} {endpoint} - Error: {str(e)}")
        return False

def main():
    """Run API tests."""
    print("🚀 Testing TAO Hash API")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server health check failed")
            sys.exit(1)
    except:
        print("❌ Cannot connect to API server")
        print("   Please start the server with: python start_api.py")
        sys.exit(1)
    
    print("✅ Server is running")
    print()
    
    # Test basic endpoints
    tests = [
        ("GET", "/", None, None, 200),
        ("GET", "/health", None, None, 200),
        ("GET", "/version", None, None, 200),
    ]
    
    # Test API endpoints (these will return 403 without auth, which is expected)
    api_tests = [
        ("GET", "/api/v1/dashboard/", None, None, 403),
        ("GET", "/api/v1/dashboard/hashrate-history", None, None, 403),
        ("GET", "/api/v1/dashboard/accounts", None, None, 403),
        ("GET", "/api/v1/miners/", None, None, 403),
        ("GET", "/api/v1/miners/nodes/available", None, None, 403),
        ("GET", "/api/v1/miners/earnings/overview", None, None, 403),
        ("GET", "/api/v1/validators/overview", None, None, 403),
        ("GET", "/api/v1/staking/validators", None, None, 403),
        ("GET", "/api/v1/staking/my-nodes", None, None, 403),
    ]

    # Test authentication endpoints
    auth_tests = [
        ("POST", "/api/v1/accounts/auth", {}, None, 422),  # Should return validation error for empty data
        ("POST", "/api/v1/accounts/select", {}, None, 403),  # Requires auth
        ("POST", "/api/v1/accounts/disconnect", {}, None, 403),  # Requires auth
    ]
    
    passed = 0
    total = 0
    
    print("Testing basic endpoints:")
    for method, endpoint, data, headers, expected in tests:
        total += 1
        if test_endpoint(method, endpoint, data, headers, expected):
            passed += 1
    
    print("\nTesting API endpoints (expecting 403 Forbidden):")
    for method, endpoint, data, headers, expected in api_tests:
        total += 1
        if test_endpoint(method, endpoint, data, headers, expected):
            passed += 1

    print("\nTesting authentication endpoints:")
    for method, endpoint, data, headers, expected in auth_tests:
        total += 1
        if test_endpoint(method, endpoint, data, headers, expected):
            passed += 1
    
    print()
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! API is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
