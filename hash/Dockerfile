# TAO Hash Mining Rental Subnet
FROM python:3.11-slim

# 构建参数
ARG TAOHASH_MODE=default

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    git \
    curl \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY pyproject.toml LICENSE README.md ./
COPY taohash/ ./taohash/

# 安装Python依赖
RUN pip install --no-cache-dir .

# 创建非root用户
RUN useradd -m -u 1000 taohash && chown -R taohash:taohash /app
USER taohash

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV TAOHASH_MODE=${TAOHASH_MODE}

# 创建启动脚本
USER root
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
case "${TAOHASH_MODE}" in\n\
  "miner")\n\
    echo "Starting TAO Hash Miner..."\n\
    exec python -m taohash.miner\n\
    ;;\n\
  "validator")\n\
    echo "Starting TAO Hash Validator..."\n\
    exec python -m taohash.validator\n\
    ;;\n\
  *)\n\
    echo "Starting TAO Hash (default mode)..."\n\
    exec python -m taohash\n\
    ;;\n\
esac' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

USER taohash

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# 默认命令
ENTRYPOINT ["/app/entrypoint.sh"]