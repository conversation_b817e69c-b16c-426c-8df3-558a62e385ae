# TAO Hash API 实现方案

## 项目概述

本文档描述了在TAO Hash项目的hash目录中创建独立API模块的实现方案，该API将提供仪表盘、矿工管理、验证者管理、质押系统等功能，充分利用现有的TAO Hash基础设施。

### 实现方式

本方案采用在hash目录中创建独立API模块的方式，与现有的proxy系统完全分离：

- **proxy系统**: 专注于挖矿代理和数据收集
- **hash API**: 专注于TAO Hash生态系统的完整Web API服务

### 目标

- 实现API文档中定义的所有功能
- 充分利用现有的TAO Hash核心组件
- 提供高性能、可扩展的Web API服务
- 保持与现有生态系统的良好集成
- 创建独立可部署的API服务

## 技术架构

### 技术栈

- **Web框架**: FastAPI (异步、高性能)
- **数据存储**: Redis + JSON (复用现有存储系统)
- **认证**: Web3钱包认证 (Bittensor Wallet)
- **核心组件**: 复用现有的TAO Hash核心模块
- **部署**: Docker + Docker Compose

### 架构设计

```
用户请求 → FastAPI路由 → 服务层 → TAO Hash核心组件 → 存储系统(Redis/JSON)
                ↓
            返回JSON响应
```

### 与现有系统的关系

- **独立部署**: hash API作为独立服务运行，不依赖proxy系统
- **数据互通**: 可以从proxy获取挖矿统计数据，但主要使用自己的存储
- **功能互补**: proxy专注挖矿代理，hash API提供完整的生态API

## 实现计划

### 阶段1: 基础架构搭建

#### 1.1 更新项目依赖
```toml
# hash/pyproject.toml 添加API依赖
[project.optional-dependencies]
api = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "pydantic==2.5.0",
    "python-multipart==0.0.6",
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "web3==6.15.1",
    "eth-account==0.9.0",
    "substrate-interface==1.7.9",
    "scalecodec==1.2.11"
]
```

**依赖说明：**
- `fastapi` + `uvicorn`: Web框架和ASGI服务器
- `pydantic`: 数据模型和验证
- `python-jose`: JWT处理
- `web3` + `eth-account`: Web3钱包支持
- `substrate-interface` + `scalecodec`: Substrate/Polkadot钱包支持

#### 1.2 创建API模块结构
```
hash/
├── taohash/
│   ├── api/                    # 新增API模块
│   │   ├── __init__.py
│   │   ├── server.py           # FastAPI应用
│   │   ├── models/             # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── dashboard.py
│   │   │   ├── miners.py
│   │   │   ├── validators.py
│   │   │   ├── staking.py
│   │   │   └── accounts.py
│   │   ├── routes/             # API路由
│   │   │   ├── __init__.py
│   │   │   ├── dashboard.py
│   │   │   ├── miners.py
│   │   │   ├── validators.py
│   │   │   ├── staking.py
│   │   │   └── accounts.py
│   │   ├── services/           # 业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── dashboard_service.py
│   │   │   ├── miner_service.py
│   │   │   ├── validator_service.py
│   │   │   ├── staking_service.py
│   │   │   └── account_service.py
│   │   └── utils/              # 工具函数
│   │       ├── __init__.py
│   │       ├── auth.py
│   │       ├── data_aggregator.py
│   │       └── bittensor_client.py
├── api_server.py               # API服务启动脚本
└── requirements-api.txt         # API相关依赖
```

### 阶段2: 数据模型设计

#### 2.1 仪表盘模型
```python
# hash/taohash/api/models/dashboard.py
from pydantic import BaseModel
from typing import List, Optional

class DashboardResponse(BaseModel):
    hashrate_24h: str
    shares_24h: str
    hashrate_1h: str
    shares_1h: str
    total_staked: str
    tao_earnings: str
    doge_earnings: str
    ltc_earnings: str
    active_miners: int

class HashrateHistoryResponse(BaseModel):
    range: str
    unit: str
    data: List[dict]

class AccountInfo(BaseModel):
    name: str
    source: str
    address: str
    selected: Optional[bool] = False
```

#### 2.2 矿工模型
```python
# hash/taohash/api/models/miners.py
from pydantic import BaseModel
from typing import List, Optional

class MinerInfo(BaseModel):
    address: str
    status: str
    hashrate_1h: str
    hashrate_24h: str
    shares_1h: str
    shares_24h: str
    node: str
    stake: str
    score: int
    earning: str

class MinerListResponse(BaseModel):
    total: int
    miners: List[MinerInfo]

class MinerDetailResponse(BaseModel):
    address: str
    status: str
    node: str
    hashrate_5m: str
    hashrate_1h: str
    hashrate_24h: str
    hashrate_max: str
    shares_5m: str
    shares_1h: str
    shares_24h: str
    shares_total: str
    hashrate_history: List[dict]
    shares_history: List[dict]
```

### 阶段3: 服务层实现

#### 3.1 仪表盘服务
```python
# hash/taohash/api/services/dashboard_service.py
from typing import Dict, Any
from taohash.core.pricing import CoinPriceAPI
from taohash.core.pool import Pool
from taohash.validator import BaseValidator
from taohash.miner import BaseMiner

class DashboardService:
    def __init__(self):
        self.price_api = CoinPriceAPI("coingecko", None)
        self.validator = BaseValidator()
        self.miner = BaseMiner()
    
    async def get_dashboard_data(self, user_id: str) -> Dict[str, Any]:
        """获取仪表盘数据"""
        # 集成现有的TAO Hash核心功能
        # 从存储系统获取数据
        # 计算聚合统计
        pass
    
    async def get_hashrate_history(self, range: str = "24h") -> Dict[str, Any]:
        """获取算力历史数据"""
        pass
    
    async def get_accounts(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户账户列表"""
        pass
```

#### 3.2 矿工服务
```python
# hash/taohash/api/services/miner_service.py
from typing import List, Dict, Any
from taohash.miner import BaseMiner
from taohash.core.storage import get_miner_storage

class MinerService:
    def __init__(self):
        self.miner = BaseMiner()
        self.storage = get_miner_storage("redis", self.miner.config)
    
    async def get_miner_list(self, user_id: str, search: str = None, 
                           page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """获取矿工列表"""
        pass
    
    async def get_miner_detail(self, address: str) -> Dict[str, Any]:
        """获取矿工详情"""
        pass
    
    async def change_miner_node(self, address: str, from_node: str, 
                              to_node: str) -> Dict[str, Any]:
        """切换矿工节点"""
        pass
```

### 阶段4: 路由实现

#### 4.1 仪表盘路由
```python
# hash/taohash/api/routes/dashboard.py
from fastapi import APIRouter, Depends, HTTPException
from ..models.dashboard import DashboardResponse, HashrateHistoryResponse
from ..services.dashboard_service import DashboardService
from ..utils.auth import get_current_user

router = APIRouter(prefix="/api", tags=["Dashboard"])

@router.get("/dashboard", response_model=DashboardResponse)
async def get_dashboard(
    user_id: str = Depends(get_current_user),
    service: DashboardService = Depends()
):
    """获取仪表盘数据"""
    return await service.get_dashboard_data(user_id)

@router.get("/hashrate/history", response_model=HashrateHistoryResponse)
async def get_hashrate_history(
    range: str = "24h",
    service: DashboardService = Depends()
):
    """获取算力历史数据"""
    return await service.get_hashrate_history(range)

@router.get("/accounts")
async def get_accounts(
    user_id: str = Depends(get_current_user),
    service: DashboardService = Depends()
):
    """获取账户列表"""
    return await service.get_accounts(user_id)
```

### 阶段5: Web3钱包认证系统

#### 5.1 Web3钱包认证
本系统使用[Bittensor Wallet](https://bittensor.com/wallet)进行用户身份验证，支持多种钱包来源：

- **Bittensor Extension**: @opentensor/bittensor extension
- **Talisman**: talisman钱包
- **Polkadot**: polkadot钱包

```python
# hash/taohash/api/utils/auth.py
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
import hashlib
import time
from bittensor import Keypair

security = HTTPBearer()

class Web3AuthManager:
    def __init__(self):
        self.active_sessions = {}  # 存储活跃会话
    
    async def verify_wallet_signature(self, address: str, signature: str, message: str) -> bool:
        """验证钱包签名"""
        try:
            # 验证Substrate/Polkadot签名
            keypair = Keypair(ss58_address=address)
            return keypair.verify(message, signature)
        except Exception:
            return False
    
    async def create_session(self, wallet_address: str, wallet_source: str) -> str:
        """创建用户会话"""
        session_token = hashlib.sha256(f"{wallet_address}_{time.time()}".encode()).hexdigest()
        self.active_sessions[session_token] = {
            "address": wallet_address,
            "source": wallet_source,
            "created_at": time.time()
        }
        return session_token
    
    async def verify_session(self, token: str) -> Optional[Dict[str, Any]]:
        """验证会话"""
        session = self.active_sessions.get(token)
        if session and time.time() - session["created_at"] < 86400:  # 24小时有效期
            return session
        return None

auth_manager = Web3AuthManager()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    token = credentials.credentials
    session = await auth_manager.verify_session(token)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session"
        )
    return session["address"]

async def authenticate_wallet(request: Request):
    """钱包认证端点"""
    data = await request.json()
    address = data.get("address")
    signature = data.get("signature")
    message = data.get("message")
    source = data.get("source")
    
    if await auth_manager.verify_wallet_signature(address, signature, message):
        token = await auth_manager.create_session(address, source)
        return {"success": True, "token": token}
    else:
        raise HTTPException(status_code=400, detail="Invalid signature")
```

#### 5.2 钱包管理API
```python
# hash/taohash/api/routes/accounts.py
from fastapi import APIRouter, Depends, HTTPException
from ..utils.auth import get_current_user, authenticate_wallet

router = APIRouter(prefix="/api", tags=["Accounts"])

@router.post("/auth/wallet")
async def wallet_auth(request: Request):
    """钱包认证"""
    return await authenticate_wallet(request)

@router.get("/accounts")
async def get_accounts(user_address: str = Depends(get_current_user)):
    """获取钱包列表"""
    # 从用户会话获取钱包信息
    pass

@router.post("/accounts/select")
async def select_account(user_address: str = Depends(get_current_user)):
    """切换账户"""
    pass

@router.post("/accounts/disconnect")
async def disconnect(user_address: str = Depends(get_current_user)):
    """断开连接"""
    pass
```

## 与现有代码集成

### 1. 复用现有组件

```python
# 在服务层中使用现有的TAO Hash组件
from taohash.core.pool import Pool
from taohash.core.pricing import CoinPriceAPI
from taohash.validator import BaseValidator
from taohash.miner import BaseMiner
from taohash.core.storage import get_miner_storage, get_validator_storage

class APIService:
    def __init__(self):
        # 复用现有的核心组件
        self.pool = Pool(...)
        self.price_api = CoinPriceAPI("coingecko", None)
        self.validator = BaseValidator()
        self.miner = BaseMiner()
        self.storage = get_miner_storage("redis", self.miner.config)
```

### 2. 数据存储扩展

```python
# 扩展现有的存储系统
class APIDataStorage:
    def __init__(self, storage_type: str = "redis"):
        if storage_type == "redis":
            self.storage = BaseRedisStorage()
        else:
            self.storage = BaseJsonStorage()
    
    async def save_api_data(self, key: str, data: dict, prefix: str = "api"):
        """保存API数据"""
        self.storage.save_data(key, data, prefix)
    
    async def get_api_data(self, key: str, prefix: str = "api"):
        """获取API数据"""
        return self.storage.load_data(key, prefix)
```

## 部署方案

### 1. 开发环境
```bash
cd hash
pip install -e .[api]
python api_server.py
```

### 2. 生产环境
```dockerfile
# hash/Dockerfile.api
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -e .[api]

EXPOSE 8000
CMD ["python", "api_server.py"]
```

### 3. Docker Compose配置
```yaml
# hash/docker-compose.api.yml
version: '3.8'

services:
  taohash-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "8000:8000"
    environment:
      - REDIS_HOST=redis
      - BITTENSOR_NETWORK=finney
      - NETUID=14
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

## API功能对比分析

### 与API需求文档对比

根据`docs/api.md`的需求，以下是完整的API功能清单：

#### ✅ 已规划功能
- 仪表盘数据 (`/api/dashboard`)
- 算力历史 (`/api/hashrate/history`)
- 钱包账户管理 (`/api/accounts/*`)
- 矿工管理 (`/api/miners/*`)
- 验证者管理 (`/api/validator/*`)
- 质押系统 (`/api/stake/*`)

#### ⚠️ 需要补充的功能

##### 1. 矿工相关API
- [ ] **节点管理API** (`/api/nodes`) - 获取可用节点列表
- [ ] **矿工收益API**:
  - `/api/earnings/overview` - 收益总览
  - `/api/earnings/workers` - 收益列表
  - `/api/earnings/history` - 收益历史

##### 2. 验证者相关API
- [ ] **验证者收益API**:
  - `/api/validator/earnings/overview` - 收益总览
  - `/api/validator/earnings/chart` - 收益走势图
  - `/api/validator/earnings/history` - 收益历史
- [ ] **验证者钱包管理**:
  - `/api/validator/wallets` - 钱包列表
  - `/api/validator/wallets/add` - 添加钱包
  - `/api/validator/wallets/edit` - 编辑钱包

##### 3. 质押相关API
- [ ] **我的质押节点** (`/api/stake/my-nodes`) - 用户质押过的节点
- [ ] **收益操作**:
  - `/api/stake/claim` - 领取收益
  - `/api/stake/withdraw` - 提现

## 补充实现方案

### 阶段6: 补充缺失功能

#### 6.1 节点管理服务
```python
# hash/taohash/api/services/node_service.py
from typing import List, Dict, Any
from taohash.core.pool import Pool

class NodeService:
    def __init__(self):
        self.available_nodes = [
            {"name": "Node1", "url": "stratum+tcp://node1.taohash.com:3333"},
            {"name": "Node2", "url": "stratum+tcp://node2.taohash.com:3333"},
            {"name": "Node3", "url": "stratum+tcp://node3.taohash.com:3333"},
            {"name": "Node4", "url": "stratum+tcp://node4.taohash.com:3333"},
        ]
    
    async def get_available_nodes(self) -> List[Dict[str, Any]]:
        """获取可用节点列表"""
        return {"nodes": self.available_nodes}
```

#### 6.2 收益管理服务
```python
# hash/taohash/api/services/earnings_service.py
from typing import List, Dict, Any, Optional
from taohash.core.pricing import CoinPriceAPI
from taohash.core.storage import get_miner_storage

class EarningsService:
    def __init__(self):
        self.price_api = CoinPriceAPI("coingecko", None)
        self.storage = get_miner_storage("redis", None)
    
    async def get_earnings_overview(self, user_address: str) -> Dict[str, Any]:
        """获取收益总览"""
        # 计算今日和累计收益
        return {
            "today_rewards": "158.80 USD",
            "total_rewards": "158.80 USD"
        }
    
    async def get_earnings_workers(self, user_address: str) -> Dict[str, Any]:
        """获取矿工收益列表"""
        # 获取用户下所有矿工的收益
        pass
    
    async def get_earnings_history(self, user_address: str) -> Dict[str, Any]:
        """获取收益历史"""
        # 获取收益发放历史记录
        pass
```

#### 6.3 验证者钱包管理
```python
# hash/taohash/api/services/validator_wallet_service.py
from typing import List, Dict, Any
from taohash.core.storage import get_validator_storage

class ValidatorWalletService:
    def __init__(self):
        self.storage = get_validator_storage("redis", None)
    
    async def get_wallets(self, validator_address: str) -> Dict[str, Any]:
        """获取验证者钱包列表"""
        # 从存储中获取绑定的钱包
        return {
            "wallets": [
                {"coin": "LTC", "wallet_name": "My LTC Wallet", "address": "..."},
                {"coin": "Doge", "wallet_name": "My Doge Wallet", "address": "..."}
            ]
        }
    
    async def add_wallet(self, validator_address: str, coin: str, 
                        wallet_name: str, address: str) -> Dict[str, Any]:
        """添加钱包"""
        # 验证钱包地址格式并保存
        pass
    
    async def edit_wallet(self, validator_address: str, coin: str, 
                         wallet_name: str = None, address: str = None) -> Dict[str, Any]:
        """编辑钱包"""
        # 更新钱包信息
        pass
```

#### 6.4 质押管理扩展
```python
# hash/taohash/api/services/staking_service.py 扩展
class StakingService:
    # ... 现有代码 ...
    
    async def get_my_staked_nodes(self, user_address: str, 
                                 status: str = None) -> Dict[str, Any]:
        """获取用户质押的节点"""
        # 获取用户质押过的所有节点及状态
        return {
            "nodes": [
                {
                    "address": "5dhds...k9a",
                    "lock_status": "Staking",
                    "staked": "500 TAO",
                    "earn": "100 xxx",
                    "can_claim": True,
                    "can_unstake": True,
                    "can_stake": True,
                    "can_withdraw": False
                }
            ]
        }
    
    async def claim_rewards(self, user_address: str, node_address: str) -> Dict[str, Any]:
        """领取收益"""
        # 执行收益领取操作
        return {"success": True, "message": "收益领取成功"}
    
    async def withdraw(self, user_address: str, node_address: str) -> Dict[str, Any]:
        """提现"""
        # 执行提现操作
        return {"success": True, "message": "提现成功"}
```

## 完整实现优先级

### 第一阶段: 基础架构 + Web3认证
- [ ] 创建API模块结构
- [ ] 实现Web3钱包认证系统
- [ ] 实现钱包连接/断开功能
- [ ] 实现会话管理

### 第二阶段: 仪表盘API
- [ ] 实现仪表盘数据API
- [ ] 实现算力历史API
- [ ] 实现账户管理API

### 第三阶段: 矿工管理API
- [ ] 实现矿工列表API
- [ ] 实现矿工详情API
- [ ] 实现节点管理API
- [ ] 实现节点切换API
- [ ] 实现矿工收益API (overview/workers/history)

### 第四阶段: 验证者API
- [ ] 实现验证者概览API
- [ ] 实现验证者详情API
- [ ] 实现验证者质押/解质押API
- [ ] 实现验证者收益API (overview/chart/history)
- [ ] 实现验证者钱包管理API

### 第五阶段: 质押系统API
- [ ] 实现可质押验证者列表API
- [ ] 实现质押操作API
- [ ] 实现我的质押节点API
- [ ] 实现收益领取API
- [ ] 实现解质押和提现API

### 第六阶段: 优化和测试
- [ ] 性能优化和缓存
- [ ] 错误处理和日志
- [ ] 单元测试和集成测试
- [ ] API文档和部署指南

## 技术优势

1. **完全集成**: 与现有TAO Hash代码深度集成
2. **复用现有**: 充分利用现有的存储、价格、矿池等功能
3. **模块化**: 清晰的模块结构，易于维护
4. **可扩展**: 基于FastAPI的异步架构
5. **统一部署**: 与现有项目一起部署
6. **高性能**: 异步处理和缓存机制

## 风险评估

### 技术风险
- **依赖冲突**: 新依赖可能与现有依赖冲突
- **性能问题**: API响应时间可能较长
- **数据一致性**: 多数据源的数据同步问题

### 缓解措施
- 使用虚拟环境隔离依赖
- 实现缓存机制和异步处理
- 设计数据同步策略

## 总结

本方案在hash目录中创建独立的API模块，充分利用现有的TAO Hash基础设施，通过模块化的设计实现完整的Web API功能。该方案具有以下特点：

### 核心特性

1. **独立服务**: 在hash目录中创建完全独立的API模块
2. **职责分离**: 与proxy系统功能互补，避免重复建设
3. **深度集成**: 直接使用TAO Hash核心组件和存储系统
4. **模块化设计**: 清晰的代码结构，易于维护和扩展
5. **灵活部署**: 支持独立部署或与现有系统协同部署

### 技术优势

- **高性能**: 基于FastAPI的异步架构
- **可扩展**: 模块化设计支持功能扩展
- **易维护**: 清晰的代码结构和职责分离
- **低成本**: 复用现有基础设施，减少开发成本

### 实现价值

通过这个独立的API实现，TAO Hash项目将能够：
- 提供完整的Web API服务
- 支持多种客户端应用
- 提升用户体验和项目可用性
- 为生态系统发展提供技术基础

这个方案确保了API服务与TAO Hash生态系统的完美集成，同时保持了系统的模块化和可扩展性。