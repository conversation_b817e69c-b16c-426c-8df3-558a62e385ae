#!/usr/bin/env python3
"""
TAO Hash API Server

FastAPI server for the TAO Hash ecosystem providing dashboard functionality,
miner management, validator management, and staking system capabilities.
"""

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from taohash.api.routes import (
    dashboard_router,
    miners_router,
    validators_router,
    staking_router,
    accounts_router,
)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger.info("Starting TAO Hash API server...")
    yield
    logger.info("Shutting down TAO Hash API server...")


# Create FastAPI application
app = FastAPI(
    title="TAO Hash API",
    description="Web API for the TAO Hash ecosystem providing dashboard functionality, miner management, validator management, and staking system capabilities.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"},
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "TAO Hash API"}


# API version endpoint
@app.get("/version")
async def get_version():
    """Get API version."""
    return {"version": "1.0.0", "service": "TAO Hash API"}


# Include routers
app.include_router(dashboard_router, prefix="/api/v1")
app.include_router(miners_router, prefix="/api/v1")
app.include_router(validators_router, prefix="/api/v1")
app.include_router(staking_router, prefix="/api/v1")
app.include_router(accounts_router, prefix="/api/v1")


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to TAO Hash API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
    }


def main():
    """Main entry point."""
    import argparse

    # Create a new parser that doesn't conflict with bittensor
    parser = argparse.ArgumentParser(
        description="TAO Hash API Server",
        add_help=True,
        allow_abbrev=False,
    )
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind to (default: 8000)",
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error"],
        help="Log level (default: info)",
    )

    # Parse only known args to avoid conflicts with bittensor
    args, unknown = parser.parse_known_args()

    # Configure logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))

    logger.info(f"Starting TAO Hash API server on {args.host}:{args.port}")

    # Run the server
    uvicorn.run(
        "api_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level,
    )


if __name__ == "__main__":
    main()
