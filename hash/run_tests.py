#!/usr/bin/env python3
"""
TAO Hash Test Runner

Comprehensive test runner for TAO Hash API with Docker management.
"""

import os
import sys
import subprocess
import time
import signal
import argparse
from pathlib import Path


class TestRunner:
    """Test runner with Docker service management."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.docker_compose_path = self.project_root / "tests" / "integration_tests"
        self.api_process = None
        
    def run_command(self, cmd, cwd=None, check=True):
        """Run a shell command."""
        print(f"🔧 Running: {cmd}")
        if cwd:
            print(f"   in: {cwd}")
        
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd or self.project_root,
            capture_output=False
        )
        
        if check and result.returncode != 0:
            print(f"❌ Command failed with exit code {result.returncode}")
            sys.exit(result.returncode)
        
        return result.returncode == 0
    
    def start_docker_services(self):
        """Start Docker services for testing."""
        print("🐳 Starting Docker services...")
        
        if not (self.docker_compose_path / "docker-compose.yml").exists():
            print("⚠️  docker-compose.yml not found, skipping Docker services")
            return True
        
        success = self.run_command(
            "docker-compose up -d",
            cwd=self.docker_compose_path,
            check=False
        )
        
        if success:
            print("⏳ Waiting for services to be ready...")
            time.sleep(10)
            print("✅ Docker services started")
        else:
            print("⚠️  Failed to start Docker services")
        
        return success
    
    def stop_docker_services(self):
        """Stop Docker services."""
        print("🐳 Stopping Docker services...")
        
        if not (self.docker_compose_path / "docker-compose.yml").exists():
            return True
        
        self.run_command(
            "docker-compose down",
            cwd=self.docker_compose_path,
            check=False
        )
        print("✅ Docker services stopped")
        return True
    
    def start_api_server(self):
        """Start API server in background."""
        print("🚀 Starting API server...")
        
        try:
            self.api_process = subprocess.Popen(
                [sys.executable, "start_api.py"],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for server to start
            time.sleep(5)
            
            # Check if process is still running
            if self.api_process.poll() is None:
                print("✅ API server started")
                return True
            else:
                print("❌ API server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start API server: {e}")
            return False
    
    def stop_api_server(self):
        """Stop API server."""
        if self.api_process:
            print("🛑 Stopping API server...")
            self.api_process.terminate()
            try:
                self.api_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.api_process.kill()
            print("✅ API server stopped")
    
    def run_unit_tests(self):
        """Run unit tests."""
        print("🧪 Running unit tests...")
        return self.run_command(
            "python -m pytest tests/unit_tests/ -v --tb=short",
            check=False
        )
    
    def run_integration_tests(self):
        """Run integration tests."""
        print("🧪 Running integration tests...")
        return self.run_command(
            "python -m pytest tests/integration_tests/ -v --tb=short",
            check=False
        )
    
    def run_api_tests(self):
        """Run API endpoint tests."""
        print("🧪 Running API tests...")
        return self.run_command(
            "python test_api.py",
            check=False
        )
    
    def run_specific_test(self, test_path):
        """Run a specific test file or test."""
        print(f"🧪 Running specific test: {test_path}")
        return self.run_command(
            f"python -m pytest {test_path} -v --tb=short",
            check=False
        )
    
    def cleanup(self):
        """Cleanup resources."""
        self.stop_api_server()
        self.stop_docker_services()
    
    def run_all_tests(self):
        """Run all tests with proper setup and cleanup."""
        success = True
        
        try:
            # Start Docker services
            docker_started = self.start_docker_services()
            
            # Run unit tests (don't require Docker)
            print("\n" + "="*50)
            if not self.run_unit_tests():
                success = False
            
            # Run integration tests (require Docker)
            if docker_started:
                print("\n" + "="*50)
                if not self.run_integration_tests():
                    success = False
            
            # Start API server and run API tests
            print("\n" + "="*50)
            if self.start_api_server():
                if not self.run_api_tests():
                    success = False
            else:
                success = False
            
        except KeyboardInterrupt:
            print("\n⚠️  Tests interrupted by user")
            success = False
        except Exception as e:
            print(f"\n❌ Test runner error: {e}")
            success = False
        finally:
            self.cleanup()
        
        return success
    
    def run_integration_with_docker(self):
        """Run integration tests with Docker management."""
        success = True
        
        try:
            # Start Docker services
            if not self.start_docker_services():
                return False
            
            # Start API server
            if not self.start_api_server():
                return False
            
            # Run integration tests
            success = self.run_integration_tests()
            
        except KeyboardInterrupt:
            print("\n⚠️  Tests interrupted by user")
            success = False
        except Exception as e:
            print(f"\n❌ Integration test error: {e}")
            success = False
        finally:
            self.cleanup()
        
        return success


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="TAO Hash Test Runner")
    parser.add_argument(
        "command",
        choices=["all", "unit", "integration", "api", "specific"],
        help="Test command to run"
    )
    parser.add_argument(
        "--test-path",
        help="Specific test path (for 'specific' command)"
    )
    parser.add_argument(
        "--no-docker",
        action="store_true",
        help="Skip Docker services"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Handle interrupt signal
    def signal_handler(sig, frame):
        print("\n⚠️  Received interrupt signal")
        runner.cleanup()
        sys.exit(1)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.command == "all":
            success = runner.run_all_tests()
        elif args.command == "unit":
            success = runner.run_unit_tests()
        elif args.command == "integration":
            if args.no_docker:
                success = runner.run_integration_tests()
            else:
                success = runner.run_integration_with_docker()
        elif args.command == "api":
            if runner.start_api_server():
                success = runner.run_api_tests()
                runner.stop_api_server()
            else:
                success = False
        elif args.command == "specific":
            if not args.test_path:
                print("❌ --test-path is required for 'specific' command")
                sys.exit(1)
            success = runner.run_specific_test(args.test_path)
        
        if success:
            print("\n🎉 All tests passed!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")
        runner.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
