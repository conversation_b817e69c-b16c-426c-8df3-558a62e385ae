# TAO Hash API Dockerfile (fixed editable install)
FROM python:3.11-slim

ARG DEBIAN_FRONTEND=noninteractive
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app

WORKDIR /app

# 系统依赖
RUN apt-get update && apt-get install -y \
    gcc g++ curl git build-essential pkg-config libssl-dev libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制项目配置（缓存关键点）
COPY pyproject.toml ./

# 创建最小但**稳定**的包结构（关键：确保每次构建相同）
RUN mkdir -p taohash && \
    echo '__version__ = "0.3.0"' > taohash/__init__.py && \
    echo '# Minimal package for dependency installation' > taohash/__init__.py

# 基础工具
RUN pip install -U pip setuptools wheel

# 使用 --no-build-isolation 和固定的包结构安装依赖
RUN pip install --no-build-isolation .[api]

# 复制真实源码（覆盖最小结构）
COPY taohash/ ./taohash/
COPY api_server.py .

# 重新安装项目（--force-reinstall 确保使用新源码）
RUN pip install --no-deps --force-reinstall -e .

# 可选：安装 bittensor 相关依赖，失败不影响构建
RUN pip install -e .[bt] || echo "bittensor extras 安装失败，继续..."

EXPOSE 8000
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 CMD curl -f http://localhost:8000/health || exit 1
CMD ["python", "api_server.py", "--host", "0.0.0.0", "--port", "8000"] 