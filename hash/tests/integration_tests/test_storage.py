import time
import pytest
import redis
from taohash.miner.storage import JsonStorage, RedisStorage
from bittensor.core.config import Config


def wait_for_redis(host="localhost", port=6379, timeout=30):
    """等待Redis服务可用"""
    import time
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            client = redis.Redis(host=host, port=port, decode_responses=True)
            client.ping()
            return True
        except redis.ConnectionError:
            time.sleep(0.5)
    return False


example_pool_data = {
    "validator_hotkey_1": {
        "ip": "***********",
        "port": 3333,
        "username": "user",
        "extra_data": {"full_username": "user.workerid"}
    }
}
example_block = int(time.time())


def test_storage_json():
    """Tests JSON storage."""
    config = Config()
    json_store = JsonStorage(config)
    json_store.save_pool_data(example_block, example_pool_data)

    loaded = json_store.get_pool_info(example_block)
    print("Loaded JSON data:", loaded)

    latest = json_store.get_latest_pool_info()
    print("Latest JSON:", latest)

    assert latest == example_pool_data


def test_storage_redis():
    """Tests Redis storage."""
    try:
        import redis
    except ImportError:
        pytest.skip("Redis library not installed")
    
    # 检查Redis服务是否可用
    if not wait_for_redis():
        pytest.skip("Redis服务不可用。请使用 'docker compose up -d' 启动所需服务")
    
    redis_config = Config()
    redis_config.redis_host = "localhost"
    redis_config.redis_port = 6379
    redis_config.redis_ttl = 15

    try:
        redis_store = RedisStorage(config=redis_config)
        redis_store.save_pool_data(example_block, example_pool_data)

        loaded_redis = redis_store.get_pool_info(example_block)
        assert loaded_redis == example_pool_data

        latest_redis = redis_store.get_latest_pool_info()
        print("Latest Redis:", latest_redis)

        assert latest_redis == example_pool_data
        
    except redis.ConnectionError as e:
        pytest.skip(f"无法连接到Redis: {e}. 请使用 'docker compose up -d' 启动所需服务")


def test_generate_user_id_with_custom_network():
    """Tests user ID generation."""
    # Preps
    config = Config()
    config.wallet = Config()
    config.wallet.name = "test wallet"
    config.wallet.hotkey = "test hotkey"
    config.subtensor = Config()
    config.subtensor.network = "wss://subtensornodeurl.tao:9944"
    config.netuid = 332

    # call
    generate_user_id = JsonStorage.generate_user_id(config)

    # assert
    assert generate_user_id == "332_TEST_WALLET_TEST_HOTKEY"


def test_generate_user_id_with_empy_parts():
    """Tests user ID generation with empty parts in the config."""
    # prep
    config = Config()

    # call
    generate_user_id = JsonStorage.generate_user_id(config)

    # assert
    assert generate_user_id == "NONE_UNKNOWN_UNKNOWN"
