#!/usr/bin/env python3
"""
简化版的 API 认证集成测试

使用 FastAPI 内置的 TestClient，不需要额外的 httpx 依赖
"""

import sys
import os
import hashlib
import secrets
import time
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

try:
    from fastapi.testclient import TestClient
    from taohash.api import create_app
    from taohash.api.services import AccountService
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 依赖缺失: {e}")
    print("跳过 FastAPI 集成测试")
    DEPENDENCIES_AVAILABLE = False

import pytest

# 如果依赖不可用，跳过所有测试
pytestmark = pytest.mark.skipif(
    not DEPENDENCIES_AVAILABLE, 
    reason="缺少 FastAPI 或项目依赖"
)


class MockBittensorWallet:
    """模拟 Bittensor 钱包用于测试"""
    
    def __init__(self, address: str, private_key: str = None):
        self.address = address
        self.private_key = private_key or secrets.token_hex(32)
    
    def sign_message(self, message: str) -> str:
        """
        模拟钱包签名消息
        """
        # 创建与 AccountService 签名验证逻辑匹配的签名
        signature_data = f"{self.address}_*_{message}"
        signature_hash = hashlib.sha256(signature_data.encode()).hexdigest()
        return f"0x{signature_hash}"


def test_wallet_auth_flow():
    """测试完整的钱包认证流程"""
    
    print("🧪 测试 Bittensor 钱包认证集成")
    print("=" * 50)
    
    # 创建应用和客户端
    app = create_app()
    client = TestClient(app)
    
    # 创建测试钱包
    test_wallet = MockBittensorWallet("5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY")
    
    try:
        # 步骤 1: 获取 nonce
        print("\n📋 步骤 1: 获取登录随机数")
        nonce_response = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        print(f"状态码: {nonce_response.status_code}")
        if nonce_response.status_code != 200:
            print(f"❌ Nonce 请求失败: {nonce_response.text}")
            return False
            
        nonce_data = nonce_response.json()
        print(f"响应: {nonce_data}")
        
        if not nonce_data.get("success"):
            print("❌ Nonce 生成失败")
            return False
            
        nonce_message = nonce_data["nonce"]
        print(f"✅ Nonce 获取成功: {nonce_message[:50]}...")
        
        # 步骤 2: 钱包签名
        print("\n🔐 步骤 2: 钱包签名")
        signature = test_wallet.sign_message(nonce_message)
        print(f"✅ 签名生成: {signature[:20]}...")
        
        # 步骤 3: 提交认证
        print("\n🎫 步骤 3: 提交认证")
        auth_response = client.post(
            "/accounts/auth",
            json={
                "address": test_wallet.address,
                "signature": signature,
                "message": nonce_message,
                "source": "polkadot-js"
            }
        )
        
        print(f"状态码: {auth_response.status_code}")
        if auth_response.status_code != 200:
            print(f"❌ 认证请求失败: {auth_response.text}")
            return False
            
        auth_data = auth_response.json()
        print(f"响应: {auth_data}")
        
        if not auth_data.get("success"):
            print(f"❌ 认证失败: {auth_data.get('message', '未知错误')}")
            return False
            
        token = auth_data["token"]
        print(f"✅ 认证成功，token: {token[:20]}...")
        
        # 步骤 4: 使用 token 访问保护的 API
        print("\n🔒 步骤 4: 使用 token 访问保护的 API")
        protected_response = client.get(
            "/dashboard/overview",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        print(f"状态码: {protected_response.status_code}")
        if protected_response.status_code == 401:
            print("❌ Token 验证失败")
            return False
        elif protected_response.status_code == 404:
            print("⚠️ Dashboard API 不存在，但 token 验证通过")
        else:
            print(f"✅ Token 验证通过，状态码: {protected_response.status_code}")
        
        # 步骤 5: 测试断开连接
        print("\n🔌 步骤 5: 测试断开连接")
        disconnect_response = client.post(
            "/accounts/disconnect",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        print(f"状态码: {disconnect_response.status_code}")
        if disconnect_response.status_code != 200:
            print(f"❌ 断开连接失败: {disconnect_response.text}")
            return False
            
        disconnect_data = disconnect_response.json()
        print(f"响应: {disconnect_data}")
        
        if disconnect_data.get("success"):
            print("✅ 断开连接成功")
        else:
            print("❌ 断开连接失败")
            return False
        
        print("\n🎉 所有测试步骤完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_invalid_scenarios():
    """测试无效场景"""
    
    print("\n🧪 测试无效场景")
    print("=" * 30)
    
    app = create_app()
    client = TestClient(app)
    
    # 测试无效签名
    print("\n📋 测试: 无效签名")
    
    # 先获取有效的 nonce
    test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
    nonce_response = client.post("/accounts/nonce", json={"address": test_address})
    
    if nonce_response.status_code == 200:
        nonce_data = nonce_response.json()
        nonce_message = nonce_data["nonce"]
        
        # 使用无效签名
        auth_response = client.post(
            "/accounts/auth",
            json={
                "address": test_address,
                "signature": "0xinvalid_signature",
                "message": nonce_message,
                "source": "polkadot-js"
            }
        )
        
        auth_data = auth_response.json()
        if not auth_data.get("success"):
            print("✅ 无效签名正确被拒绝")
        else:
            print("❌ 无效签名被错误接受")
    
    # 测试无效 token
    print("\n🎫 测试: 无效 Token")
    protected_response = client.get(
        "/dashboard/overview",
        headers={"Authorization": "Bearer invalid_token_12345"}
    )
    
    if protected_response.status_code == 401:
        print("✅ 无效 token 正确被拒绝")
    else:
        print(f"❌ 无效 token 处理异常，状态码: {protected_response.status_code}")
    
    print("✅ 无效场景测试完成")


def main():
    """主函数"""
    
    if not DEPENDENCIES_AVAILABLE:
        print("⚠️ 缺少必要依赖，跳过 FastAPI 集成测试")
        return
    
    print("🚀 TaoHash API 认证集成测试")
    print("测试内容:")
    print("- Bittensor 钱包认证流程")
    print("- Token 生成和验证")
    print("- 会话管理")
    print("- 错误处理")
    
    try:
        # 运行主要测试
        success = test_wallet_auth_flow()
        
        if success:
            # 运行边界条件测试
            test_invalid_scenarios()
            
            print("\n" + "=" * 60)
            print("🎉 集成测试完成！")
            print("\n📋 测试覆盖范围:")
            print("  ✓ 完整的钱包认证流程")
            print("  ✓ Nonce 生成和验证")
            print("  ✓ 签名验证机制")
            print("  ✓ Token 生成和使用")
            print("  ✓ 会话管理")
            print("  ✓ 错误处理")
            print("\n🔧 注意事项:")
            print("  • 当前使用模拟签名验证（适用于开发和测试）")
            print("  • 生产环境需要实现真正的密码学签名验证")
            print("  • 可以通过 CI/CD 自动运行此测试")
        else:
            print("\n❌ 主要测试失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()