#!/usr/bin/env python3
"""
集成测试运行脚本
运行集成测试，假设Docker Compose服务已在CI中启动
"""
import os
import sys
import subprocess
import argparse
from typing import Optional


def run_tests(test_dir: str, test_filter: Optional[str] = None) -> bool:
    """运行集成测试"""
    print("运行集成测试...")
    
    # 设置环境变量以启用集成测试
    env = os.environ.copy()
    env["SKIP_INTEGRATION_TESTS"] = "0"
    
    # 构建pytest命令
    pytest_cmd = ["python", "-m", "pytest", "-v"]
    
    if test_filter:
        pytest_cmd.extend(["-k", test_filter])
    
    pytest_cmd.append(".")
    
    # 运行测试
    result = subprocess.run(
        pytest_cmd,
        cwd=test_dir,
        env=env
    )
    
    return result.returncode == 0


def main():
    parser = argparse.ArgumentParser(description="运行集成测试")
    parser.add_argument(
        "--filter", 
        type=str, 
        help="测试过滤器 (例如: test_storage_redis)"
    )
    
    args = parser.parse_args()
    
    # 获取测试目录
    test_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"测试目录: {test_dir}")
    
    print("=== 开始集成测试 ===")
    
    # 运行测试
    success = run_tests(test_dir, args.filter)
    
    if success:
        print("=== 集成测试通过 ===")
    else:
        print("=== 集成测试失败 ===")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()