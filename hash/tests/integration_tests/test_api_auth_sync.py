#!/usr/bin/env python3
"""
同步版本的 API 认证集成测试
解决了异步测试在 CI 中被跳过的问题
"""

import hashlib
import secrets
import time

try:
    import pytest
    from fastapi.testclient import TestClient
    from taohash.api import create_app
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 依赖缺失: {e}")
    print("跳过 API 集成测试")
    DEPENDENCIES_AVAILABLE = False
    
    # Mock pytest for graceful handling
    class MockPytest:
        class mark:
            @staticmethod
            def skipif(condition, reason=""):
                def decorator(func):
                    return func
                return decorator
    pytest = MockPytest()


class MockBittensorWallet:
    """模拟 Bittensor 钱包用于测试"""
    
    def __init__(self, address: str):
        self.address = address
    
    def sign_message(self, message: str) -> str:
        """生成与 AccountService 验证逻辑匹配的签名"""
        signature_data = f"{self.address}_*_{message}"
        signature_hash = hashlib.sha256(signature_data.encode()).hexdigest()
        return f"0x{signature_hash}"


def test_sync_wallet_auth():
    """同步钱包认证测试"""
    if not DEPENDENCIES_AVAILABLE:
        pytest.skip("缺少必要依赖")
    
    print(f"\n🧪 测试同步钱包认证流程")
    
    app = create_app()
    client = TestClient(app)
    wallet = MockBittensorWallet("5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY")
    
    # 步骤 1: 获取 nonce
    print("📋 获取 nonce")
    nonce_response = client.post(
        "/accounts/nonce",
        json={"address": wallet.address}
    )
    
    assert nonce_response.status_code == 200
    nonce_data = nonce_response.json()
    assert nonce_data["success"] is True
    
    nonce_message = nonce_data["nonce"]
    print(f"✅ Nonce 获取成功")
    
    # 步骤 2: 认证
    print("🔐 钱包认证")
    signature = wallet.sign_message(nonce_message)
    
    auth_response = client.post(
        "/accounts/auth",
        json={
            "address": wallet.address,
            "signature": signature,
            "message": nonce_message,
            "source": "polkadot-js"
        }
    )
    
    assert auth_response.status_code == 200
    auth_data = auth_response.json()
    assert auth_data["success"] is True
    assert "token" in auth_data
    
    print(f"✅ 认证成功")
    return True


# 如果作为脚本运行
def main():
    if not DEPENDENCIES_AVAILABLE:
        print("⚠️ 缺少必要依赖，跳过测试")
        return
    
    print("🚀 运行同步 API 认证测试")
    
    try:
        test_sync_wallet_auth()
        print("\n🎉 同步 API 测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()