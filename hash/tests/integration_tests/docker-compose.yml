services:
  redis:
    image: redis:7-alpine
    container_name: taohash-test-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 10s
    restart: unless-stopped

networks:
  default:
    driver: bridge

volumes:
  redis_data:
    driver: local