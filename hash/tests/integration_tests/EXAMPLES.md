# 集成测试使用示例

## 基本用法

### 在CI环境中运行（自动）
集成测试在CI中自动运行，Docker Compose服务由CI管理。

### 本地开发运行

#### 方法1: 使用Docker Compose（推荐）
```bash
cd hash/tests/integration_tests

# 启动服务
docker compose up -d

# 等待Redis就绪
docker compose exec redis redis-cli ping

# 运行测试
python run_integration_tests.py

# 清理服务
docker compose down -v
```

#### 方法2: 使用测试脚本
```bash
cd hash/tests/integration_tests
python run_integration_tests.py
# 注意：需要手动管理Docker Compose服务
```

### 运行特定测试
```bash
python run_integration_tests.py --filter test_storage_redis
python run_integration_tests.py --filter TestCoinMarketCap
```

### 开发模式 - 保持服务运行
```bash
# 启动服务
docker compose up -d

# 在另一个终端或多次运行测试
python -m pytest test_storage.py::test_storage_redis -v
python -m pytest test_pricing.py -v

# 完成后清理
docker compose down -v
```

## 测试输出示例

### 成功运行
```
=== 开始集成测试 ===
运行集成测试...
13 passed, 1 warning in 5.27s
=== 集成测试通过 ===
```

### 服务不可用时自动跳过
```bash
python -m pytest test_storage.py::test_storage_redis -v
# 输出: SKIPPED (Redis服务不可用。请使用 'docker compose up -d' 启动所需服务)
```

## 故障排除

### 查看服务状态
```bash
# 检查正在运行的容器
docker ps

# 查看Redis日志
docker logs taohash-test-redis

# 手动测试Redis连接
redis-cli -h localhost -p 6379 ping
```

### 完全重置
```bash
docker compose down -v
docker system prune -f
docker compose up -d
python run_integration_tests.py
```

## 集成到CI/CD

在CI环境中使用（已配置）：
```yaml
# .woodpecker/docker-test.yml 示例
- name: Run Integration Tests
  run: |
    cd hash/tests/integration_tests
    docker compose up -d
    timeout 60 bash -c 'until docker compose exec redis redis-cli ping; do sleep 1; done'
    python run_integration_tests.py --no-cleanup
    docker compose down -v
```

或者跳过集成测试：
```bash
export SKIP_INTEGRATION_TESTS=1
python -m pytest
```