#!/usr/bin/env python3
"""
AccountService 集成测试

直接测试 AccountService 的业务逻辑，不依赖 FastAPI
"""

import sys
import os
import asyncio
import hashlib
import secrets
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

try:
    from taohash.api.services import AccountService
    from taohash.api.models.accounts import WalletAuthResponse
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在项目根目录中运行此测试")
    sys.exit(1)


class MockBittensorWallet:
    """模拟 Bittensor 钱包"""
    
    def __init__(self, address: str):
        self.address = address
    
    def sign_message(self, message: str) -> str:
        """生成与 AccountService 验证逻辑匹配的签名"""
        signature_data = f"{self.address}_*_{message}"
        signature_hash = hashlib.sha256(signature_data.encode()).hexdigest()
        return f"0x{signature_hash}"


async def test_nonce_generation():
    """测试 Nonce 生成"""
    print("📋 测试 Nonce 生成")
    
    service = AccountService()
    test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
    
    # 生成 nonce
    nonce1 = await service.generate_nonce(test_address)
    nonce2 = await service.generate_nonce(test_address)
    
    # 验证 nonce 格式
    assert "TaoHash 登录验证" in nonce1
    assert test_address in nonce1
    assert "随机数:" in nonce1
    
    # 验证 nonce 唯一性
    assert nonce1 != nonce2
    
    print(f"✅ Nonce 生成测试通过")
    print(f"   样例: {nonce1[:50]}...")
    return True


async def test_wallet_authentication_substrate():
    """测试 Substrate 钱包认证"""
    print("\n🔐 测试 Substrate 钱包认证")
    
    service = AccountService()
    wallet = MockBittensorWallet("5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY")
    
    # 1. 生成 nonce
    nonce_message = await service.generate_nonce(wallet.address)
    print(f"✅ Nonce 生成: {nonce_message[:30]}...")
    
    # 2. 钱包签名
    signature = wallet.sign_message(nonce_message)
    print(f"✅ 签名生成: {signature[:20]}...")
    
    # 3. 认证
    result = await service.authenticate_wallet(
        wallet.address, signature, nonce_message, "polkadot-js"
    )
    
    # 验证结果
    assert isinstance(result, WalletAuthResponse)
    assert result.success is True
    assert result.token is not None
    assert len(result.token) > 0
    
    print(f"✅ 认证成功，token: {result.token[:20]}...")
    return result.token


async def test_wallet_authentication_ethereum():
    """测试 Ethereum 钱包认证"""
    print("\n🔐 测试 Ethereum 钱包认证")
    
    service = AccountService()
    wallet = MockBittensorWallet("******************************************")
    
    # 1. 生成 nonce
    nonce_message = await service.generate_nonce(wallet.address)
    
    # 2. 钱包签名
    signature = wallet.sign_message(nonce_message)
    
    # 3. 认证
    result = await service.authenticate_wallet(
        wallet.address, signature, nonce_message, "metamask"
    )
    
    # 验证结果
    assert result.success is True
    assert result.token is not None
    
    print(f"✅ Ethereum 钱包认证成功")
    return result.token


async def test_token_verification():
    """测试 Token 验证"""
    print("\n🎫 测试 Token 验证")
    
    service = AccountService()
    wallet = MockBittensorWallet("5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY")
    
    # 先获取有效 token
    nonce_message = await service.generate_nonce(wallet.address)
    signature = wallet.sign_message(nonce_message)
    auth_result = await service.authenticate_wallet(
        wallet.address, signature, nonce_message, "polkadot-js"
    )
    
    token = auth_result.token
    
    # 验证有效 token
    session = await service.verify_session(token)
    assert session is not None
    assert session["address"] == wallet.address
    assert session["source"] == "polkadot-js"
    
    print(f"✅ 有效 token 验证通过")
    
    # 验证无效 token
    invalid_session = await service.verify_session("invalid_token_123")
    assert invalid_session is None
    
    print(f"✅ 无效 token 正确被拒绝")
    return True


async def test_invalid_signature():
    """测试无效签名处理"""
    print("\n❌ 测试无效签名处理")
    
    service = AccountService()
    test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
    
    # 生成有效 nonce
    nonce_message = await service.generate_nonce(test_address)
    
    # 使用无效签名
    invalid_signature = "0xinvalid_signature_123"
    
    result = await service.authenticate_wallet(
        test_address, invalid_signature, nonce_message, "polkadot-js"
    )
    
    # 应该失败
    assert result.success is False
    assert result.message is not None
    
    print(f"✅ 无效签名正确被拒绝: {result.message}")
    return True


async def test_message_tampering():
    """测试消息篡改检测"""
    print("\n🔍 测试消息篡改检测")
    
    service = AccountService()
    wallet = MockBittensorWallet("5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY")
    
    # 生成正确的 nonce 和签名
    nonce_message = await service.generate_nonce(wallet.address)
    signature = wallet.sign_message(nonce_message)
    
    # 篡改消息
    tampered_message = nonce_message + " TAMPERED"
    
    result = await service.authenticate_wallet(
        wallet.address, signature, tampered_message, "polkadot-js"
    )
    
    # 应该失败
    assert result.success is False
    
    print(f"✅ 消息篡改正确被检测")
    return True


async def test_session_disconnect():
    """测试会话断开"""
    print("\n🔌 测试会话断开")
    
    service = AccountService()
    wallet = MockBittensorWallet("5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY")
    
    # 创建会话
    nonce_message = await service.generate_nonce(wallet.address)
    signature = wallet.sign_message(nonce_message)
    auth_result = await service.authenticate_wallet(
        wallet.address, signature, nonce_message, "polkadot-js"
    )
    
    token = auth_result.token
    
    # 验证会话存在
    session = await service.verify_session(token)
    assert session is not None
    
    # 断开连接
    disconnect_result = await service.disconnect(wallet.address)
    assert disconnect_result.success is True
    
    # 验证会话已失效
    session_after_disconnect = await service.verify_session(token)
    assert session_after_disconnect is None
    
    print(f"✅ 会话断开功能正常")
    return True


async def run_all_tests():
    """运行所有测试"""
    
    print("🚀 开始运行 AccountService 集成测试")
    print("=" * 60)
    
    tests = [
        test_nonce_generation,
        test_wallet_authentication_substrate,
        test_wallet_authentication_ethereum,
        test_token_verification,
        test_invalid_signature,
        test_message_tampering,
        test_session_disconnect,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            await test()
            passed += 1
        except Exception as e:
            print(f"❌ 测试失败: {test.__name__}")
            print(f"   错误: {e}")
            import traceback
            traceback.print_exc()
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"🧪 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        print("\n📋 测试覆盖范围:")
        print("  ✓ Nonce 生成和管理")
        print("  ✓ Substrate 钱包认证")
        print("  ✓ Ethereum 钱包认证")
        print("  ✓ Token 验证机制")
        print("  ✓ 无效签名处理")
        print("  ✓ 消息完整性检测")
        print("  ✓ 会话管理和断开")
        print("\n🔧 注意事项:")
        print("  • 测试使用模拟签名验证")
        print("  • 生产环境需要真实的密码学验证")
        print("  • 建议在 CI/CD 中集成此测试")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


def main():
    """主函数"""
    
    print("🧪 TaoHash AccountService 集成测试")
    print("测试内容: Bittensor 钱包认证的核心业务逻辑")
    
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()