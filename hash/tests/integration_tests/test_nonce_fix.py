#!/usr/bin/env python3
"""
测试 nonce 存储修复

验证 AccountService 单例模式是否解决了 nonce 丢失问题
"""

import asyncio
import hashlib

def test_account_service_singleton():
    """测试 AccountService 单例行为"""
    try:
        from taohash.api.dependencies import get_account_service
        print("✅ 依赖模块导入成功")
        
        # 获取两个服务实例
        service1 = get_account_service()
        service2 = get_account_service()
        
        # 验证是同一个实例
        assert service1 is service2, "应该返回相同的实例"
        print("✅ 单例模式工作正常")
        
        return True
        
    except ImportError as e:
        print(f"⚠️ 依赖缺失: {e}")
        return False


async def test_nonce_persistence():
    """测试 nonce 持久化"""
    try:
        from taohash.api.dependencies import get_account_service
        
        service = get_account_service()
        test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
        
        # 生成 nonce
        nonce_message = await service.generate_nonce(test_address)
        print(f"✅ Nonce 生成: {nonce_message[:50]}...")
        
        # 验证 nonce 存储
        assert test_address in service.nonces, "Nonce 应该被存储"
        print("✅ Nonce 存储成功")
        
        # 模拟第二个请求使用相同的服务实例
        service2 = get_account_service()
        assert service is service2, "应该是同一个实例"
        assert test_address in service2.nonces, "第二个实例应该有相同的 nonce"
        print("✅ Nonce 在实例间持久化")
        
        # 测试签名验证
        signature_data = f"{test_address}_*_{nonce_message}"
        signature_hash = hashlib.sha256(signature_data.encode()).hexdigest()
        signature = f"0x{signature_hash}"
        
        auth_result = await service.authenticate_wallet(
            test_address, signature, nonce_message, "polkadot-js"
        )
        
        assert auth_result.success, f"认证应该成功: {auth_result.message}"
        print("✅ 完整认证流程成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    print("🔧 测试 Nonce 存储修复")
    print("=" * 40)
    
    # 测试 1: 单例模式
    if not test_account_service_singleton():
        print("❌ 单例模式测试失败")
        return False
    
    # 测试 2: nonce 持久化
    if not asyncio.run(test_nonce_persistence()):
        print("❌ Nonce 持久化测试失败")
        return False
    
    print("\n🎉 所有测试通过！")
    print("✅ AccountService 单例模式正常工作")
    print("✅ Nonce 在请求间正确持久化")
    print("✅ 完整认证流程可以正常工作")
    
    return True


if __name__ == "__main__":
    main()