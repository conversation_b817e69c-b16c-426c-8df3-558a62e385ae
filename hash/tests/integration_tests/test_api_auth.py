"""
API 认证集成测试

测试 Bittensor 钱包认证的完整流程，包括：
1. Nonce 生成
2. 钱包签名认证
3. Token 使用和验证
4. 会话管理
"""

import asyncio
import time
import hashlib
import secrets
from typing import Dict, Any

try:
    import pytest
    import httpx
    from fastapi.testclient import TestClient
    from taohash.api import create_app
    from taohash.api.services import AccountService
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 依赖缺失: {e}")
    print("跳过完整 API 集成测试")
    DEPENDENCIES_AVAILABLE = False
    
    # 创建基本的 pytest 替代，避免导入错误
    class MockPytest:
        class mark:
            @staticmethod
            def skipif(condition, reason=""):
                def decorator(func):
                    return func
                return decorator
        
        class fixture:
            def __init__(self, scope="function"):
                pass
            def __call__(self, func):
                return func
    
    pytest = MockPytest()

# 如果依赖不可用，跳过所有测试
if DEPENDENCIES_AVAILABLE:
    pytestmark = pytest.mark.skipif(
        not DEPENDENCIES_AVAILABLE, 
        reason="缺少 pytest, httpx, FastAPI 或项目依赖"
    )


class MockBittensorWallet:
    """模拟 Bittensor 钱包用于测试"""
    
    def __init__(self, address: str, private_key: str = None):
        self.address = address
        self.private_key = private_key or secrets.token_hex(32)
    
    def sign_message(self, message: str) -> str:
        """
        模拟钱包签名消息
        
        Args:
            message: 要签名的消息
            
        Returns:
            签名字符串
        """
        # 创建与 AccountService 签名验证逻辑匹配的签名
        # 使用固定的模式来确保测试的可重复性
        signature_data = f"{self.address}_*_{message}"  # 使用 * 作为模拟私钥标识
        signature_hash = hashlib.sha256(signature_data.encode()).hexdigest()
        return f"0x{signature_hash}"


class TestBittensorWalletAuth:
    """Bittensor 钱包认证集成测试"""
    
    @pytest.fixture(scope="class")
    def app(self):
        """创建测试应用实例"""
        return create_app()
    
    @pytest.fixture(scope="class") 
    def client(self, app):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture(scope="class")
    def test_wallet(self):
        """创建测试钱包"""
        # 使用固定的测试地址，确保测试的可重复性
        test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
        test_private_key = "abcd1234567890efgh1234567890abcdefgh1234567890"
        return MockBittensorWallet(test_address, test_private_key)
    
    @pytest.fixture(scope="class")
    def ethereum_wallet(self):
        """创建以太坊测试钱包"""
        eth_address = "******************************************"
        eth_private_key = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
        return MockBittensorWallet(eth_address, eth_private_key)
    
    def test_complete_wallet_auth_flow_substrate(self, client, test_wallet):
        """
        测试完整的 Substrate 钱包认证流程
        
        测试步骤:
        1. 获取 nonce
        2. 签名认证
        3. 使用 token 访问保护的 API
        4. 断开连接
        """
        print(f"\n🧪 测试 Substrate 钱包认证流程 - 地址: {test_wallet.address}")
        
        # 步骤 1: 获取 nonce
        print("📋 步骤 1: 获取登录随机数")
        nonce_response = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        assert nonce_response.status_code == 200
        nonce_data = nonce_response.json()
        assert nonce_data["success"] is True
        assert "nonce" in nonce_data
        assert test_wallet.address in nonce_data["nonce"]
        
        nonce_message = nonce_data["nonce"]
        print(f"✅ Nonce 获取成功: {nonce_message[:50]}...")
        
        # 步骤 2: 模拟钱包签名
        print("🔐 步骤 2: 钱包签名")
        signature = test_wallet.sign_message(nonce_message)
        print(f"✅ 签名生成: {signature[:20]}...")
        
        # 步骤 3: 提交认证
        print("🎫 步骤 3: 提交认证")
        auth_response = client.post(
            "/accounts/auth",
            json={
                "address": test_wallet.address,
                "signature": signature,
                "message": nonce_message,
                "source": "polkadot-js"
            }
        )
        
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is True
        assert "token" in auth_data
        
        token = auth_data["token"]
        print(f"✅ 认证成功，token: {token[:20]}...")
        
        # 步骤 4: 使用 token 访问保护的 API
        print("🔒 步骤 4: 使用 token 访问保护的 API")
        protected_response = client.get(
            "/dashboard/overview",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        # 由于 dashboard 可能有其他依赖，我们主要测试认证是否工作
        # 如果是 401，说明 token 验证失败
        # 如果是其他错误码，说明 token 验证通过但业务逻辑有问题
        assert protected_response.status_code != 401, "Token 验证失败"
        print(f"✅ Token 验证通过，状态码: {protected_response.status_code}")
        
        # 步骤 5: 测试断开连接
        print("🔌 步骤 5: 测试断开连接")
        disconnect_response = client.post(
            "/accounts/disconnect",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert disconnect_response.status_code == 200
        disconnect_data = disconnect_response.json()
        assert disconnect_data["success"] is True
        print("✅ 断开连接成功")
        
        # 步骤 6: 验证 token 已失效
        print("🔍 步骤 6: 验证 token 已失效")
        invalid_response = client.get(
            "/dashboard/overview", 
            headers={"Authorization": f"Bearer {token}"}
        )
        # 断开连接后 token 应该失效
        assert invalid_response.status_code == 401
        print("✅ Token 已正确失效")
    
    def test_complete_wallet_auth_flow_ethereum(self, client, ethereum_wallet):
        """
        测试完整的 Ethereum 钱包认证流程
        """
        print(f"\n🧪 测试 Ethereum 钱包认证流程 - 地址: {ethereum_wallet.address}")
        
        # 步骤 1: 获取 nonce
        nonce_response = client.post(
            "/accounts/nonce",
            json={"address": ethereum_wallet.address}
        )
        
        assert nonce_response.status_code == 200
        nonce_data = nonce_response.json()
        assert nonce_data["success"] is True
        
        nonce_message = nonce_data["nonce"]
        
        # 步骤 2: 签名和认证
        signature = ethereum_wallet.sign_message(nonce_message)
        
        auth_response = client.post(
            "/accounts/auth",
            json={
                "address": ethereum_wallet.address,
                "signature": signature,
                "message": nonce_message,
                "source": "metamask"
            }
        )
        
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is True
        assert "token" in auth_data
        
        print(f"✅ Ethereum 钱包认证成功")
    
    def test_nonce_expiration(self, client, test_wallet):
        """
        测试 nonce 过期机制
        """
        print("\n🧪 测试 Nonce 过期机制")
        
        # 获取 nonce
        nonce_response = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        assert nonce_response.status_code == 200
        nonce_data = nonce_response.json()
        nonce_message = nonce_data["nonce"]
        
        # 模拟 nonce 过期（通过直接修改服务中的过期时间）
        # 这里我们通过等待一段时间来模拟，实际测试中可以 mock 时间
        
        # 使用过期的 nonce 进行认证
        signature = test_wallet.sign_message(nonce_message)
        
        # 由于当前实现是5分钟过期，这里我们测试立即使用是否有效
        auth_response = client.post(
            "/accounts/auth",
            json={
                "address": test_wallet.address,
                "signature": signature,
                "message": nonce_message,
                "source": "polkadot-js"
            }
        )
        
        # 立即使用应该成功
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is True
        
        print("✅ Nonce 正常使用测试通过")
    
    def test_invalid_signature(self, client, test_wallet):
        """
        测试无效签名的处理
        """
        print("\n🧪 测试无效签名处理")
        
        # 获取 nonce
        nonce_response = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        nonce_data = nonce_response.json()
        nonce_message = nonce_data["nonce"]
        
        # 使用无效签名
        invalid_signature = "0xinvalid_signature"
        
        auth_response = client.post(
            "/accounts/auth",
            json={
                "address": test_wallet.address,
                "signature": invalid_signature,
                "message": nonce_message,
                "source": "polkadot-js"
            }
        )
        
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is False
        assert "message" in auth_data
        
        print("✅ 无效签名正确被拒绝")
    
    def test_message_tampering(self, client, test_wallet):
        """
        测试消息篡改的检测
        """
        print("\n🧪 测试消息篡改检测")
        
        # 获取 nonce
        nonce_response = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        nonce_data = nonce_response.json()
        nonce_message = nonce_data["nonce"]
        
        # 对正确的消息签名
        signature = test_wallet.sign_message(nonce_message)
        
        # 但提交时使用不同的消息
        tampered_message = nonce_message + " TAMPERED"
        
        auth_response = client.post(
            "/accounts/auth",
            json={
                "address": test_wallet.address,
                "signature": signature,
                "message": tampered_message,
                "source": "polkadot-js"
            }
        )
        
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is False
        
        print("✅ 消息篡改正确被检测")
    
    def test_multiple_nonce_requests(self, client, test_wallet):
        """
        测试多次 nonce 请求的处理
        """
        print("\n🧪 测试多次 Nonce 请求")
        
        # 第一次请求 nonce
        nonce_response1 = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        nonce_data1 = nonce_response1.json()
        nonce_message1 = nonce_data1["nonce"]
        
        # 第二次请求 nonce（应该覆盖第一次）
        nonce_response2 = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        nonce_data2 = nonce_response2.json()
        nonce_message2 = nonce_data2["nonce"]
        
        # 两个 nonce 应该不同
        assert nonce_message1 != nonce_message2
        
        # 使用第一个 nonce 应该失败（已被覆盖）
        signature1 = test_wallet.sign_message(nonce_message1)
        auth_response1 = client.post(
            "/accounts/auth",
            json={
                "address": test_wallet.address,
                "signature": signature1,
                "message": nonce_message1,
                "source": "polkadot-js"
            }
        )
        
        auth_data1 = auth_response1.json()
        assert auth_data1["success"] is False
        
        # 使用第二个 nonce 应该成功
        signature2 = test_wallet.sign_message(nonce_message2)
        auth_response2 = client.post(
            "/accounts/auth",
            json={
                "address": test_wallet.address,
                "signature": signature2,
                "message": nonce_message2,
                "source": "polkadot-js"
            }
        )
        
        auth_data2 = auth_response2.json()
        assert auth_data2["success"] is True
        
        print("✅ 多次 Nonce 请求处理正确")
    
    def test_concurrent_auth_attempts(self, client, test_wallet):
        """
        测试并发认证尝试
        """
        print("\n🧪 测试并发认证尝试")
        
        # 获取 nonce
        nonce_response = client.post(
            "/accounts/nonce",
            json={"address": test_wallet.address}
        )
        
        nonce_data = nonce_response.json()
        nonce_message = nonce_data["nonce"]
        signature = test_wallet.sign_message(nonce_message)
        
        # 模拟并发认证（同一个签名被多次提交）
        auth_requests = []
        for i in range(3):
            auth_response = client.post(
                "/accounts/auth",
                json={
                    "address": test_wallet.address,
                    "signature": signature,
                    "message": nonce_message,
                    "source": "polkadot-js"
                }
            )
            auth_requests.append(auth_response)
        
        # 第一次应该成功
        assert auth_requests[0].json()["success"] is True
        
        # 后续的可能失败（nonce 已被使用）
        # 这取决于具体的实现逻辑
        
        print("✅ 并发认证测试完成")
    
    def test_invalid_address_format(self, client):
        """
        测试无效地址格式的处理
        """
        print("\n🧪 测试无效地址格式")
        
        invalid_addresses = [
            "",  # 空地址
            "invalid",  # 无效格式
            "0x",  # 不完整的以太坊地址
            "5" * 100,  # 过长的地址
        ]
        
        for invalid_address in invalid_addresses:
            nonce_response = client.post(
                "/accounts/nonce",
                json={"address": invalid_address}
            )
            
            # 应该能处理无效地址（可能返回错误或生成 nonce）
            assert nonce_response.status_code in [200, 400, 422]
        
        print("✅ 无效地址格式处理测试完成")


class TestApiAuthEdgeCases:
    """API 认证边界条件测试"""
    
    @pytest.fixture(scope="class")
    def app(self):
        return create_app()
    
    @pytest.fixture(scope="class") 
    def client(self, app):
        return TestClient(app)
    
    def test_missing_authorization_header(self, client):
        """测试缺少认证头的请求"""
        response = client.get("/dashboard/overview")
        assert response.status_code == 403  # Forbidden or 401 Unauthorized
    
    def test_malformed_authorization_header(self, client):
        """测试格式错误的认证头"""
        headers_to_test = [
            {"Authorization": "InvalidFormat"},
            {"Authorization": "Bearer"},  # 缺少 token
            {"Authorization": "Basic token123"},  # 错误的类型
            {"Authorization": "Bearer "},  # 空 token
        ]
        
        for headers in headers_to_test:
            response = client.get("/dashboard/overview", headers=headers)
            assert response.status_code in [401, 403, 422]
    
    def test_expired_token_simulation(self, client):
        """模拟过期 token 的处理"""
        # 使用明显无效的 token
        invalid_token = "expired_or_invalid_token"
        
        response = client.get(
            "/dashboard/overview",
            headers={"Authorization": f"Bearer {invalid_token}"}
        )
        
        assert response.status_code == 401
        error_data = response.json()
        assert "detail" in error_data


if __name__ == "__main__":
    # 单独运行此测试文件
    pytest.main([__file__, "-v", "--tb=short"])