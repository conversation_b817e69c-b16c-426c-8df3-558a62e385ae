#!/usr/bin/env python3
"""
运行 API 认证集成测试的独立脚本

这个脚本可以独立运行 API 认证测试，而不需要启动完整的测试套件。
"""

import sys
import os
import subprocess
import time

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

def check_dependencies():
    """检查测试依赖"""
    try:
        import pytest
        import httpx
        from fastapi.testclient import TestClient
        print("✅ 所有测试依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少测试依赖: {e}")
        print("请安装: pip install pytest httpx")
        return False

def run_auth_tests():
    """运行认证集成测试"""
    
    print("🚀 开始运行 API 认证集成测试")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 运行测试
    test_file = os.path.join(os.path.dirname(__file__), 'test_api_auth.py')
    
    try:
        print(f"📋 运行测试文件: {test_file}")
        
        # 使用 pytest 运行测试
        cmd = [
            sys.executable, "-m", "pytest", 
            test_file,
            "-v",  # 详细输出
            "--tb=short",  # 简短的错误回溯
            "--no-header",  # 不显示头部信息
            "-s",  # 不捕获输出，显示 print 语句
        ]
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        print("\n" + "=" * 60)
        if result.returncode == 0:
            print("🎉 所有测试通过！")
        else:
            print("❌ 部分测试失败")
            
        return result.returncode == 0
        
    except FileNotFoundError:
        print("❌ 找不到 pytest，请安装: pip install pytest")
        return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def main():
    """主函数"""
    
    print("🧪 TaoHash API 认证集成测试")
    print("测试内容:")
    print("- Bittensor 钱包认证流程")
    print("- Token 生成和验证")
    print("- 会话管理")
    print("- 安全验证机制")
    print("- 边界条件和错误处理")
    print()
    
    success = run_auth_tests()
    
    if success:
        print("\n✅ 集成测试完成！")
        print("\n📋 测试覆盖范围:")
        print("  ✓ 完整的钱包认证流程")
        print("  ✓ Substrate 和 Ethereum 钱包支持")
        print("  ✓ Nonce 生成和过期机制")
        print("  ✓ 签名验证和消息完整性")
        print("  ✓ Token 使用和会话管理")
        print("  ✓ 错误处理和边界条件")
        print("\n🔧 注意事项:")
        print("  • 当前使用模拟签名验证（适用于开发和测试）")
        print("  • 生产环境需要实现真正的密码学签名验证")
        print("  • 建议在 CI/CD 中集成这些测试")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()