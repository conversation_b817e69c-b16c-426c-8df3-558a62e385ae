# 集成测试

本目录包含需要外部服务（如Redis）的集成测试。

## 快速开始

### 在CI环境中运行
集成测试在CI中自动运行，Docker Compose服务由CI管理。

### 本地开发运行

#### 方法1: 使用Docker Compose（推荐）
```bash
cd hash/tests/integration_tests

# 启动服务
docker compose up -d

# 等待Redis就绪
docker compose exec redis redis-cli ping

# 运行测试
python run_integration_tests.py

# 清理服务
docker compose down -v
```

#### 方法2: 使用测试脚本（简化版）
```bash
cd hash/tests/integration_tests

# 启动服务并运行测试
python run_integration_tests.py

# 注意：现在需要手动管理Docker Compose服务
```

### 运行特定测试
```bash
python run_integration_tests.py --filter test_storage_redis
```

## 测试脚本选项

- `--filter <测试名>`: 只运行匹配的测试

## 依赖服务

当前集成测试需要以下服务：

### Redis
- **用途**: 测试Redis存储功能
- **端口**: 6379
- **配置**: 见 `docker-compose.yml`

## 测试结构

### 现有测试
- `test_storage.py`: 存储相关的集成测试
- `test_pricing.py`: 价格API集成测试
- `docker-compose.yml`: 测试所需的服务定义
- `run_integration_tests.py`: 测试运行脚本

### 🆕 新增: API认证集成测试

#### `test_auth_core.py` - 核心逻辑测试 (推荐)
**无依赖** - 测试核心认证逻辑，可独立运行：
```bash
python tests/integration_tests/test_auth_core.py
```

**测试内容:**
- ✅ Bittensor钱包认证流程
- ✅ Substrate和Ethereum钱包支持  
- ✅ Nonce生成和验证机制
- ✅ 签名验证 (模拟实现)
- ✅ Token生成和会话管理
- ✅ 错误处理和边界条件
- ✅ 消息完整性检测

#### `test_api_auth.py` - 完整API测试
**需要依赖:** pytest, httpx, FastAPI
```bash
pytest tests/integration_tests/test_api_auth.py -v
```

#### `test_auth_simple.py` - 简化API测试  
**需要依赖:** FastAPI TestClient
```bash
python tests/integration_tests/test_auth_simple.py
```

#### `test_account_service.py` - 业务逻辑测试
**需要依赖:** 项目依赖包
```bash 
python tests/integration_tests/test_account_service.py
```

### 钱包认证流程测试

测试覆盖完整的Web3钱包登录流程：

1. **获取随机数**: `POST /accounts/nonce`
2. **钱包签名**: 使用钱包对消息签名
3. **提交认证**: `POST /accounts/auth` 获取token
4. **使用token**: `Authorization: Bearer <token>`
5. **会话管理**: 断开连接和token失效

## 故障排除

### Redis连接失败
确保Docker服务正在运行：
```bash
docker ps
```

检查Redis容器状态：
```bash
docker logs taohash-test-redis
```

### 手动测试Redis连接
```bash
redis-cli -h localhost -p 6379 ping
```

### 重置环境
```bash
docker compose down -v
docker system prune -f
docker compose up -d
python run_integration_tests.py
```

## 开发指南

### 添加新的集成测试

1. 在测试函数中添加服务可用性检查
2. 如果需要新服务，更新 `docker-compose.yml`
3. 在CI配置中添加相应的健康检查

### 测试模式

集成测试可以在两种模式下运行：

1. **CI模式**: 在CI环境中自动运行
   - Docker Compose服务由CI管理
   - 自动启动/停止服务
   - 包含健康检查
   - 自动清理

2. **本地开发模式**: 手动管理服务
   - 需要手动启动Docker Compose服务
   - 测试会被跳过如果服务不可用

### 环境变量

- `SKIP_INTEGRATION_TESTS=1`: 跳过所有集成测试
- `SKIP_INTEGRATION_TESTS=0`: 运行集成测试（脚本自动设置）