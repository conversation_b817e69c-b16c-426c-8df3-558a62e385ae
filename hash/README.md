<div align="center">

# **TAO Hash** ![Subnet 14](https://img.shields.io/badge/Subnet-14_%CE%BE-blue)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/latent-to/taohash)

</div>

TAO Hash 是一个 Bittensor 子网，用于激励和去中心化工作量证明（PoW）比特币挖矿算力的生产、租赁和交换。验证者通过基于产生的份额价值发布权重来评估矿工，而矿工贡献算力并对算力、哈希价格和 Alpha 排放进行投机。实际上，Alpha 会自动与比特币算力进行交换。

该架构设计为可扩展到其他具有类似验证矿工性能能力的可挖矿项目。

---
- [激励机制设计](#incentive-design)
- [系统要求](#requirements)
  - [矿工要求](#miner-requirements)
  - [验证者要求](#validator-requirements)
- [安装指南](#installation)
  - [通用设置](#common-setup)
  - [矿工专用设置](#miner-specific-setup)
  - [验证者专用设置](#validator-specific-setup)
- [参与项目](#get-involved)
---

# 激励机制设计
核心激励机制通过一个市场来协调矿工，在该市场中，比特币算力与比特币和链上奖励（Alpha）进行交换。所有矿工都向统一的子网池贡献算力，验证者根据他们产生的份额价值来评估矿工。

![TAO Hash 架构图](docs/images/incentive-design.png)

# 系统要求

## 矿工要求
运行 TAO Hash 矿工，您需要：
- 一个 Bittensor 钱包
- 比特币挖矿硬件（ASIC、GPU 等）或远程算力访问权限（NiceHash、MiningRigRentals）
- Python 3.9 或更高版本

### 可选（用于矿工代理）：
- Docker 和 Docker Compose

## 验证者要求
运行 TAO Hash 验证者，您需要：
- 一个 Bittensor 钱包
- 子网代理凭证（由子网维护者提供）
- Python 3.9 或更高版本环境

# 安装指南

## 通用设置
以下步骤适用于矿工和验证者：

1.  **克隆仓库：**
    ```bash
    git clone [https://github.com/latent-to/taohash.git](https://github.com/latent-to/taohash.git)
    cd taohash
    ```

2.  **设置并激活 Python 虚拟环境：**
    ```bash
    python3 -m venv venv
    source venv/bin/activate
    ```

3.  **升级 pip：**
    ```bash
    pip install --upgrade pip
    ```

4.  **安装 TAO Hash 包：**
    ```bash
    pip install -e .
    ```

## 矿工专用设置
完成通用设置后，开始挖矿的最简单方法是：

### 快速开始（直接挖矿）
1. **获取挖矿池凭证**：运行 [`miner.py`](taohash/miner/miner.py) 脚本来获取您的池信息：
   ```bash
   python taohash/miner/miner.py --subtensor.network finney --wallet.name WALLET_NAME --wallet.hotkey WALLET_HOTKEY --btc_address YOUR_BTC_ADDRESS
   ```
   此脚本将显示您的唯一工作凭证和池连接详情。用户名格式将为 `YOUR_BTC_ADDRESS.WORKER_SUFFIX`。

2. **配置您的矿工**：使用步骤 1 中的凭证将您的挖矿硬件直接指向子网池。

3. **监控您的性能**：在 [https://taohash.com/leaderboard](https://taohash.com/leaderboard) 查看您的统计数据

### 高级设置（可选）
对于最低难度设置和高级监控等功能：
* [设置 TAO Hash 代理](docs/running_miner.md#optional-proxy-setup)
* [运行矿工脚本进行池管理](docs/running_miner.md#legacy-miner-script)

完整详情请参阅 [TAO Hash 矿工设置指南](docs/running_miner.md)。

## 验证者专用设置
完成通用设置后，请按照验证者指南中的详细步骤操作：

* [从子网工作人员处获取子网代理凭证](docs/running_validator.md#1-get-subnet-proxy-credentials)
* [配置您的验证者（`.env` 文件）](docs/running_validator.md#4-configuration)
* [运行验证者（推荐使用 PM2）](docs/running_validator.md#5-running-the-validator)

有关设置和运行验证者的完整分步说明，请参阅 [TAO Hash 验证者设置](docs/running_validator.md)。

# 参与项目

- 在 [Bittensor Discord](https://discord.com/invite/bittensor) 的 Subnet 14 频道中参与讨论。
- 查看 [Bittensor 文档](https://docs.bittensor.com/) 了解运行子网和节点的一般信息。
- 欢迎贡献！详情请参阅仓库的贡献指南。

---
**完整指南：**
- [TAO Hash 矿工设置指南](docs/running_miner.md)
- [TAO Hash 验证者设置](docs/running_validator.md) 
