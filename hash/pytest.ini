[tool:pytest]
# pytest configuration for TAO Hash API tests

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    auth: Authentication tests
    slow: Slow running tests
    requires_docker: Tests that require Docker services

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Async support
asyncio_mode = auto

# Coverage options (if pytest-cov is installed)
# addopts = --cov=taohash --cov-report=html --cov-report=term-missing

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:munch.*
    ignore::pytest.PytestUnhandledCoroutineWarning
    ignore::pytest.PytestReturnNotNoneWarning
