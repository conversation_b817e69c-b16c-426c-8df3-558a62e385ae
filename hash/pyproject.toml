[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "taohash"
version = "0.3.0" 
description = "TAO Hash Mining Rental Subnet"
readme = "README.md"
requires-python = ">=3.9"
license = "MIT"
authors = [
    {name = "Latent Holdings", email = "<EMAIL>"}
]
dependencies = [
    # 基础运行时依赖（不包含 bittensor）
    "ratelimit==2.2.1",
    "cachetools==5.3.1",
    "tabulate~=0.9.0",
    "python-dotenv>=0.21.0",
    "backoff==2.2.1",
    "redis==5.2.1",
    "httpx==0.25.2",
]
[project.optional-dependencies]
# 开发依赖
dev = [
    "pytest==8.3.5",
    "pytest-cov==6.1.1",
    "responses==0.25.7",
    "ruff==0.11.7"
]
# API 运行时依赖
api = [
    "fastapi~=0.110.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "web3>=6.15.1",
    "eth-account>=0.9.0",
    "substrate-interface>=1.7.9",
    "scalecodec>=1.2.11"
]
# Bittensor 相关依赖（可选）
bt = [
    "bittensor==9.4.0",
    "bittensor-commit-reveal",
    # 下列依赖用于提升兼容性和可重复构建
    "typing-extensions>=4.6.0",
    "numpy>=1.21.0",
    "colorama>=0.4.6",
    "rich>=13.3.5",
    "PyYAML>=6.0",
    "munch>=2.5.0",
    "async-substrate-interface>=0.1.0",
    "bittensor-wallet>=1.1.0",
    "python-statemachine>=2.0.0",
    "packaging>=21.0",
    "certifi>=2021.10.8",
    "cryptography>=3.4.8",
    "requests>=2.25.1",
]

[tool.flit.metadata]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Build Tools",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

[project.urls]
Homepage = "https://github.com/latent-to/taohash"
Repository = "https://github.com/latent-to/taohash.git"

[tool.setuptools]
packages = ["taohash"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"