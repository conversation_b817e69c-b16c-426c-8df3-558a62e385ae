#!/usr/bin/env python3
"""
快速本地集成测试脚本
"""

import subprocess
import sys
import os

def run_cmd(cmd, description):
    """运行命令并显示结果"""
    print(f"\n📋 {description}")
    print(f"命令: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"✅ 成功")
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
    else:
        print(f"❌ 失败 (退出码: {result.returncode})")
        if result.stderr:
            print(f"错误: {result.stderr.strip()}")
    
    return result.returncode == 0

def main():
    print("🚀 TaoHash 快速集成测试")
    print("=" * 50)
    
    # 1. 检查 Docker
    if not run_cmd("docker --version", "检查 Docker"):
        print("❌ Docker 未安装或不可用")
        return False
    
    # 2. 启动 Redis
    if not run_cmd("docker-compose up -d", "启动 Redis 服务"):
        print("❌ 无法启动 Redis")
        return False
    
    # 3. 等待 Redis 就绪
    if not run_cmd("timeout 30 bash -c 'until docker-compose exec redis redis-cli ping; do sleep 1; done'", "等待 Redis 就绪"):
        print("❌ Redis 未就绪")
        return False
    
    # 4. 运行核心认证测试
    if not run_cmd("python test_auth_core.py", "运行核心认证测试"):
        print("❌ 核心认证测试失败")
        return False
    
    # 5. 运行基础集成测试 (如果pytest可用)
    print("\n📋 尝试运行基础集成测试")
    try:
        result = subprocess.run([sys.executable, "-m", "pytest", "test_storage.py", "test_pricing.py", "-v"], 
                               cwd=os.getcwd(), capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ 基础集成测试通过")
        else:
            print("⚠️ 基础集成测试失败，可能缺少依赖")
    except:
        print("⚠️ 无法运行 pytest，跳过基础集成测试")
    
    # 6. 清理
    run_cmd("docker-compose down -v", "清理 Docker 服务")
    
    print("\n🎉 快速测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)