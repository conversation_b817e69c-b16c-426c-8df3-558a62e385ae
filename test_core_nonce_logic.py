#!/usr/bin/env python3
"""
测试核心 nonce 逻辑（无 FastAPI 依赖）
"""

import sys
import os
import asyncio
import hashlib

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from taohash.api.services.account_service import AccountService
    print("✅ AccountService 导入成功")
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 依赖缺失: {e}")
    DEPENDENCIES_AVAILABLE = False


async def test_nonce_logic():
    """测试 nonce 逻辑"""
    if not DEPENDENCIES_AVAILABLE:
        print("⚠️ 跳过测试：缺少依赖")
        return False
    
    print("\n🧪 测试 AccountService 核心逻辑")
    
    # 创建两个服务实例（模拟不同请求）
    service1 = AccountService()
    service2 = AccountService()
    
    test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
    
    # 第一个实例生成 nonce
    print("📋 实例1: 生成 nonce")
    nonce_message = await service1.generate_nonce(test_address)
    print(f"✅ Nonce: {nonce_message[:50]}...")
    
    # 检查 nonce 存储
    assert test_address in service1.nonces, "service1 应该有 nonce"
    print("✅ service1 存储了 nonce")
    
    # 第二个实例（模拟不同请求）应该没有这个 nonce
    print("🔍 检查实例隔离问题")
    if test_address in service2.nonces:
        print("❌ 问题确认：service2 也有 nonce（不应该！）")
        return False
    else:
        print("✅ 问题确认：service2 没有 nonce（这就是 API 测试失败的原因！）")
    
    # 现在测试签名（使用正确的实例）
    print("🔐 使用正确实例进行认证")
    signature_data = f"{test_address}_*_{nonce_message}"
    signature_hash = hashlib.sha256(signature_data.encode()).hexdigest()
    signature = f"0x{signature_hash}"
    
    # 用有 nonce 的实例进行认证
    auth_result = await service1.authenticate_wallet(
        test_address, signature, nonce_message, "polkadot-js"
    )
    
    assert auth_result.success, f"认证应该成功: {auth_result.message}"
    print("✅ 使用正确实例认证成功")
    
    # 用没有 nonce 的实例进行认证（模拟 API 测试失败场景）
    print("❌ 尝试用错误实例认证（模拟 API 失败）")
    try:
        auth_result2 = await service2.authenticate_wallet(
            test_address, signature, nonce_message, "polkadot-js"
        )
        if not auth_result2.success:
            print(f"✅ 预期失败: {auth_result2.message}")
        else:
            print("❌ 意外成功")
    except Exception as e:
        print(f"✅ 预期异常: {e}")
    
    return True


def main():
    print("🔍 核心 Nonce 逻辑分析")
    print("=" * 40)
    
    if asyncio.run(test_nonce_logic()):
        print("\n📋 分析结论:")
        print("❌ 问题根因: 每次 API 请求创建新的 AccountService 实例")
        print("✅ 解决方案: 使用单例模式共享实例")
        print("🔧 修复状态: 代码已修复，等待测试验证")
    else:
        print("❌ 测试失败")


if __name__ == "__main__":
    main()