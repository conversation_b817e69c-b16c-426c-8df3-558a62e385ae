:root {
  --primary: #4ECDC4;
  --secondary: #52D9D0;
  --dark: #0A1628;
  --dark-accent: #162236;
  --light-text: #FFFFFF;
  --muted-text: #B8C5D6;
  --card-bg: #162236;
  --hover: #1A2940;
  --border: rgba(78, 205, 196, 0.15);
  --danger: #FF6B6B;
  --warning: #FFE66D;
  --accent: #4ECDC4;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: var(--dark);
  color: var(--light-text);
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.logo-title {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.analytics-link {
  padding: 8px 16px;
  background: rgba(0, 185, 185, 0.1);
  color: var(--primary);
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid rgba(0, 185, 185, 0.2);
}

.analytics-link:hover {
  background: rgba(0, 185, 185, 0.2);
  transform: translateY(-1px);
}

h1 {
  font-size: 32px;
  color: var(--light-text);
  font-weight: 700;
  letter-spacing: -0.5px;
  position: relative;
}

.tao-letter {
  color: var(--primary);
  font-weight: 700;
}

.subtitle {
  font-size: 18px;
  color: var(--muted-text);
  opacity: 0.8;
  font-weight: 500;
  margin-left: 4px;
}

/* Pools section */
.pools-section {
  margin-bottom: 2rem;
}

.pools-section h2 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--muted-text);
}

.pools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.pool-card {
  background: var(--card-bg);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  border: 1px solid var(--border);
}

.pool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.pool-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--light-text);
}

.pool-status {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.pool-status.active {
  background-color: rgba(26, 218, 187, 0.2);
  color: var(--secondary);
}

.pool-status.inactive {
  background-color: rgba(128, 128, 128, 0.2);
  color: #888;
}

.pool-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.pool-detail {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  font-size: 0.875rem;
}

.detail-label {
  color: var(--muted-text);
  min-width: 80px;
}

.detail-value {
  color: var(--light-text);
  font-family: monospace;
  background-color: var(--hover);
  padding: 4px 8px;
  border-radius: 4px;
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--primary);
}

.pool-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.pool-stat {
  text-align: center;
}

.pool-stat .stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
}

.pool-stat .stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--muted-text);
  margin-top: 0.25rem;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transition: all 0.3s ease;
  border: 1px solid var(--border);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.2);
  border-color: var(--primary);
}

.stat-card h3 {
  font-size: 14px;
  color: var(--muted-text);
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stat-card .value {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary);
}

.chart-container {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  border: 1px solid var(--border);
}

.chart-container h2 {
  margin-bottom: 15px;
  font-size: 18px;
  color: var(--muted-text);
}

table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--card-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  border: 1px solid var(--border);
}

thead th {
  background-color: var(--dark);
  color: var(--primary);
  padding: 15px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid var(--border);
}

tbody tr:nth-child(even) {
  background-color: var(--hover);
}

tbody tr:hover {
  background-color: rgba(0, 185, 185, 0.05);
}

td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border);
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.badge-success {
  background-color: rgba(26, 218, 187, 0.2);
  color: var(--secondary);
}

.badge-danger {
  background-color: rgba(255, 76, 91, 0.2);
  color: var(--danger);
}

footer {
  text-align: center;
  margin-top: 30px;
  color: var(--muted-text);
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.refresh-time {
  font-size: 12px;
  color: var(--muted-text);
  text-align: right;
}

@media (max-width: 768px) {
  .summary-stats {
    grid-template-columns: 1fr;
  }
  .charts-row {
    flex-direction: column;
  }
  .dashboard-bottom-section {
    flex-direction: column !important;
  }
}

/* Rejection tooltip */
.rejection-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  margin-bottom: 4px;
}

.rejection-tooltip div {
  padding: 2px 0;
  color: var(--muted-text);
}

td:hover .rejection-tooltip {
  visibility: visible;
  opacity: 1;
} 