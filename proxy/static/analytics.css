/* Analytics Page Styles */

.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: block;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary);
}

.card {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.card h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
}

.query-form {
  margin-top: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.form-control {
  width: 100%;
  padding: 0.625rem 1rem;
  background: var(--taohash-bg-secondary);
  border: 1px solid var(--taohash-border);
  border-radius: 6px;
  color: var(--taohash-text-primary);
  font-size: 0.875rem;
}

.form-control:focus {
  outline: none;
  border-color: var(--taohash-accent-primary);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn {
  padding: 0.625rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: var(--primary);
  color: var(--bg);
}

.btn-primary:hover {
  background: #15a085;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--card-bg);
  color: var(--text);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.05);
}

.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.results-header h2 {
  margin: 0;
}

.export-buttons {
  display: flex;
  gap: 0.5rem;
}

.results-summary {
  display: flex;
  gap: 2rem;
  padding: 1rem;
  background: rgba(78, 205, 196, 0.05);
  border-radius: 6px;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.summary-value {
  font-weight: 600;
  color: var(--primary);
}

.table-container {
  overflow-x: auto;
  border: 1px solid var(--border);
  border-radius: 8px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: rgba(255, 255, 255, 0.02);
  padding: 0.75rem;
  text-align: left;
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border);
}

td {
  padding: 0.75rem;
  font-size: 0.875rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

tr:hover {
  background: rgba(26, 218, 187, 0.02);
}

.mono {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.8125rem;
}

.status-accepted {
  color: var(--secondary);
}

.status-rejected {
  color: #ff4c5b;
}

.luck-high {
  color: var(--taohash-warning);
  font-weight: 600;
}

.luck-normal {
  color: var(--taohash-success);
}

.luck-low {
  color: var(--taohash-text-muted);
}

.load-more {
  text-align: center;
  margin-top: 1.5rem;
}

.loading {
  text-align: center;
  color: var(--text-secondary);
  padding: 2rem;
}

.error {
  text-align: center;
  color: #ff4c5b;
  padding: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  .stats-summary {
    grid-template-columns: 1fr;
  }
  
  .results-summary {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .results-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .table-container {
    font-size: 0.75rem;
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--taohash-bg-card);
  border: 1px solid var(--taohash-border);
  border-radius: 8px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
}

.modal-content h3 {
  margin-top: 0;
  color: var(--text-primary);
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1.5rem 0;
}

.export-options label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
}

.export-options input[type="radio"] {
  cursor: pointer;
}
  
  th, td {
    padding: 0.5rem;
  }
}