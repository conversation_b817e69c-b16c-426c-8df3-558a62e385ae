<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TaoHash Analytics</title>
  <link rel="stylesheet" href="/static/common/taohash-design-system.css">
  <link rel="stylesheet" href="/static/styles.css">
  <link rel="stylesheet" href="/static/analytics.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
</head>
<body class="taohash-bg-pattern">
  <div class="taohash-container">
    <nav class="taohash-nav">
      <div class="taohash-logo">
        <img src="/static/images/taohash-logo.png" alt="TaoHash" style="height: 40px; width: auto;">
      </div>
      <div class="taohash-nav-links">
        <a href="/" class="taohash-nav-link">Dashboard</a>
        <a href="/analytics" class="taohash-nav-link active">Analytics</a>
      </div>
    </nav>

    <div style="text-align: center; margin: 3rem 0;">
      <h1 class="taohash-heading-1" style="margin-bottom: 1rem;">
        📊 Analytics Dashboard
      </h1>
      <p class="taohash-body" style="font-size: 1.25rem;">
        Deep dive
      </p>
    </div>

    <!-- Pool Statistics Summary -->
    <div class="taohash-grid taohash-grid-4" style="margin-bottom: 3rem;">
      <div class="taohash-metric-card taohash-animate">
        <div class="taohash-metric-value" id="poolHashrate">-</div>
        <div class="taohash-metric-label">24h Pool Hashrate</div>
      </div>
      <div class="taohash-metric-card taohash-animate" style="animation-delay: 0.1s;">
        <div class="taohash-metric-value" id="activeWorkers">-</div>
        <div class="taohash-metric-label">Active Workers</div>
      </div>
      <div class="taohash-metric-card taohash-animate" style="animation-delay: 0.2s;">
        <div class="taohash-metric-value" id="totalShares">-</div>
        <div class="taohash-metric-label">24h Shares</div>
      </div>
      <div class="taohash-metric-card taohash-animate" style="animation-delay: 0.3s;">
        <div class="taohash-metric-value" id="bestShare">-</div>
        <div class="taohash-metric-label">Best Share (24h)</div>
      </div>
    </div>

    <!-- Share Query Section -->
    <section class="taohash-card" style="margin-bottom: 3rem;">
      <h2 class="taohash-heading-2" style="margin-bottom: 2rem;">Query Share History</h2>
      <div class="query-form">
        <div class="form-row">
          <div class="form-group">
            <label for="workerFilter">Worker Name</label>
            <input type="text" id="workerFilter" class="form-control" placeholder="Leave empty for all workers">
          </div>
          <div class="form-group">
            <label for="timeRange">Time Range</label>
            <select id="timeRange" class="form-control">
              <option value="1h">Last Hour</option>
              <option value="6h">Last 6 Hours</option>
              <option value="24h" selected>Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>
        </div>

        <div id="customTimeRange" class="form-row" style="display: none;">
          <div class="form-group">
            <label for="startTime">Start Time</label>
            <input type="datetime-local" id="startTime" class="form-control">
          </div>
          <div class="form-group">
            <label for="endTime">End Time</label>
            <input type="datetime-local" id="endTime" class="form-control">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="minDifficulty">Min Actual Difficulty</label>
            <input type="number" id="minDifficulty" class="form-control" placeholder="Optional: e.g., 1000000">
          </div>
          <div class="form-group">
            <label for="shareStatus">Share Status</label>
            <select id="shareStatus" class="form-control">
              <option value="all">All Shares</option>
            </select>
          </div>
        </div>

        <div class="button-group" style="margin-top: 2rem;">
          <button id="queryShares" class="taohash-button taohash-button-primary">Query Shares</button>
          <button id="resetForm" class="taohash-button taohash-button-secondary">Reset</button>
        </div>
      </div>
    </section>

    <!-- Results Section -->
    <section id="resultsSection" class="taohash-card" style="display: none; margin-bottom: 3rem;">
      <div class="results-header">
        <h2 class="taohash-heading-2">Share History</h2>
        <div class="export-buttons">
          <button id="exportCsv" class="btn btn-small">Export CSV</button>
          <button id="exportJson" class="btn btn-small">Export JSON</button>
        </div>
      </div>
      
      <div id="resultsSummary" class="results-summary"></div>
      
      <div style="overflow-x: auto;">
        <table class="taohash-table" id="resultsTable">
          <thead>
            <tr>
              <th>Time</th>
              <th>Worker</th>
              <th>Pool Request</th>
              <th>Miner Diff</th>
              <th>Actual Diff</th>
              <th>Luck</th>
              <th>Block Hash</th>
            </tr>
          </thead>
          <tbody id="resultsBody"></tbody>
        </table>
      </div>
      
      <div id="loadMore" class="load-more" style="display: none; text-align: center; margin-top: 2rem;">
        <button id="loadMoreBtn" class="taohash-button taohash-button-secondary">Load More</button>
      </div>
    </section>

    <!-- Top Workers Section -->
    <section class="taohash-card" style="margin-bottom: 3rem;">
      <h2 class="taohash-heading-2" style="margin-bottom: 2rem;">Top Workers (24 Hours)</h2>
      <div style="overflow-x: auto;">
        <table class="taohash-table" id="workersTable">
          <thead>
            <tr>
              <th>Worker</th>
              <th>Hashrate</th>
              <th>Shares</th>
              <th>Best Share</th>
              <th>Avg Luck</th>
            </tr>
          </thead>
          <tbody id="workersBody">
            <tr><td colspan="5" class="loading">Loading...</td></tr>
          </tbody>
        </table>
      </div>
    </section>
  </div>

  <!-- Export Options Modal -->
  <div id="exportModal" class="modal" style="display: none;">
    <div class="modal-content">
      <h3>Export Options</h3>
      <p>Choose what to export:</p>
      <div class="export-options">
        <label>
          <input type="radio" name="exportOption" value="visible" checked>
          Export visible rows (<span id="visibleCount">0</span> records)
        </label>
        <label>
          <input type="radio" name="exportOption" value="all">
          Export all matching records (<span id="totalCount">0</span> records)
        </label>
      </div>
      <div class="button-group">
        <button id="confirmExport" class="btn btn-primary">Export</button>
        <button id="cancelExport" class="btn btn-secondary">Cancel</button>
      </div>
    </div>
  </div>

  <script src="/static/analytics.js"></script>
</body>
</html>