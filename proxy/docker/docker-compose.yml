services:
  haproxy:
    image: haproxy:2.8-alpine
    container_name: taohash-haproxy
    ports:
      - "${PROXY_PORT:-3331}:3331"
      - "${PROXY_PORT_HIGH:-3332}:3332"
      - "8404:8404"
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    restart: unless-stopped
    networks:
      - taohash-network

  proxy:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    deploy:
      replicas: 10
    volumes:
      - ../config:/app/config:ro
    environment:
      - PYTHONUNBUFFERED=1
      - PROXY_PORT=${PROXY_PORT:-3331}
      - PROXY_PORT_HIGH=${PROXY_PORT_HIGH:-3332}
      - CLICKHOUSE_REQUIRED=true
      - CLICKHOUSE_HOST=clickhouse
      - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-taohash123}
    restart: unless-stopped
    depends_on:
      clickhouse:
        condition: service_healthy
    networks:
      - taohash-network

  api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: taohash-api
    entrypoint: ["python", "-m", "uvicorn"]
    command: ["src.api.app:app", "--host", "0.0.0.0", "--port", "8000"]
    ports:
      - "${API_PORT:-8888}:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - API_TOKENS=${API_TOKENS}
      - REWARDS_POST_TOKEN=${REWARDS_POST_TOKEN}
      - CLICKHOUSE_HOST=clickhouse
      - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-taohash123}
      - ENABLE_REWARD_POLLING=${ENABLE_REWARD_POLLING:-false}
      - BRAIINS_API_TOKEN=${BRAIINS_API_TOKEN}
      - BRAIINS_API_URL=${BRAIINS_API_URL:-https://pool.braiins.com/accounts/rewards/json/btc}
      - REWARD_CHECK_INTERVAL=${REWARD_CHECK_INTERVAL:-3600}
    restart: unless-stopped
    depends_on:
      clickhouse:
        condition: service_healthy
    networks:
      - taohash-network

  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: taohash-clickhouse
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./clickhouse-storage.xml:/etc/clickhouse-server/config.d/storage.xml
    environment:
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-taohash123}
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET_ENDPOINT=${AWS_S3_ENDPOINT:-https://${AWS_S3_BUCKET}.s3.amazonaws.com}/clickhouse/
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 30s
    restart: unless-stopped
    networks:
      - taohash-network

  aggregator:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: taohash-aggregator
    command: ["python", "-m", "src.aggregator"]
    ports:
      - "5000:5555"
    networks:
      - taohash-network
    depends_on:
      - proxy
    restart: unless-stopped

networks:
  taohash-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  clickhouse_data:
    driver: local