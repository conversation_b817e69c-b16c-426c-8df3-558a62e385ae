# Proxy Configuration
PROXY_PORT=3331
PROXY_PORT_HIGH=3332
DASHBOARD_PORT=5000
RELOAD_API_PORT=5001

# API Configuration
API_PORT=8888
API_TOKENS=your-secure-token-here,another-token-here

# ClickHouse Configuration
CLICKHOUSE_HOST=clickhouse
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=taohash123
CLICKHOUSE_REQUIRED=true

# AWS S3 Configuration (for tiered storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-bucket-name

# Braiins reward cron-job
ENABLE_REWARD_POLLING=false
# REWARD_CHECK_INTERVAL=3600
# BRAIINS_API_TOKEN=api_token
# BRAIINS_API_URL=https://pool.braiins.com/accounts/rewards/json/btc

# Optional: Custom S3 endpoint (defaults to AWS S3)
# For MinIO: AWS_S3_ENDPOINT=http://minio:9000/${AWS_S3_BUCKET}
# For custom S3: AWS_S3_ENDPOINT=https://s3.example.com/${AWS_S3_BUCKET}
