events {
    worker_connections 1024;
}

http {
    # Basic DDoS protection
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    limit_conn_zone $binary_remote_addr zone=addr:10m;
    
    # Client body size limit
    client_body_buffer_size 1K;
    client_header_buffer_size 1k;
    client_max_body_size 1K;
    large_client_header_buffers 2 1k;
    
    # Upstream API server
    upstream api {
        server taohash-api:8000;
    }
    
    server {
        listen 80;
        server_name _;
        
        # Rate limiting
        limit_req zone=api_limit burst=20 nodelay;
        limit_conn addr 10;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        
        # API endpoints
        location /api/ {
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeout settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }
        
        # Health check endpoint (no rate limit)
        location /health {
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # Default: return 404
        location / {
            return 404;
        }
    }
}