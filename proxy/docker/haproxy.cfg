global
    maxconn 20000
    log stdout local0
    tune.ssl.default-dh-param 2048
    nbthread 4
    
defaults
    mode tcp
    timeout connect 5s
    timeout client 24h
    timeout server 24h
    timeout tunnel 24h
    log global
    option tcplog
    option dontlognull

frontend stats
    bind *:8404
    mode http
    stats enable
    stats uri /stats
    stats refresh 10s

frontend mining_normal
    bind *:3331
    maxconn 10000
    option tcplog
    default_backend mining_normal_backend

backend mining_normal_backend
    balance leastconn
    option tcp-check
    tcp-check connect
    # server-template proxy- 1-20 proxy:3331 check resolvers docker init-addr none
    server proxy-1 docker-proxy-1:3331 check resolvers docker init-addr none
    server proxy-2 docker-proxy-2:3331 check resolvers docker init-addr none
    server proxy-3 docker-proxy-3:3331 check resolvers docker init-addr none
    server proxy-4 docker-proxy-4:3331 check resolvers docker init-addr none
    server proxy-5 docker-proxy-5:3331 check resolvers docker init-addr none
    server proxy-6 docker-proxy-6:3331 check resolvers docker init-addr none
    server proxy-7 docker-proxy-7:3331 check resolvers docker init-addr none
    server proxy-8 docker-proxy-8:3331 check resolvers docker init-addr none
    server proxy-9 docker-proxy-9:3331 check resolvers docker init-addr none
    server proxy-10 docker-proxy-10:3331 check resolvers docker init-addr none
    server proxy-11 docker-proxy-11:3331 check resolvers docker init-addr none
    server proxy-12 docker-proxy-12:3331 check resolvers docker init-addr none

frontend mining_high
    bind *:3332
    maxconn 10000
    option tcplog
    default_backend mining_high_backend

backend mining_high_backend
    balance leastconn
    option tcp-check
    tcp-check connect
    # server-template proxy- 1-20 proxy:3332 check resolvers docker init-addr none
    server proxy-1 docker-proxy-1:3332 check resolvers docker init-addr none
    server proxy-2 docker-proxy-2:3332 check resolvers docker init-addr none
    server proxy-3 docker-proxy-3:3332 check resolvers docker init-addr none
    server proxy-4 docker-proxy-4:3332 check resolvers docker init-addr none
    server proxy-5 docker-proxy-5:3332 check resolvers docker init-addr none
    server proxy-6 docker-proxy-6:3332 check resolvers docker init-addr none
    server proxy-7 docker-proxy-7:3332 check resolvers docker init-addr none
    server proxy-8 docker-proxy-8:3332 check resolvers docker init-addr none
    server proxy-9 docker-proxy-9:3332 check resolvers docker init-addr none
    server proxy-10 docker-proxy-10:3332 check resolvers docker init-addr none
    server proxy-11 docker-proxy-11:3332 check resolvers docker init-addr none
    server proxy-12 docker-proxy-12:3332 check resolvers docker init-addr none

resolvers docker
    nameserver dns 127.0.0.11:53
    accepted_payload_size 8192
    resolve_retries 3
    timeout resolve 1s
    timeout retry 1s
    hold other 10s
    hold refused 10s
    hold nx 10s
    hold timeout 10s
    hold valid 10s
