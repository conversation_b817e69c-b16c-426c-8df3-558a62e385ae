# Woodpecker CI - Lint and Test Pipeline

when:
  - branch: main
    event: [push,pull_request]

volumes:
  # 定义一个持久化的 volume 来缓存 bun 的依赖
  bun_cache:
    driver: local

steps:
  - name: all-tests
    image: coinflow/dind-bun
    # network_mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # 将 bun 缓存目录挂载到持久化 volume
      # 这会使不同 pipeline run 之间的依赖安装速度大大加快
      - bun_cache:/root/.bun/install/cache
    environment:
      NODE_ENV: test
      # 明确指定 bun 的缓存目录，确保 bun 使用我们挂载的 volume
      BUN_INSTALL_CACHE_DIR: /root/.bun/install/cache
      # 增加内存限制，避免分段错误
    #   NODE_OPTIONS: "--max-old-space-size=4096"
    commands:
      - ls -l $(echo $PATH | tr ':' '\n') | head -n 50
      - pwd
      - ls -la
      - cd front
      - pwd
      - ls -la
      - bun install --frozen-lockfile
      - bun run lint
      - bun run build

