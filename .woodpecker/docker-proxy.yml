# Woodpecker CI - TAO Hash Proxy Docker Build Pipeline

when:
  event: manual
  evaluate: CI_MANUAL_TARGET == "proxy"

volumes:
  docker_cache:
    driver: local

steps:
  - name: build-taohash-proxy
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      # TAO Hash Proxy 构建配置
      repo: coinflow/dogehash-proxy
      dockerfile: proxy/docker/Dockerfile
      context: proxy/
      platforms:
        - linux/amd64
        - linux/arm64/v8
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local\,src=/cache'
      cache_to: "type=local,dest=/cache,mode=max"

  