# Woodpecker CI - TAO Hash API Docker Build Pipeline

when:
  event: manual
  evaluate: CI_MANUAL_TARGET == "api"

volumes:
  docker_cache:
    driver: local

steps:
  - name: build-taohash-api
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      # TAO Hash API 构建配置
      repo: coinflow/dogehash-api
      dockerfile: hash/Dockerfile.api
      context: hash/
      platforms:
        - linux/amd64
        # - linux/arm64/v8  # 暂时禁用多架构，调试缓存问题
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local\,src=/cache'
      cache_to: "type=local,dest=/cache,mode=max"

  