# Woodpecker CI - Docker Build Pipeline

when:
  event: manual
  evaluate: CI_MANUAL_TARGET == "web"
  
volumes:
  # 定义一个持久化的 volume，用于 buildx 缓存
  docker_cache:
    driver: local

steps:
  - name: build-web
    image: woodpeckerci/plugin-docker-buildx
    # 两个步骤共享同一个缓存 volume
    volumes:
      - docker_cache:/cache
    settings:
      repo: coinflow/dogemine-web
      dockerfile: front/Dockerfile
      context: front
      platforms:
        - linux/amd64
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      # 从挂载的 volume 读取缓存 (用引号包裹，防止被错误分割)
      cache_from:
        - 'type=local\,src=/cache'
      # 将构建缓存写入到挂载的 volume (用引号包裹，防止被错误分割)
      cache_to: "type=local,dest=/cache,mode=max"
