
when:
  - branch: main
    event: [push,pull_request]

volumes:
  python_cache:
    driver: local
  pip_cache:
    driver: local

steps:
  run-unit-tests:
    image: coinflow/dind-py
    volumes:
      - python_cache:/opt/venv
      - pip_cache:/root/.cache/pip
    commands:
      - echo "=== 清理和重建Python环境 ==="
      - rm -rf /opt/venv || echo "清理旧环境失败，继续..."
      - python3 -m venv /opt/venv
      - echo "=== 激活虚拟环境并安装Python依赖 ==="
      - cd hash
      - . /opt/venv/bin/activate
      - echo "=== 升级 pip 工具 (使用缓存) ==="
      - pip install --cache-dir /root/.cache/pip --upgrade pip setuptools wheel
      - echo "=== 安装项目依赖 (使用缓存) ==="
      - pip install --cache-dir /root/.cache/pip -e .[dev]
      - pip install --cache-dir /root/.cache/pip pytest pytest-cov
      - echo "=== 运行单元测试 ==="
      - python -m pytest tests/unit_tests/ -v --tb=short
      - echo "=== 单元测试完成 ==="

  run-integration-tests:
    image: coinflow/dind-py
    privileged: true
    network_mode: host
    environment:
      DOCKER_TLS_CERTDIR: ""
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - python_cache:/opt/venv
      - pip_cache:/root/.cache/pip
    commands:
      - echo "=== 清理和重建Python环境 ==="
      - rm -rf /opt/venv || echo "清理旧环境失败，继续..."
      - python3 -m venv /opt/venv
      - echo "=== 激活虚拟环境并安装项目依赖 ==="
      - cd hash
      - . /opt/venv/bin/activate
      - echo "=== 升级 pip 工具 (使用缓存) ==="
      - pip install --cache-dir /root/.cache/pip --upgrade pip setuptools wheel
      - echo "=== 安装项目依赖 (使用缓存) ==="
      - pip install --cache-dir /root/.cache/pip -e .[dev]
      - pip install --cache-dir /root/.cache/pip pytest redis
      - echo "=== 安装额外依赖 (使用缓存) ==="
      - pip install --cache-dir /root/.cache/pip pytest-asyncio || echo "pytest-asyncio 安装失败，继续..."
      - pip install --cache-dir /root/.cache/pip bt_decode async-substrate-interface || echo "部分依赖安装失败，继续..."
      - pip install --cache-dir /root/.cache/pip bittensor || echo "bittensor 安装失败，继续..."
      - echo "=== 启动Docker Compose服务 ==="
      - cd tests/integration_tests
      - docker-compose up -d
      - echo "=== 等待Redis服务就绪 ==="
      - timeout 60 bash -c 'until docker-compose exec redis redis-cli ping; do sleep 1; done'
      - echo "=== 运行集成测试 ==="
      - python run_integration_tests.py
      - echo "=== 运行 API 认证集成测试 (核心逻辑) ==="
      - python test_auth_core.py
      - echo "=== 集成测试完成 ==="
      - echo "=== 清理Docker Compose服务 ==="
      - docker-compose down -v