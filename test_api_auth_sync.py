#!/usr/bin/env python3
"""
同步版本的 API 认证集成测试

解决了异步测试在 CI 中被跳过的问题
"""

import hashlib
import secrets
import time
from typing import Dict, Any

try:
    import pytest
    from fastapi.testclient import TestClient
    from taohash.api import create_app
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 依赖缺失: {e}")
    print("跳过 API 集成测试")
    DEPENDENCIES_AVAILABLE = False
    
    # Mock pytest for graceful handling
    class MockPytest:
        class mark:
            @staticmethod
            def skipif(condition, reason=""):
                def decorator(func):
                    return func
                return decorator
    pytest = MockPytest()

# 如果依赖不可用，跳过所有测试
if DEPENDENCIES_AVAILABLE:
    pytestmark = pytest.mark.skipif(
        not DEPENDENCIES_AVAILABLE, 
        reason="缺少 FastAPI 或项目依赖"
    )


class MockBittensorWallet:
    """模拟 Bittensor 钱包用于测试"""
    
    def __init__(self, address: str):
        self.address = address
    
    def sign_message(self, message: str) -> str:
        """生成与 AccountService 验证逻辑匹配的签名"""
        signature_data = f"{self.address}_*_{message}"
        signature_hash = hashlib.sha256(signature_data.encode()).hexdigest()
        return f"0x{signature_hash}"


class TestSyncWalletAuth:
    """同步钱包认证集成测试"""
    
    def setup_method(self):
        """测试设置"""
        if DEPENDENCIES_AVAILABLE:
            self.app = create_app()
            self.client = TestClient(self.app)
    
    def test_complete_wallet_auth_flow_substrate(self):
        """测试完整的 Substrate 钱包认证流程 (同步)"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("缺少必要依赖")
        
        print(f"\n🧪 测试 Substrate 钱包认证流程")
        
        wallet = MockBittensorWallet("5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY")
        
        # 步骤 1: 获取 nonce
        print("📋 步骤 1: 获取登录随机数")
        nonce_response = self.client.post(
            "/accounts/nonce",
            json={"address": wallet.address}
        )
        
        assert nonce_response.status_code == 200
        nonce_data = nonce_response.json()
        assert nonce_data["success"] is True
        assert "nonce" in nonce_data
        
        nonce_message = nonce_data["nonce"]
        print(f"✅ Nonce 获取成功")
        
        # 步骤 2: 签名认证
        print("🔐 步骤 2: 钱包签名和认证")
        signature = wallet.sign_message(nonce_message)
        
        auth_response = self.client.post(
            "/accounts/auth",
            json={
                "address": wallet.address,
                "signature": signature,
                "message": nonce_message,
                "source": "polkadot-js"
            }
        )
        
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is True
        assert "token" in auth_data
        
        token = auth_data["token"]
        print(f"✅ 认证成功")
        
        # 步骤 3: 使用 token 访问保护的 API
        print("🔒 步骤 3: 使用 token 访问保护的 API")
        protected_response = self.client.get(
            "/health",  # 使用简单的健康检查端点
            headers={"Authorization": f"Bearer {token}"}
        )
        
        # 健康检查不需要认证，应该返回 200
        assert protected_response.status_code == 200
        print(f"✅ API 访问成功")
        
        # 步骤 4: 测试断开连接
        print("🔌 步骤 4: 测试断开连接")
        disconnect_response = self.client.post(
            "/accounts/disconnect",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert disconnect_response.status_code == 200
        disconnect_data = disconnect_response.json()
        assert disconnect_data["success"] is True
        print("✅ 断开连接成功")
    
    def test_ethereum_wallet_auth(self):
        """测试 Ethereum 钱包认证"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("缺少必要依赖")
        
        print(f"\n🧪 测试 Ethereum 钱包认证")
        
        wallet = MockBittensorWallet("******************************************")
        
        # 获取 nonce
        nonce_response = self.client.post(
            "/accounts/nonce",
            json={"address": wallet.address}
        )
        
        assert nonce_response.status_code == 200
        nonce_data = nonce_response.json()
        nonce_message = nonce_data["nonce"]
        
        # 签名和认证
        signature = wallet.sign_message(nonce_message)
        auth_response = self.client.post(
            "/accounts/auth",
            json={
                "address": wallet.address,
                "signature": signature,
                "message": nonce_message,
                "source": "metamask"
            }
        )
        
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is True
        
        print(f"✅ Ethereum 钱包认证成功")
    
    def test_invalid_signature(self):
        """测试无效签名处理"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("缺少必要依赖")
        
        print(f"\n🧪 测试无效签名处理")
        
        test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
        
        # 获取有效 nonce
        nonce_response = self.client.post(
            "/accounts/nonce",
            json={"address": test_address}
        )
        nonce_data = nonce_response.json()
        nonce_message = nonce_data["nonce"]
        
        # 使用无效签名
        auth_response = self.client.post(
            "/accounts/auth",
            json={
                "address": test_address,
                "signature": "0xinvalid_signature",
                "message": nonce_message,
                "source": "polkadot-js"
            }
        )
        
        assert auth_response.status_code == 200
        auth_data = auth_response.json()
        assert auth_data["success"] is False
        
        print(f"✅ 无效签名正确被拒绝")
    
    def test_invalid_token(self):
        """测试无效 token 处理"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("缺少必要依赖")
        
        print(f"\n🧪 测试无效 Token 处理")
        
        response = self.client.post(
            "/accounts/disconnect",
            headers={"Authorization": "Bearer invalid_token_123"}
        )
        
        # 应该返回 401 未授权
        assert response.status_code == 401
        
        print(f"✅ 无效 token 正确被拒绝")
    
    def test_missing_authorization_header(self):
        """测试缺少认证头的请求"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("缺少必要依赖")
        
        response = self.client.post("/accounts/disconnect")
        
        # 应该返回 403 或 422 (缺少必需头部)
        assert response.status_code in [403, 422]
        
        print(f"✅ 缺少认证头正确被拒绝")


# 如果作为脚本运行，执行简单测试
def main():
    if not DEPENDENCIES_AVAILABLE:
        print("⚠️ 缺少必要依赖，跳过测试")
        return
    
    print("🚀 运行同步 API 认证测试")
    
    # 创建测试实例
    test_instance = TestSyncWalletAuth()
    test_instance.setup_method()
    
    try:
        # 运行核心测试
        test_instance.test_complete_wallet_auth_flow_substrate()
        test_instance.test_ethereum_wallet_auth()
        test_instance.test_invalid_signature()
        test_instance.test_invalid_token()
        test_instance.test_missing_authorization_header()
        
        print("\n🎉 所有同步 API 测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()