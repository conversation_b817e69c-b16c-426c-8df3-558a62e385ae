{"name": "interface", "type": "module", "version": "0.1.0", "private": true, "packageManager": "bun@1.2.18", "engines": {"node": ">=20"}, "scripts": {"build": "next build", "check:type": "tsc --noEmit --pretty", "dev": "next dev", "lint": "eslint --flag unstable_ts_config", "lint:fix": "eslint --flag unstable_ts_config --fix", "prepare": "simple-git-hooks", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@lingui/core": "^5.3.3", "@lingui/react": "^5.3.3", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@rainbow-me/rainbowkit": "^2.2.8", "@tanstack/react-table": "^8.20.6", "@types/react-datepicker": "^6.2.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.5.1", "embla-carousel-wheel-gestures": "^8.0.2", "framer-motion": "^12.23.7", "gsap": "^3.13.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lodash-es": "^4.17.21", "lucide-react": "^0.468.0", "next": "^15.3.5", "next-themes": "^0.4.4", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.1", "react-i18next": "^15.6.0", "react-qrcode-logo": "^3.0.0", "react-toastify": "^11.0.2", "tailwindcss-animate": "^1.0.7", "viem": "^2.33.1", "wagmi": "^2.16.0", "zod": "^3.24.1"}, "devDependencies": {"@ebay/nice-modal-react": "^1.2.13", "@eslint/eslintrc": "^3", "@innei/prettier": "^0.15.0", "@swc-jotai/debug-label": "^0.2.0", "@swc-jotai/react-refresh": "^0.3.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.62.8", "@types/eslint__eslintrc": "^2.1.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-hyoban": "^4.0.8", "eslint-config-next": "15.1.0", "eslint-plugin-package-json": "0.15.2", "eslint-plugin-react-hooks": "^5.1.0", "jiti": "^2.4.1", "jotai": "^2.10.4", "jotai-devtools": "^0.10.1", "jotai-scope": "^0.7.2", "jotai-tanstack-query": "^0.9.0", "lint-staged": "^15.2.11", "postcss": "^8", "prettier": "^3.4.2", "simple-git-hooks": "^2.11.1", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.17", "typescript": "^5"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"**/src/**/*.{js,jsx,ts,tsx}": ["eslint --flag unstable_ts_config --cache --fix", "prettier --ignore-path ./.gitignore --write "]}}