declare global {
  type Nilable<T> = T | undefined | null

  type CanWrite<T> = {
    -readonly [K in keyof T]: T[K] extends Record<any, any> ? CanWrite<T[K]> : T[K]
  }

  type Prettify<T> = {
    [K in keyof T]: T[K]
  } & {}

  type MaybeArray<T> = T | T[]

  type DeepRequired<T> = T extends (...args: any[]) => any
    ? T
    : T extends object
    ? { [P in keyof T]-?: DeepRequired<Required<T[P]>> }
    : T

  type DeepOverride<T, U> = {
    [K in keyof T]: K extends keyof U
    ? U[K] extends Record<string, unknown> // 检查 U[K] 是否是对象
    ? T[K] extends Record<string, unknown> // 检查 T[K] 是否是对象
    ? DeepOverride<T[K], U[K]> // 如果双方是对象，则递归
    : U[K] // 如果只有 U[K] 是对象，则直接覆盖
    : U[K] // 如果 U[K] 不是对象，则直接覆盖
    : T[K] // 如果 U 没有覆盖该属性，则保持原类型
  }
}

export { }
