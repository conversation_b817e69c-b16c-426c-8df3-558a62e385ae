import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  /* config options here */
  output: 'standalone',
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  webpack: (config) => {
    config.externals.push('pino-pretty', 'encoding')
    return config
  },
  experimental: {
    // swcPlugins: [
    //   ['@swc-jotai/react-refresh', {}],
    //   ['@swc-jotai/debug-label', {}],
    // ],
  },
  // Disable the development indicator (N)
  devIndicators: {
    buildActivity: false,
  },

  transpilePackages: ['jotai-devtools', 'lucide-react'],
}

export default nextConfig
