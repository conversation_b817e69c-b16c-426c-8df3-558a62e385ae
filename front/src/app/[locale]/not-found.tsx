'use client'

import Link from 'next/link'

import { Button } from '@/components/ui'
import { DOGE_MINE_POOL_APP } from '@/constants/link'
import { useSafeTranslation } from '@/lib/use-safe-translation'

export default function NotFound() {
  const { t } = useSafeTranslation()
  
  return (
    <div className="flex h-full items-center justify-center">
      <div className="flex flex-col items-center gap-7">
        <h2 className="text-6xl">404</h2>
        <div className="text-center">
          <p>
            {t('notFound.message', 'Sorry, the page you are trying to access does not exist')}
          </p>
          <p className="text-xs text-[#848d9b]">
            {t('notFound.suggestion', 'The page you are trying to view is temporarily unavailable. Please try again later')}
          </p>
        </div>
        <Link href={DOGE_MINE_POOL_APP.home}>
          <Button>
            {t('notFound.returnHome', 'Return Home')}
          </Button>
        </Link>
      </div>
    </div>
  )
}
