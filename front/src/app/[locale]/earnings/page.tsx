'use client'

import { use<PERSON>ara<PERSON>,useRouter } from 'next/navigation'
import { useState } from 'react'

import { useWalletConnection } from '@/lib/hooks/use-wallet-connection'

import { NotConnectedDashboard } from '../dashboard/_comps/not-connected-dashboard'

export default function EarningsPage() {
  const { isConnected } = useWalletConnection()
  const router = useRouter()
  const params = useParams()
  const locale = params.locale as string
  const [activeTab, setActiveTab] = useState<'worker' | 'earning'>('earning')
  
  const handleTabChange = (tab: 'worker' | 'earning') => {
    setActiveTab(tab)
    if (tab === 'worker') {
      router.push(`/${locale}/workers`)
    }
  }

  // 如果未连接钱包，显示连接提示页面
  if (!isConnected) {
    return <NotConnectedDashboard />
  }

  // Mock data for earnings
  const earningsData = [
    { time: '11:11 2025/6/6', worker: '5EewuHxa', earning: '200xxx', walletAddress: '0x4c6924...11a0fa', hash: '0x4c6924...11a0fa' },
    { time: '11:11 2025/6/6', worker: '5EewuHxa', earning: '200xxx', walletAddress: '0x4c6924...11a0fa', hash: '0x4c6924...11a0fa' },
    { time: '11:11 2025/6/6', worker: '5EewuHxa', earning: '200xxx', walletAddress: '0x4c6924...11a0fa', hash: '0x4c6924...11a0fa' },
    { time: '11:11 2025/6/6', worker: '5EewuHxa', earning: '200xxx', walletAddress: '0x4c6924...11a0fa', hash: '0x4c6924...11a0fa' }
  ]

  return (
    <div className="flex min-h-screen w-full flex-col bg-[#FAF9F5] p-4 md:p-6">
      <div className="mx-auto w-full max-w-[1184px]">
        <div className="flex flex-col gap-6 rounded-2xl bg-white p-6 pb-3">
          {/* Header Section */}
          <div className="flex flex-col gap-8">
            {/* Title */}
            <div className="flex gap-6 pr-6">
              <div className="flex flex-1 flex-col gap-1">
                <div className="flex items-center gap-4">
                  <h1 
                    className="text-[#141413]"
                    style={{
                      fontFamily: 'EB Garamond',
                      fontWeight: 600,
                      fontSize: '24px',
                      lineHeight: '1.5'
                    }}
                  >
                    Miner Overview
                  </h1>
                </div>
                <div className="flex h-9 items-center">
                  <p 
                    className="text-[#3D3D3A]"
                    style={{
                      fontFamily: 'Styrene B',
                      fontWeight: 400,
                      fontSize: '16px',
                      lineHeight: '1.5'
                    }}
                  >
                    Track hashrate, stakes, scores and rewards across all your active workers
                  </p>
                </div>
              </div>
            </div>

            {/* Tabs and Content */}
            <div className="flex flex-col gap-6">
              {/* Tab Buttons */}
              <div className="flex items-center gap-2.5">
                <div className="flex items-center gap-2 rounded-3xl bg-[#F7F5EE] p-2">
                  <button
                    onClick={() => handleTabChange('worker')}
                    className={`rounded-3xl px-4 py-2 transition-colors ${
                      activeTab === 'worker'
                        ? 'bg-[#E8E6DC] text-[#141413]'
                        : 'bg-[#E8E6DC] text-[#73726C]'
                    }`}
                    style={{
                      fontFamily: 'Styrene B',
                      fontWeight: 500,
                      fontSize: '14px',
                      lineHeight: '1.571'
                    }}
                  >
                    Worker
                  </button>
                  <button
                    onClick={() => handleTabChange('earning')}
                    className={`rounded-3xl px-4 py-2 transition-colors ${
                      activeTab === 'earning'
                        ? 'bg-[#E8E6DC] text-[#141413]'
                        : 'bg-[#E8E6DC] text-[#73726C]'
                    }`}
                    style={{
                      fontFamily: 'Styrene B',
                      fontWeight: 500,
                      fontSize: '14px',
                      lineHeight: '1.571'
                    }}
                  >
                    Earning
                  </button>
                </div>
              </div>

              {/* Earnings Cards */}
              <div className="flex flex-col gap-2 md:flex-row">
                {/* Today Rewards */}
                <div className="flex flex-1 items-center gap-2 rounded-xl bg-[#F4F2E7] p-2">
                  <div className="flex size-11 items-center justify-center rounded-lg bg-[#E8E6DC] p-2.5">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect width="15" height="16.54" fill="#38365D"/>
                      <rect x="8" y="3.14" width="3.77" height="4.33" fill="#38365D"/>
                    </svg>
                  </div>
                  <div className="flex flex-1 flex-col">
                    <span 
                      className="text-[#73726C]"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.571'
                      }}
                    >
                      Today xx Rewards
                    </span>
                    <span 
                      className="text-[#141413]"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '16px',
                        lineHeight: '1.5'
                      }}
                    >
                      158.80 USD
                    </span>
                  </div>
                </div>

                {/* Yesterday Rewards */}
                <div className="flex flex-1 items-center gap-2 rounded-xl bg-[#F4F2E7] p-2">
                  <div className="flex size-11 items-center justify-center rounded-lg bg-[#E8E6DC] p-2.5">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect width="15" height="16.54" fill="#38365D"/>
                      <rect x="8" y="3.14" width="3.77" height="4.33" fill="#38365D"/>
                    </svg>
                  </div>
                  <div className="flex flex-1 flex-col">
                    <span 
                      className="text-[#73726C]"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.571'
                      }}
                    >
                      Yesterday xx Rewards
                    </span>
                    <span 
                      className="text-[#141413]"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '16px',
                        lineHeight: '1.5'
                      }}
                    >
                      158.80 USD
                    </span>
                  </div>
                </div>

                {/* Total Rewards */}
                <div className="flex flex-1 items-center gap-2 rounded-xl bg-[#F4F2E7] p-2">
                  <div className="flex size-11 items-center justify-center rounded-lg bg-[#E8E6DC] p-2.5">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect width="15" height="16.54" fill="#38365D"/>
                      <rect x="8" y="3.14" width="3.77" height="4.33" fill="#38365D"/>
                    </svg>
                  </div>
                  <div className="flex flex-1 flex-col">
                    <span 
                      className="text-[#73726C]"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.571'
                      }}
                    >
                      Total xx Rewards
                    </span>
                    <span 
                      className="text-[#141413]"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '16px',
                        lineHeight: '1.5'
                      }}
                    >
                      158.80 USD
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chart Section */}
          <div className="flex flex-col items-center gap-2 rounded-2xl bg-[#FAF9F5] p-4">
            <div className="flex w-full flex-col items-center gap-2">
              <div className="flex w-full items-center gap-4 px-2">
                <h2 
                  className="text-[#141413]"
                  style={{
                    fontFamily: 'Styrene B',
                    fontWeight: 500,
                    fontSize: '18px',
                    lineHeight: '1.556'
                  }}
                >
                  Rewards Chart
                </h2>
              </div>
              <div className="flex h-[348px] w-full flex-col rounded-xl bg-[#F7F5EE] p-4">
                <div className="relative flex h-full flex-col">
                  {/* Chart Title - positioned absolute */}
                  <div className="absolute left-5 top-2.5 z-10">
                    <span 
                      className="text-[#CC785C]"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        fontSize: '12px',
                        lineHeight: '1.5'
                      }}
                    >
                      Rewards
                    </span>
                  </div>
                  
                  {/* Chart Labels */}
                  <div className="flex size-full items-start gap-1 pt-8">
                    {/* Y-axis Labels */}
                    <div className="flex h-full w-[60px] flex-col justify-end gap-8 pt-4">
                      <span className="text-center text-xs text-[#3D3D3A]">1.00</span>
                      <span className="text-center text-xs text-[#3D3D3A]">800.00</span>
                      <span className="text-center text-xs text-[#3D3D3A]">600.00</span>
                      <span className="text-center text-xs text-[#3D3D3A]">400.00</span>
                      <span className="text-center text-xs text-[#3D3D3A]">200.00</span>
                      <span className="text-center text-xs text-[#3D3D3A]">0.00</span>
                    </div>
                    
                    {/* Chart Area */}
                    <div className="flex h-full flex-1 flex-col">
                      {/* Chart content area */}
                      <div className="relative flex-1">
                        {/* Grid lines */}
                        <div className="absolute inset-0 flex flex-col justify-between">
                          {Array.from({length: 6}).map((_, i) => (
                            <div key={i} className="border-t border-dashed border-[rgba(31,30,29,0.15)]" />
                          ))}
                        </div>
                        
                        {/* Chart line simulation */}
                        <div className="absolute inset-0 flex items-center">
                          <svg 
                            width="100%" 
                            height="100%" 
                            viewBox="0 0 1000 200" 
                            className="absolute inset-0"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M0,150 Q100,120 200,130 T400,110 T600,140 T800,120 L1000,160"
                              stroke="#DA7A5B"
                              strokeWidth="2"
                              fill="none"
                              vectorEffect="non-scaling-stroke"
                            />
                            <defs>
                              <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" stopColor="rgba(218, 122, 91, 0.2)" />
                                <stop offset="100%" stopColor="rgba(218, 122, 91, 0)" />
                              </linearGradient>
                            </defs>
                            <path
                              d="M0,150 Q100,120 200,130 T400,110 T600,140 T800,120 L1000,160 L1000,200 L0,200 Z"
                              fill="url(#areaGradient)"
                            />
                          </svg>
                        </div>
                      </div>
                      
                      {/* X-axis Labels */}
                      <div className="flex justify-between gap-[70px] pt-4 text-xs text-[#73726C]">
                        <span>6/24 10:00</span>
                        <span>6/24 13:00</span>
                        <span>6/24 16:00</span>
                        <span>6/24 19:00</span>
                        <span>6/24 22:00</span>
                        <span>6/25 1:00</span>
                        <span>6/25 4:00</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* History Table */}
          <div className="flex flex-col rounded-2xl bg-[#FAF9F5] p-4">
            <div className="flex items-stretch gap-4 pb-3">
              <h2 
                className="text-[#141413]"
                style={{
                  fontFamily: 'Styrene B',
                  fontWeight: 500,
                  fontSize: '18px',
                  lineHeight: '1.556'
                }}
              >
                History
              </h2>
            </div>

            {/* Table Header */}
            <div className="hidden w-full items-center bg-[#E8E6DC] px-2 md:flex">
              <div className="w-[266px] px-0 py-3 pr-1.5">
                <span 
                  className="text-[#3D3D3A]"
                  style={{
                    fontFamily: 'Styrene B',
                    fontWeight: 500,
                    fontSize: '12px',
                    lineHeight: '1.5'
                  }}
                >
                  Time
                </span>
              </div>
              <div className="w-[210px] px-0 py-3">
                <span 
                  className="text-[#3D3D3A]"
                  style={{
                    fontFamily: 'Styrene B',
                    fontWeight: 500,
                    fontSize: '12px',
                    lineHeight: '1.5'
                  }}
                >
                  Worker
                </span>
              </div>
              <div className="w-[186px] px-0 py-3">
                <span 
                  className="text-[#3D3D3A]"
                  style={{
                    fontFamily: 'Styrene B',
                    fontWeight: 500,
                    fontSize: '12px',
                    lineHeight: '1.5'
                  }}
                >
                  Earning
                </span>
              </div>
              <div className="w-[297px] px-0 py-3">
                <span 
                  className="text-[#3D3D3A]"
                  style={{
                    fontFamily: 'Styrene B',
                    fontWeight: 500,
                    fontSize: '12px',
                    lineHeight: '1.5'
                  }}
                >
                  Wallet Address
                </span>
              </div>
              <div className="w-[104px] px-0 py-3">
                <span 
                  className="text-[#3D3D3A]"
                  style={{
                    fontFamily: 'Styrene B',
                    fontWeight: 500,
                    fontSize: '12px',
                    lineHeight: '1.5'
                  }}
                >
                  Hash
                </span>
              </div>
            </div>

            {/* Table Rows */}
            <div className="flex w-full flex-col">
              {earningsData.map((item, index) => (
                <div 
                  key={index}
                  className={`flex w-full flex-col gap-2 border-b border-[rgba(31,30,29,0.15)] px-2 py-3 md:flex-row md:items-center md:gap-0 md:py-1.5 ${
                    index % 2 === 1 ? 'bg-[#F0EEE6]' : 'bg-transparent'
                  }`}
                >
                  {/* Mobile Layout */}
                  <div className="flex flex-col gap-2 md:hidden">
                    <div className="flex justify-between">
                      <span className="text-xs text-[#73726C]">Time:</span>
                      <span className="text-sm text-[#141413]">{item.time}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-[#73726C]">Worker:</span>
                      <span className="text-sm text-[#141413]">{item.worker}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-[#73726C]">Earning:</span>
                      <span className="text-sm text-[#141413]">{item.earning}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-[#73726C]">Wallet:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-[#141413]">{item.walletAddress}</span>
                        <div className="flex size-6 items-center justify-center rounded-lg bg-[rgba(31,30,29,0.05)]">
                          <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                            <path d="M5.5 3.5H9.5V1.5H5.5V3.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                            <path d="M2.5 4.5H10.5V12.5H2.5V4.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Desktop Layout */}
                  <div className="hidden md:flex md:w-full md:items-center">
                    <div className="w-[266px] p-1">
                      <span 
                        className="text-[#141413]"
                        style={{
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.571'
                        }}
                      >
                        {item.time}
                      </span>
                    </div>
                    <div className="w-[210px] py-2.5">
                      <span 
                        className="text-[#141413]"
                        style={{
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.571'
                        }}
                      >
                        {item.worker}
                      </span>
                    </div>
                    <div className="w-[186px]">
                      <span 
                        className="text-[#141413]"
                        style={{
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.571'
                        }}
                      >
                        {item.earning}
                      </span>
                    </div>
                    <div className="flex w-[297px] items-center gap-2 py-2.5">
                      <span 
                        className="truncate text-[#141413]"
                        style={{
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.571'
                        }}
                      >
                        {item.walletAddress}
                      </span>
                      <div className="flex size-6 items-center justify-center rounded-lg bg-[rgba(31,30,29,0.05)]">
                        <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                          <path d="M5.5 3.5H9.5V1.5H5.5V3.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                          <path d="M2.5 4.5H10.5V12.5H2.5V4.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                        </svg>
                      </div>
                    </div>
                    <div className="flex w-[104px] items-center gap-2 py-2.5">
                      <span 
                        className="truncate text-[#141413]"
                        style={{
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.571'
                        }}
                      >
                        {item.hash}
                      </span>
                      <div className="flex items-center p-1">
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                          <path d="M3 3L7 7M7 3L3 7" stroke="#73726C" strokeWidth="1"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}