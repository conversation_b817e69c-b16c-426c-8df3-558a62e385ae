'use client'

// import { Trans } from '@lingui/react/macro'
import type { ColumnDef } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useMemo } from 'react'

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui'

const columns: ColumnDef<{
  date: string
  power: string
  currency: string
  total: string
  unit: string
  price: string
  totalUsd: string
  settlement: string
}>[] = [
  {
    header: 'Date',
    cell: ({ row }) => row.original.date,
  },
  {
    header: 'Power',
    cell: ({ row }) => row.original.power,
  },
  {
    header: 'Total Earnings',
    cell: ({ row }) => `${row.original.total} ${row.original.currency}`,
  },
  {
    header: 'Unit Earnings (TH/S)',
    cell: ({ row }) => row.original.unit,
  },
  {
    header: 'Price (USD)',
    cell: ({ row }) => row.original.price,
  },
  {
    header: 'Total Earnings (USD)',
    cell: ({ row }) => row.original.totalUsd,
  },
  {
    header: 'Settlement Method',
    cell: ({ row }) => row.original.settlement,
  },
]

const EarningsTable = () => {
  const { data } = {
    data: [
      {
        date: '2024-01-01',
        power: '1000',
        total: '1000',
        currency: 'BTC',
        unit: '1000',
        price: '1000',
        totalUsd: '1000',
        settlement: 'PPS',
      },
      {
        date: '2024-01-02',
        power: '2000',
        total: '2000',
        currency: 'DOGE',
        unit: '2000',
        price: '2000',
        totalUsd: '2000',
        settlement: 'PPS',
      },
      {
        date: '2024-01-03',
        power: '3000',
        total: '3000',
        currency: 'ETH',
        unit: '3000',
        price: '3000',
        totalUsd: '3000',
        settlement: 'PPS',
      },
    ],
  }

  const table = useReactTable({
    data: useMemo(() => data, [data]),
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          "Earnings Details"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export const Content = () => {
  return (
    <div className="flex flex-col gap-4 p-6">
      <EarningsTable />
    </div>
  )
}
