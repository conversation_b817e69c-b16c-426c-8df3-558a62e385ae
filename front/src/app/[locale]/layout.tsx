import '@/styles/globals.css'

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Merriweather } from 'next/font/google'
import * as React from 'react'

import { Providers } from '@/components/providers'
import { ToastifyContainer } from '@/components/toastify'
import { LOCALES } from '@/constants'

import { LayoutManager } from './_layout/layout-manager'

type PageLangParam = {
  params: Promise<{ locale: string }>
}

export async function generateStaticParams() {
  return LOCALES.map((lang) => ({ lang }))
}

export async function generateMetadata() {
  return {
    title: 'Dogemine - Triple Mining, Triple Rewards',
    description: 'The world\'s first mining pool enabling Scrypt miners to join Bittensor subnet.',
    icons: [
      {
        url: '/images/icon.svg',
        type: 'image/x-icon',
        rel: 'icon',
      },
    ],
  }
}

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
})

const ebGaramond = EB_Garamond({
  subsets: ['latin'],
  variable: '--font-eb-garamond',
  display: 'swap',
})

const merriweather = Merriweather({
  weight: ['300', '400', '700', '900'],
  subsets: ['latin'],
  variable: '--font-merriweather',
  display: 'swap',
})

async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode
} & PageLangParam) {
  const { locale } = await params

  return (
    <html lang={locale || 'en'} suppressHydrationWarning>
      <body className={`h-dvh font-sans antialiased ${inter.variable} ${ebGaramond.variable} ${merriweather.variable}`}>
        <Providers locale={locale}>
          <LayoutManager>
            {children}
          </LayoutManager>
          <ToastifyContainer />
        </Providers>
      </body>
    </html>
  )
}

export default RootLayout
