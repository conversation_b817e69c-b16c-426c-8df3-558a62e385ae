import { create, useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
// import { Trans } from '@lingui/react/macro'
import { createContext, use, useEffect, useMemo, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { z } from 'zod'

import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
} from '@/components/ui'

const SecurityWarning = () => {
  const { setType, setStep } = useModifyWithdrawAddressContext()

  const handleVerify = (type: ModifyWithdrawAddressVerifyType) => {
    setType(type)
    setStep(ModifyWithdrawAddressStep.Verify)
  }

  return (
    <div className="flex flex-col gap-4">
      <Button
        onClick={() => handleVerify(ModifyWithdrawAddressVerifyType.Email)}
      >
        "Email Verification"
      </Button>
      <Button
        onClick={() => handleVerify(ModifyWithdrawAddressVerifyType.Google)}
      >
        "Google Verification"
      </Button>
      <Button
        onClick={() => handleVerify(ModifyWithdrawAddressVerifyType.Passkey)}
      >
        "Passkey Verification"
      </Button>
    </div>
  )
}

const Verify = () => {
  const form = useFormContext<
    z.infer<typeof emailCodeVerifyFormSchema> &
      z.infer<typeof googleCodeVerifyFormSchema> &
      z.infer<typeof passkeyCodeVerifyFormSchema>
  >()
  const { type } = useModifyWithdrawAddressContext()

  return (
    <div className="flex flex-col gap-4">
      {type === ModifyWithdrawAddressVerifyType.Email && (
        <FormField
          control={form.control}
          name="emailCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                "Email Code"
              </FormLabel>
              <FormControl>
                <div className="flex items-center gap-2">
                  <InputField
                    {...field}
                    LeftIcon={<Icons.ShieldCheck size={16} />}
                    placeholder="Please enter email verification code"
                  />
                  <Button>
                    "Get Code"
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      {type === ModifyWithdrawAddressVerifyType.Google && (
        <FormField
          control={form.control}
          name="googleCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                "Google Code"
              </FormLabel>
              <FormControl>
                <InputField
                  {...field}
                  LeftIcon={<Icons.ShieldCheck size={16} />}
                  placeholder="Please enter google verification code"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      {type === ModifyWithdrawAddressVerifyType.Passkey && (
        <FormField
          control={form.control}
          name="passkeyCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                "Passkey Code"
              </FormLabel>
              <FormControl>
                <InputField
                  {...field}
                  LeftIcon={<Icons.ShieldCheck size={16} />}
                  placeholder="Please enter passkey verification code"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  )
}

const Modify = () => {
  const form = useFormContext<z.infer<typeof modifyWithdrawAddressFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between">
        <div>
          "Withdrawal Currency"
        </div>
        <div>USDT</div>
      </div>
      <div className="flex justify-between">
        <div>
          "Withdrawal Network"
        </div>
        <div>TRC20</div>
      </div>

      <FormField
        control={form.control}
        name="address"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Withdrawal Address"
            </FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.HandCoins size={16} />}
                placeholder="Please enter withdraw address"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="remark"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Wallet Remark"
            </FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.MessageSquare size={16} />}
                placeholder="Please enter wallet remark"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

const emailCodeVerifyFormSchema = z.object({
  emailCode: z.string().min(1),
})

const googleCodeVerifyFormSchema = z.object({
  googleCode: z.string().min(1),
})

const passkeyCodeVerifyFormSchema = z.object({
  passkeyCode: z.string().min(1),
})

const modifyWithdrawAddressFormSchema = z.object({
  address: z.string().min(1),
  remark: z.string().optional(),
})

const ModifyWithdrawAddressForm = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const { type, step, setStep } = useModifyWithdrawAddressContext()

  const formSchema = useMemo(() => {
    const schema =
      type === ModifyWithdrawAddressVerifyType.Email
        ? emailCodeVerifyFormSchema
        : type === ModifyWithdrawAddressVerifyType.Google
          ? googleCodeVerifyFormSchema
          : passkeyCodeVerifyFormSchema

    if (step === ModifyWithdrawAddressStep.Verify) {
      return schema
    }

    return modifyWithdrawAddressFormSchema.merge(schema)
  }, [type, step])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      emailCode: '',
      googleCode: '',
      passkeyCode: '',
      address: '',
      remark: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log('Modify Withdraw Address Data:', data)
    if (step === ModifyWithdrawAddressStep.Verify) {
      setStep(ModifyWithdrawAddressStep.Modify)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {children}
      </form>
    </Form>
  )
}

enum ModifyWithdrawAddressStep {
  SecurityWarning = 'security-warning',
  Verify = 'verify',
  Modify = 'modify',
}

enum ModifyWithdrawAddressVerifyType {
  Email = 'email',
  Google = 'google',
  Passkey = 'passkey',
}

const Context = createContext<{
  step: ModifyWithdrawAddressStep
  setStep: (step: ModifyWithdrawAddressStep) => void
  type: ModifyWithdrawAddressVerifyType | null
  setType: (type: ModifyWithdrawAddressVerifyType | null) => void
}>({
  step: ModifyWithdrawAddressStep.SecurityWarning,
  setStep: () => {},
  type: null,
  setType: () => {},
})

const useModifyWithdrawAddressContext = () => {
  return use(Context)
}

export const ModifyWithdrawAddressModal = create(() => {
  const modal = useModal()

  const [step, setStep] = useState<ModifyWithdrawAddressStep>(
    ModifyWithdrawAddressStep.SecurityWarning,
  )
  const [type, setType] = useState<ModifyWithdrawAddressVerifyType | null>(null)

  useEffect(() => {
    if (modal.visible) {
      setStep(ModifyWithdrawAddressStep.SecurityWarning)
      setType(null)
    }
  }, [modal.visible, setStep, setType])

  return (
    <Dialog open={modal.visible} onOpenChange={modal.hide}>
      <DialogContent aria-describedby={undefined}>
        <Context value={{ step, setStep, type, setType }}>
          <ModifyWithdrawAddressForm>
            <DialogHeader>
              <DialogTitle>
                "Modify Withdrawal Address"
              </DialogTitle>
              {step === ModifyWithdrawAddressStep.SecurityWarning && (
                <DialogDescription>
                  <Trans>
                    For your account security, please complete the two-step
                    verification first.
                  </Trans>
                </DialogDescription>
              )}
            </DialogHeader>

            {step === ModifyWithdrawAddressStep.SecurityWarning && (
              <SecurityWarning />
            )}

            {step === ModifyWithdrawAddressStep.Verify && <Verify />}
            {step === ModifyWithdrawAddressStep.Modify && <Modify />}

            {(step === ModifyWithdrawAddressStep.Verify ||
              step === ModifyWithdrawAddressStep.Modify) && (
              <DialogFooter className="pt-4">
                <Button type="submit" block>
                  {step === ModifyWithdrawAddressStep.Verify ? (
                    "Next Step"
                  ) : (
                    "Submit"
                  )}
                </Button>
              </DialogFooter>
            )}
          </ModifyWithdrawAddressForm>
        </Context>
      </DialogContent>
    </Dialog>
  )
})
