'use client'

import { show } from '@ebay/nice-modal-react'
// import { Trans } from '@lingui/react/macro'
import { useEffect, useMemo, useState } from 'react'

import type { CardColumnProps } from '@/components/card/card-column'
import { CardColumn } from '@/components/card/card-column'
import {
  Button,
  Icons,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui'

import { ModifyWithdrawAddressModal } from './modify-withdraw-address-modal'

interface Currency {
  icon?: React.ReactNode
  label: React.ReactNode
  value: string
}

export const Content = () => {
  const [currency, setCurrency] = useState<string>()

  const currencys: Currency[] = useMemo(() => {
    return [
      {
        icon: <Icons.DollarSign size={16} />,
        label: 'USDT',
        value: 'USDT',
      },
      {
        icon: <Icons.DollarSign size={16} />,
        label: 'DO<PERSON>',
        value: 'DO<PERSON>',
      },
    ]
  }, [])

  useEffect(() => {
    setCurrency(currencys[0].value)
  }, [currencys, setCurrency])

  const walletColumnGroups: CardColumnProps['columns'][] = [
    [
      {
        label: "Withdrawal Network",
        value: 'DOGECOIN',
      },
      {
        label: "Withdrawal Address",
        value: '1234567890',
        actions: [
          {
            label: "Edit",
            onClick: () => {},
          },
        ],
      },
    ],
    [
      {
        label: "Withdrawal Network",
        value: 'DOGECOIN',
      },
      {
        label: "Withdrawal Address",
        value: '1234567890',
        actions: [
          {
            label: "Edit",
            onClick: () => {
              show(ModifyWithdrawAddressModal)
            },
          },
        ],
      },
    ],
  ]

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <Select value={currency} onValueChange={setCurrency}>
          <SelectTrigger className="w-auto min-w-[200px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {currencys.map((currency) => (
                <SelectItem key={currency.value} value={currency.value}>
                  <div className="flex items-center gap-2">
                    {currency.icon}
                    {currency.label}
                  </div>
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>

        <Button LeftIcon={<Icons.Plus size={16} />}>
          "Add Wallet"
        </Button>
      </div>

      {walletColumnGroups.map((group, index) => (
        <CardColumn
          key={index}
          title={`Withdrawal Wallet ${index + 1}`}
          columns={group}
        />
      ))}
    </div>
  )
}
