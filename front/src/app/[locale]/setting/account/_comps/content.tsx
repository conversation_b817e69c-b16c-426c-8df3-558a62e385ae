'use client'

import { show } from '@ebay/nice-modal-react'
// import { Trans } from '@lingui/react/macro'
import type { ColumnDef } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import * as React from 'react'
import { useMemo } from 'react'

import type { CardColumnProps } from '@/components/card/card-column'
import { CardColumn } from '@/components/card/card-column'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

import { ModifyEmailModal } from './modify-email-modal'
import { ModifyGoogleVerificationModal } from './modify-google-verification-modal'
import { ModifyLoginPasswordModal } from './modify-login-password-modal'
import { SetEmailModal } from './set-email-modal'
import { SetGoogleVerificationModal } from './set-google-verification-modal'

const columns: ColumnDef<{
  loginTime: string
  ip: string
  browser: string
  device: string
  status: string
}>[] = [
  {
    id: '1',
    header: 'Login Time',
    cell: '2024-01-01 10:00:00',
  },
  {
    id: '2',
    header: 'IP Address',
    cell: '***********',
  },
  {
    id: '3',
    header: 'Browser',
    cell: 'Chrome',
  },
  {
    id: '4',
    header: 'Device',
    cell: 'Mac',
  },
  {
    id: '5',
    header: 'Status',
    cell: 'Success',
  },
]

const LoginHistoryTable = () => {
  const data = useMemo(
    () => [
      {
        loginTime: '2024-01-01 10:00:00',
        ip: '***********',
        browser: 'Chrome',
        device: 'Mac',
        status: 'Success',
      },
      {
        loginTime: '2024-01-01 10:00:00',
        ip: '***********',
        browser: 'Chrome',
        device: 'Mac',
        status: 'Success',
      },
    ],
    [],
  )

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          "Login History"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} className="bg-popup leading-8">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export const Content = () => {
  const basicInfoColumns: CardColumnProps['columns'] = [
    {
      label: "Account",
      value: 'melody11',
    },
    {
      label: "Real Name Authentication",
      value: "Not Completed",
      actions: [
        {
          label: "Authenticate",
        },
      ],
    },
  ]

  const securityColumns: CardColumnProps['columns'] = [
    {
      label: "Email",
      value: '<EMAIL>',
      actions: [
        {
          label: "Set",
          visible: true,
          onClick: () => show(SetEmailModal),
        },
        {
          label: "Modify",
          visible: true,
          onClick: () =>
            show(ModifyEmailModal, { email: '<EMAIL>' }),
        },
      ],
    },
    {
      label: "Google Verification",
      value: 'Not Set',
      actions: [
        {
          label: "Set",
          visible: true,
          onClick: () => {
            show(SetGoogleVerificationModal)
          },
        },
        {
          label: "Modify",
          visible: true,
          onClick: () => {
            show(ModifyGoogleVerificationModal, { email: '<EMAIL>' })
          },
        },
      ],
    },
    {
      label: "Passkey",
      value: "Not Set",
      actions: [
        {
          label: "Set",
          onClick: () => {},
        },
      ],
    },
    {
      label: "Login Password",
      value: "******",
      actions: [
        {
          label: "Modify",
          onClick: () => {
            show(ModifyLoginPasswordModal)
          },
        },
      ],
    },
  ]

  return (
    <div className="flex flex-col gap-6">
      <CardColumn
        title={"Basic Information"}
        columns={basicInfoColumns}
      />
      <CardColumn
        title={"Account Security"}
        columns={securityColumns}
      />
      <LoginHistoryTable />
    </div>
  )
}
