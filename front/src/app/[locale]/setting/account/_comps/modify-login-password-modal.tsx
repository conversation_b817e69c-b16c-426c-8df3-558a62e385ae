import { create, useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
// import { Trans } from '@lingui/react/macro'
import { createContext, use, useEffect, useMemo, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { z } from 'zod'

import {
  Button,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  PasswordField,
} from '@/components/ui'

const Verify = () => {
  const form = useFormContext<z.infer<typeof verifyFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        control={form.control}
        name="password"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Login Password"
            </FormLabel>
            <FormControl>
              <PasswordField
                {...field}
                LeftIcon={<Icons.Lock size={16} />}
                placeholder="Please enter login password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

const Modify = () => {
  const form = useFormContext<z.infer<typeof modifyFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        control={form.control}
        name="newPassword"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "New Password"
            </FormLabel>
            <FormControl>
              <PasswordField
                {...field}
                LeftIcon={<Icons.Lock size={16} />}
                placeholder="Please enter new password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="confirmPassword"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Confirm New Password"
            </FormLabel>
            <FormControl>
              <PasswordField
                {...field}
                LeftIcon={<Icons.Lock size={16} />}
                placeholder="Please confirm new password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

enum ModifyPasswordStep {
  Verify = 'Verify',
  Modify = 'Modify',
}

const verifyFormSchema = z.object({
  password: z.string().min(1),
})

const modifyFormSchema = z.object({
  newPassword: z
    .string()
    .regex(
      /^(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*])[A-Z\d!@#$%^&*]{8,12}$/i,
      'Password must be 8-12 characters long and include letters, numbers, and special characters',
    ),
  confirmPassword: z.string().min(8),
})

const ModifyPasswordForm = ({ children }: { children: React.ReactNode }) => {
  const { step, setStep, visible } = useStepContext()

  const formSchema = useMemo(() => {
    return step === ModifyPasswordStep.Verify
      ? verifyFormSchema
      : verifyFormSchema
          .merge(modifyFormSchema)
          .refine((data) => data.newPassword === data.confirmPassword, {
            message: "Passwords don't match",
            path: ['confirmPassword'],
          })
  }, [step])

  const form = useForm<
    z.infer<typeof verifyFormSchema> & z.infer<typeof modifyFormSchema>
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
      newPassword: '',
      confirmPassword: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log('Modify Password Data:', data)
    if (step === ModifyPasswordStep.Verify) {
      setStep(ModifyPasswordStep.Modify)
    } else {
      // Handle final submission
    }
  }

  const { reset } = form

  useEffect(() => {
    if (!visible) {
      reset()
    }
  }, [visible, reset])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {children}
      </form>
    </Form>
  )
}

const Context = createContext<{
  visible: boolean
  step: ModifyPasswordStep
  setStep: React.Dispatch<React.SetStateAction<ModifyPasswordStep>>
}>({
  visible: false,
  step: ModifyPasswordStep.Verify,
  setStep: () => {},
})

const useStepContext = () => {
  return use(Context)
}

export const ModifyLoginPasswordModal = create(() => {
  const modal = useModal()
  const [step, setStep] = useState(ModifyPasswordStep.Verify)

  useEffect(() => {
    if (modal.visible) {
      setStep(ModifyPasswordStep.Verify)
    }
  }, [modal.visible])

  return (
    <Dialog open={modal.visible} onOpenChange={modal.hide}>
      <DialogContent aria-describedby={undefined}>
        <Context value={{ visible: modal.visible, step, setStep }}>
          <ModifyPasswordForm>
            <DialogHeader>
              <DialogTitle>
                "Modify Login Password"
              </DialogTitle>
            </DialogHeader>

            {step === ModifyPasswordStep.Verify && <Verify />}
            {step === ModifyPasswordStep.Modify && <Modify />}

            <DialogFooter className="pt-4">
              <Button type="submit" block>
                <Trans>
                  {step === ModifyPasswordStep.Verify ? 'Next Step' : 'Confirm'}
                </Trans>
              </Button>
            </DialogFooter>
          </ModifyPasswordForm>
        </Context>
      </DialogContent>
    </Dialog>
  )
})
