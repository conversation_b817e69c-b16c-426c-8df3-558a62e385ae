import { create, useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
// import { Trans } from '@lingui/react/macro'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import {
  Button,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
} from '@/components/ui'

const formSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6).max(6),
})

export const SetEmailModal = create(() => {
  const modal = useModal()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      code: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log(data)
  }

  const { reset } = form

  useEffect(() => {
    if (modal.visible) {
      reset()
    }
  }, [modal.visible, reset])

  return (
    <Dialog open={modal.visible} onOpenChange={modal.hide}>
      <DialogContent aria-describedby={undefined}>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <DialogHeader>
              <DialogTitle>
                "Set Email"
              </DialogTitle>
            </DialogHeader>

            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      "Email"
                    </FormLabel>
                    <FormControl>
                      <InputField
                        {...field}
                        LeftIcon={<Icons.Mail size={16} />}
                        placeholder="Please enter email"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      "Code"
                    </FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <InputField
                          {...field}
                          LeftIcon={<Icons.ShieldCheck size={16} />}
                          placeholder="Please enter email verification code"
                        />
                        <Button>
                          "Get Code"
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="pt-4">
              <Button type="submit" block>
                "Submit"
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
})
