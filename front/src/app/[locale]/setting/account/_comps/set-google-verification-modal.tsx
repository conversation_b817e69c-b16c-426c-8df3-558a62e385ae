'use client'

import { create, useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
// import { Trans } from '@lingui/react/macro'
import { createContext, use, useEffect, useMemo, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { QRCode } from 'react-qrcode-logo'
import { z } from 'zod'

import { CopyButton } from '@/components/common/copy-button'
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
} from '@/components/ui'

const Verify = () => {
  const form = useFormContext<z.infer<typeof verifyFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        control={form.control}
        name="emailCode"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Code"
            </FormLabel>
            <FormControl>
              <div className="flex items-center gap-2">
                <InputField
                  {...field}
                  LeftIcon={<Icons.ShieldCheck size={16} />}
                  placeholder="Please enter email verification code"
                />
                <Button>
                  "Get Code"
                </Button>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

const Bind = () => {
  const form = useFormContext<z.infer<typeof bindFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col items-center justify-center gap-4">
        <QRCode
          value="https://example.com"
          size={256}
          bgColor="#ffffff"
          fgColor="#000000"
        />
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            B7EVMCULFZSBTPIBTEA7IKJEPGMWKUFG
          </div>
          <CopyButton text="B7EVMCULFZSBTPIBTEA7IKJEPGMWKUFG" />
        </div>
        <div className="text-sm text-red-400">
          <Trans>
            (Please keep this key safe for recovery in case of phone change or
            loss)
          </Trans>
        </div>
      </div>
      <FormField
        control={form.control}
        name="code"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Code</FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.ShieldCheck size={16} />}
                placeholder="Please enter Google verification code"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

enum SetGoogleVerificationStep {
  Verify = 'verify',
  Bind = 'bind',
}

const verifyFormSchema = z.object({
  emailCode: z.string().min(6).max(6),
})

const bindFormSchema = z.object({
  code: z.string().min(6).max(6),
})

const SetGoogleVerificationForm = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const { step, setStep, visible } = useStepContext()

  const formSchema = useMemo(() => {
    const schema =
      step === SetGoogleVerificationStep.Verify
        ? verifyFormSchema
        : verifyFormSchema.merge(bindFormSchema)
    return schema
  }, [step])

  const form = useForm<
    z.infer<typeof verifyFormSchema> & z.infer<typeof bindFormSchema>
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      emailCode: '',
      code: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log('Set Google Verification Code:', data)
    if (step === SetGoogleVerificationStep.Verify) {
      setStep(SetGoogleVerificationStep.Bind)
    } else {
      // Handle final submission
    }
  }

  const { reset } = form

  useEffect(() => {
    if (!visible) {
      reset()
    }
  }, [visible, reset])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {children}
      </form>
    </Form>
  )
}

const Context = createContext<{
  visible: boolean
  step: SetGoogleVerificationStep
  setStep: React.Dispatch<React.SetStateAction<SetGoogleVerificationStep>>
}>({
  visible: false,
  step: SetGoogleVerificationStep.Verify,
  setStep: () => {},
})

const useStepContext = () => {
  return use(Context)
}

export const SetGoogleVerificationModal = create(() => {
  const modal = useModal()
  const [step, setStep] = useState(SetGoogleVerificationStep.Verify)

  useEffect(() => {
    if (modal.visible) {
      setStep(SetGoogleVerificationStep.Verify)
    }
  }, [modal.visible, setStep])

  return (
    <Dialog open={modal.visible} onOpenChange={modal.hide}>
      <DialogContent aria-describedby={undefined}>
        <Context value={{ visible: modal.visible, step, setStep }}>
          <SetGoogleVerificationForm>
            <DialogHeader>
              <DialogTitle>
                "Bind Google Verification"
              </DialogTitle>
              {step === SetGoogleVerificationStep.Bind && (
                <DialogDescription>
                  <Trans>
                    Please use the Google Authenticator app to scan the QR code
                    below, or manually enter the key. Enter the generated
                    6-digit Google verification code into the verification box.
                  </Trans>
                </DialogDescription>
              )}
            </DialogHeader>

            {step === SetGoogleVerificationStep.Verify && <Verify />}
            {step === SetGoogleVerificationStep.Bind && <Bind />}

            <DialogFooter className="pt-4">
              <Button type="submit" block>
                {step === SetGoogleVerificationStep.Verify ? (
                  "Next Step"
                ) : (
                  "Confirm"
                )}
              </Button>
            </DialogFooter>
          </SetGoogleVerificationForm>
        </Context>
      </DialogContent>
    </Dialog>
  )
})
