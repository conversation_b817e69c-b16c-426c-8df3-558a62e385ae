'use client'

import { create, useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
// import { Trans } from '@lingui/react/macro'
import { createContext, use, useEffect, useMemo, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { QRCode } from 'react-qrcode-logo'
import { z } from 'zod'

import { CopyButton } from '@/components/common/copy-button'
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
} from '@/components/ui'

const Verify = () => {
  const form = useFormContext<z.infer<typeof verifyFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        control={form.control}
        name="emailCode"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Email Code"
            </FormLabel>
            <FormControl>
              <div className="flex items-center gap-2">
                <InputField
                  {...field}
                  LeftIcon={<Icons.ShieldCheck size={16} />}
                  placeholder="Please enter email verification code"
                />
                <Button>
                  "Get Code"
                </Button>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="googleCode"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Google Code"
            </FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.ShieldCheck size={16} />}
                placeholder="Please enter Google verification code"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

const Modify = () => {
  const form = useFormContext<z.infer<typeof modifyFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col items-center justify-center gap-4">
        <QRCode
          value="https://example.com"
          size={256}
          bgColor="#ffffff"
          fgColor="#000000"
        />
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            B7EVMCULFZSBTPIBTEA7IKJEPGMWKUFG
          </div>
          <CopyButton text="B7EVMCULFZSBTPIBTEA7IKJEPGMWKUFG" />
        </div>
        <div className="text-sm text-red-400">
          <Trans>
            (Please keep this key safe for recovery in case of phone change or
            loss)
          </Trans>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                "Code"
              </FormLabel>
              <FormControl>
                <InputField
                  {...field}
                  LeftIcon={<Icons.ShieldCheck size={16} />}
                  placeholder="Please enter Google verification code"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}

enum ModifyGoogleVerificationStep {
  Verify = 'verify',
  Modify = 'modify',
}

const verifyFormSchema = z.object({
  emailCode: z.string().min(6).max(6),
  googleCode: z.string().min(6).max(6),
})

const modifyFormSchema = z.object({
  code: z.string().min(6).max(6),
})

const ModifyGoogleVerificationForm = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const { step, setStep, visible } = useStepContext()

  const formSchema = useMemo(() => {
    const schema =
      step === ModifyGoogleVerificationStep.Verify
        ? verifyFormSchema
        : verifyFormSchema.merge(modifyFormSchema)
    return schema
  }, [step])

  const form = useForm<
    z.infer<typeof verifyFormSchema> & z.infer<typeof modifyFormSchema>
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      emailCode: '',
      googleCode: '',
      code: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log('Modify Google Verification Code:', data)
    if (step === ModifyGoogleVerificationStep.Verify) {
      setStep(ModifyGoogleVerificationStep.Modify)
    } else {
    }
  }

  const { reset } = form

  useEffect(() => {
    if (!visible) {
      reset()
    }
  }, [visible, reset])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {children}
      </form>
    </Form>
  )
}

const Context = createContext<{
  email: string
  visible: boolean
  step: ModifyGoogleVerificationStep
  setStep: React.Dispatch<React.SetStateAction<ModifyGoogleVerificationStep>>
}>({
  email: '',
  visible: false,
  step: ModifyGoogleVerificationStep.Verify,
  setStep: () => {},
})

const useStepContext = () => {
  return use(Context)
}

export const ModifyGoogleVerificationModal = create(
  ({ email }: { email: string }) => {
    const modal = useModal()
    const [step, setStep] = useState(ModifyGoogleVerificationStep.Verify)

    useEffect(() => {
      if (modal.visible) {
        setStep(ModifyGoogleVerificationStep.Verify)
      }
    }, [modal.visible, setStep])

    return (
      <Dialog open={modal.visible} onOpenChange={modal.hide}>
        <DialogContent aria-describedby={undefined}>
          <Context value={{ visible: modal.visible, step, setStep, email }}>
            <ModifyGoogleVerificationForm>
              <DialogHeader>
                <DialogTitle>
                  "Modify Google Verification"
                </DialogTitle>
                {step === ModifyGoogleVerificationStep.Modify && (
                  <DialogDescription>
                    <Trans>
                      Please use the Google Authenticator app to scan the QR
                      code below, or manually enter the key. Enter the generated
                      6-digit Google verification code into the verification
                      box.
                    </Trans>
                  </DialogDescription>
                )}
              </DialogHeader>

              {step === ModifyGoogleVerificationStep.Verify && <Verify />}
              {step === ModifyGoogleVerificationStep.Modify && <Modify />}

              <DialogFooter className="pt-4">
                <Button type="submit" block>
                  {step === ModifyGoogleVerificationStep.Verify ? (
                    "Next Step"
                  ) : (
                    "Confirm"
                  )}
                </Button>
              </DialogFooter>
            </ModifyGoogleVerificationForm>
          </Context>
        </DialogContent>
      </Dialog>
    )
  },
)
