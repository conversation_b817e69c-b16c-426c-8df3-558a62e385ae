import { create, useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
// import { Trans } from '@lingui/react/macro'
import { createContext, use, useEffect, useMemo, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { z } from 'zod'

import {
  <PERSON>ton,
  <PERSON>alog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
} from '@/components/ui'

const Verify = () => {
  const form = useFormContext<z.infer<typeof verifyFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        control={form.control}
        name="emailCode"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Code"
            </FormLabel>
            <FormControl>
              <div className="flex items-center gap-2">
                <InputField
                  {...field}
                  LeftIcon={<Icons.ShieldCheck size={16} />}
                  placeholder="Please enter email verification code"
                />
                <Button>
                  "Get Code"
                </Button>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

const Modify = () => {
  const form = useFormContext<z.infer<typeof modifyFormSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        control={form.control}
        name="newEmail"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Email"
            </FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.Mail size={16} />}
                placeholder="Please enter new email"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="newEmailCode"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Code"
            </FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.ShieldCheck size={16} />}
                placeholder="Please enter email verification code"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

enum ModifyEmailStep {
  Verify = 'verify',
  Modify = 'modify',
}

const verifyFormSchema = z.object({
  emailCode: z.string().min(6).max(6),
})

const modifyFormSchema = z.object({
  newEmail: z.string().email(),
  newEmailCode: z.string().min(6).max(6),
})

const ModifyEmailForm = ({ children }: { children: React.ReactNode }) => {
  const { step, setStep, visible } = useStepContext()

  const formSchema = useMemo(() => {
    const schema =
      step === ModifyEmailStep.Verify
        ? verifyFormSchema
        : verifyFormSchema.merge(modifyFormSchema)
    return schema
  }, [step])

  const form = useForm<
    z.infer<typeof formSchema> & z.infer<typeof modifyFormSchema>
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      emailCode: '',
      newEmail: '',
      newEmailCode: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log('Modify Email Data:', data)
    if (step === ModifyEmailStep.Verify) {
      setStep(ModifyEmailStep.Modify)
    } else {
      // Handle final submission
    }
  }

  const { reset } = form

  useEffect(() => {
    if (!visible) {
      reset()
    }
  }, [visible, reset])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {children}
      </form>
    </Form>
  )
}

const Context = createContext<{
  email: string
  visible: boolean
  step: ModifyEmailStep
  setStep: React.Dispatch<React.SetStateAction<ModifyEmailStep>>
}>({
  email: '',
  visible: false,
  step: ModifyEmailStep.Verify,
  setStep: () => {},
})

const useStepContext = () => {
  return use(Context)
}

export const ModifyEmailModal = create<{
  email: string
}>(({ email }) => {
  const modal = useModal()
  const [step, setStep] = useState(ModifyEmailStep.Verify)

  useEffect(() => {
    if (modal.visible) {
      setStep(ModifyEmailStep.Verify)
    }
  }, [modal.visible])

  return (
    <Dialog open={modal.visible} onOpenChange={modal.hide}>
      <DialogContent aria-describedby={undefined}>
        <Context value={{ visible: modal.visible, step, setStep, email }}>
          <ModifyEmailForm>
            <DialogHeader>
              <DialogTitle>
                "Modify Email"
              </DialogTitle>
            </DialogHeader>

            {step === ModifyEmailStep.Verify && <Verify />}
            {step === ModifyEmailStep.Modify && <Modify />}

            <DialogFooter className="pt-4">
              <Button type="submit" block>
                <Trans>
                  {step === ModifyEmailStep.Verify ? 'Next Step' : 'Confirm'}
                </Trans>
              </Button>
            </DialogFooter>
          </ModifyEmailForm>
        </Context>
      </DialogContent>
    </Dialog>
  )
})
