'use client'

import { show } from '@ebay/nice-modal-react'

// import { Trans } from '@lingui/react/macro'
import type { CardColumnProps } from '@/components/card/card-column'
import { CardColumn } from '@/components/card/card-column'
import { Switch } from '@/components/ui'

import { EditNotificationEmailModal } from './edit-notification-email-modal'

export const Content = () => {
  const notificationColumns: CardColumnProps['columns'] = [
    {
      label: "Announcement Notifications (Email)",
      value: () => {
        return (
          <div>
            <Switch />
          </div>
        )
      },
    },
    {
      label: "Email Daily Mining Earnings Report",
      value: () => {
        return (
          <div>
            <Switch />
          </div>
        )
      },
    },
    {
      label: "Email Monthly Mining Earnings Report",
      value: () => {
        return (
          <div>
            <Switch />
          </div>
        )
      },
    },
  ]

  const alertColumns: CardColumnProps['columns'] = [
    {
      label: "Alert Frequency",
      value: () => {
        return <div>1 hour</div>
      },
    },
    {
      label: "Alert Notifications",
      value: () => {
        return (
          <div>
            <Switch />
          </div>
        )
      },
    },
    {
      label: "Notification Email",
      value: () => {
        return <div><EMAIL></div>
      },
      actions: [
        {
          label: "Edit",
          onClick: () => {
            show(EditNotificationEmailModal, {
              email: '<EMAIL>',
            })
          },
        },
      ],
    },
  ]

  return (
    <div className="flex flex-col gap-6">
      <CardColumn
        title={"Notification Settings"}
        columns={notificationColumns}
      />
      <CardColumn
        title={"Alert Notification Settings"}
        columns={alertColumns}
      />
    </div>
  )
}
