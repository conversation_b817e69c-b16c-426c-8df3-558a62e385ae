// import { Trans } from '@lingui/react/macro'

import type { MenuGroup } from '@/components/menu/menu'
import { Icons } from '@/components/ui'

export function getMenuList(pathname: string): MenuGroup[] {
  return [
    {
      groupLabel: '',
      menus: [
        {
          href: '/setting/account',
          label: "Account Settings",
          active: pathname.includes('/setting/account'),
          icon: Icons.UserRoundCog,
          submenus: [],
        },
        {
          href: '/setting/withdraw',
          label: "Withdrawal Settings",
          active: pathname.includes('/setting/withdraw'),
          icon: Icons.HandCoins,
          submenus: [],
        },
        {
          href: '/setting/notification',
          label: "Notification Settings",
          active: pathname.includes('/setting/notification'),
          icon: Icons.BellDot,
          submenus: [],
        },
      ],
    },
  ]
}
