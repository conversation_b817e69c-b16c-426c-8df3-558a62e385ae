'use client'

import { usePathname } from 'next/navigation'
import { useMemo } from 'react'

import { SidebarMenuLayout } from '@/components/menu/sidebar-menu-layout'

import { getMenuList } from './menu-list'

export function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const menuList = useMemo(() => getMenuList(pathname), [pathname])

  return <SidebarMenuLayout menuList={menuList}>{children}</SidebarMenuLayout>
}
