'use client'

// import { Trans } from '@lingui/react/macro'
import { useAtom } from 'jotai'
import { useEffect } from 'react'

import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  IconButton,
  Icons,
} from '@/components/ui'
import { DOGE_MINE_POOL_APP } from '@/constants'

import { ForgotPasswordStep, forgotPasswordStepAtom } from '../_atom'
import { ForgotPasswordForm } from './forgot-password-form'
import { ResetPassword } from './reset-password'
import { VerifyEmail } from './verify-email'

export const ForgotPasswordPanel = () => {
  const [forgotPasswordStep, setForgotPasswordStep] = useAtom(
    forgotPasswordStepAtom,
  )

  useEffect(() => {
    setForgotPasswordStep(ForgotPasswordStep.VerifyEmail)
  }, [])

  return (
    <Card className="w-[420px]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconButton
            variant="outline"
            size="sm"
            href={DOGE_MINE_POOL_APP.emailLogin}
            onClick={(e) => {
              if (forgotPasswordStep === ForgotPasswordStep.ResetPassword) {
                e.preventDefault()
                setForgotPasswordStep(ForgotPasswordStep.VerifyEmail)
              }
            }}
          >
            <Icons.ChevronLeft size={16} />
          </IconButton>
          "Reset Password"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ForgotPasswordForm>
          {forgotPasswordStep === ForgotPasswordStep.VerifyEmail && (
            <VerifyEmail />
          )}
          {forgotPasswordStep === ForgotPasswordStep.ResetPassword && (
            <ResetPassword />
          )}
          <Button type="submit" className="mt-6" block>
            {forgotPasswordStep === ForgotPasswordStep.VerifyEmail ? (
              "Next Step"
            ) : (
              "Confirm"
            )}
          </Button>
        </ForgotPasswordForm>
      </CardContent>
    </Card>
  )
}
