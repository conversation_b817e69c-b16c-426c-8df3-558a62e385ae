'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useAtom } from 'jotai'
import { useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Form } from '@/components/ui'

import { ForgotPasswordStep, forgotPasswordStepAtom } from '../_atom'

export const verifyEmailSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6),
})

export const resetPasswordSchema = z.object({
  password: z
    .string()
    .regex(
      /^(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*])[A-Z\d!@#$%^&*]{8,12}$/i,
      'Password must be 8-12 characters long and include letters, numbers, and special characters',
    ),
  confirmPassword: z.string().min(8),
})

export const ForgotPasswordForm = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [forgotPasswordStep, setForgotPasswordStep] = useAtom(
    forgotPasswordStepAtom,
  )

  const formSchema = useMemo(() => {
    const schema =
      forgotPasswordStep === ForgotPasswordStep.VerifyEmail
        ? verifyEmailSchema
        : verifyEmailSchema
            .merge(resetPasswordSchema)
            .refine((data) => data.password === data.confirmPassword, {
              message: 'Passwords do not match',
              path: ['confirmPassword'],
            })
    return schema
  }, [forgotPasswordStep])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      code: '',
      password: '',
      confirmPassword: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log(data)
    if (forgotPasswordStep === ForgotPasswordStep.VerifyEmail) {
      setForgotPasswordStep(ForgotPasswordStep.ResetPassword)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>{children}</form>
    </Form>
  )
}
