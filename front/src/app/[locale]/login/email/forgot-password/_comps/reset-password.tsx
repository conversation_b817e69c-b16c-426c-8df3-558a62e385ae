'use client'

// import { Trans } from '@lingui/react/macro'
import { useFormContext } from 'react-hook-form'
import type { z } from 'zod'

import { Icons, PasswordField } from '@/components/ui'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

import type { resetPasswordSchema } from './forgot-password-form'

export const ResetPassword = () => {
  const form = useFormContext<z.infer<typeof resetPasswordSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        name="password"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "New Password"
            </FormLabel>
            <FormControl>
              <PasswordField
                {...field}
                LeftIcon={<Icons.Lock size={16} />}
                placeholder="Please enter new password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="confirmPassword"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Re-enter New Password"
            </FormLabel>
            <FormControl>
              <PasswordField
                {...field}
                LeftIcon={<Icons.Lock size={16} />}
                placeholder="Re-enter new password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
