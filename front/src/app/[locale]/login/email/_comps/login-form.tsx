'use client'

import { zodR<PERSON>olver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import { IconButton, Icons, InputField, PasswordField } from '@/components/ui'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { DOGE_MINE_POOL_APP } from '@/constants'

export const LoginForm = () => {
  const { t } = useTranslation()
  const formSchema = z.object({
    email: z.string().email(),
    password: z.string().min(8),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="w-[420px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconButton
                variant="outline"
                size="sm"
                href={DOGE_MINE_POOL_APP.login}
              >
                <Icons.ChevronLeft size={16} />
              </IconButton>
              {t('auth.login')}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col gap-4">
            <FormField
              name="email"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <InputField
                      {...field}
                      LeftIcon={<Icons.Mail size={16} />}
                      placeholder="Please enter your email"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="password"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('auth.password')}
                  </FormLabel>
                  <FormControl>
                    <PasswordField
                      {...field}
                      LeftIcon={<Icons.Lock size={16} />}
                      placeholder="Please enter your password"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="text-right">
              <Button
                variant="link"
                size="none"
                href={DOGE_MINE_POOL_APP.emailForgotPassword}
              >
                {t('auth.forgotPassword')}
              </Button>
            </div>
            <Button type="submit">
              {t('auth.signIn')}
            </Button>
          </CardContent>
        </Card>
      </form>
    </Form>
  )
}
