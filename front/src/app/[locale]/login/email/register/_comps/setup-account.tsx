'use client'

// import { Trans } from '@lingui/react/macro'
import { useFormContext } from 'react-hook-form'
import type { z } from 'zod'

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
  PasswordField,
} from '@/components/ui'

import type { setupAccountSchema } from './register-form'

export const SetupAccount = () => {
  const form = useFormContext<z.infer<typeof setupAccountSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        name="username"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Username"
            </FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.User size={16} />}
                placeholder="Set your username"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="password"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Password"
            </FormLabel>
            <FormControl>
              <PasswordField
                {...field}
                LeftIcon={<Icons.Lock size={16} />}
                placeholder="Set your password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="confirmPassword"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Confirm Password"
            </FormLabel>
            <FormControl>
              <PasswordField
                {...field}
                LeftIcon={<Icons.Lock size={16} />}
                placeholder="Re-enter your password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
