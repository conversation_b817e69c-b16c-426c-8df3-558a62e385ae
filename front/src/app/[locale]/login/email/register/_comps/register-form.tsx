'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useAtom } from 'jotai'
import { useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Form } from '@/components/ui'

import { RegisterStep, registerStepAtom } from '../_atom'

export const verifyEmailSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6),
})

export const setupAccountSchema = z.object({
  username: z
    .string()
    .regex(
      /^\w{6,8}$/,
      'Username must be 6-8 characters long and can include letters, numbers, and underscores',
    ),
  password: z
    .string()
    .regex(
      /^(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*])[A-Z\d!@#$%^&*]{8,12}$/i,
      'Password must be 8-12 characters long and include letters, numbers, and special characters',
    ),
  confirmPassword: z.string().min(8),
})

export const RegisterForm = ({ children }: { children: React.ReactNode }) => {
  const [registerStep, setRegisterStep] = useAtom(registerStepAtom)

  const formSchema = useMemo(() => {
    const schema =
      registerStep === RegisterStep.VerifyEmail
        ? verifyEmailSchema
        : verifyEmailSchema
            .merge(setupAccountSchema)
            .refine((data) => data.password === data.confirmPassword, {
              message: 'Passwords do not match',
              path: ['confirmPassword'],
            })
    return schema
  }, [registerStep])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      code: '',
      username: '',
      password: '',
      confirmPassword: '',
    },
  })

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log(data)
    if (registerStep === RegisterStep.VerifyEmail) {
      setRegisterStep(RegisterStep.SetupAccount)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>{children}</form>
    </Form>
  )
}
