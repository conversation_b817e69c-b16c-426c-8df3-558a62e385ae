'use client'

// import { Trans } from '@lingui/react/macro'
import { useFormContext } from 'react-hook-form'
import type { z } from 'zod'

import {
  Button,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
} from '@/components/ui'

import type { verifyEmailSchema } from './register-form'

export const VerifyEmail = () => {
  const form = useFormContext<z.infer<typeof verifyEmailSchema>>()

  return (
    <div className="flex flex-col gap-4">
      <FormField
        name="email"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Email"
            </FormLabel>
            <FormControl>
              <InputField
                {...field}
                LeftIcon={<Icons.Mail size={16} />}
                placeholder="Please enter your email"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="code"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              "Code"
            </FormLabel>
            <FormControl>
              <div className="flex gap-2">
                <InputField
                  {...field}
                  LeftIcon={<Icons.ShieldCheck size={16} />}
                  placeholder="Please enter email verification code"
                />
                <Button>
                  "Get Code"
                </Button>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
