'use client'

// import { Trans } from '@lingui/react/macro'
import { useAtom } from 'jotai'
import { useEffect } from 'react'

import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  IconButton,
  Icons,
} from '@/components/ui'
import { DOGE_MINE_POOL_APP } from '@/constants'

import { RegisterStep, registerStepAtom } from '../_atom'
import { RegisterForm } from '../_comps/register-form'
import { SetupAccount } from '../_comps/setup-account'
import { VerifyEmail } from './verify-email'

export const RegisterPanel = () => {
  const [registerStep, setRegisterStep] = useAtom(registerStepAtom)

  useEffect(() => {
    setRegisterStep(RegisterStep.VerifyEmail)
  }, [])

  return (
    <Card className="w-[420px]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconButton
            variant="outline"
            size="sm"
            href={DOGE_MINE_POOL_APP.login}
            onClick={(e) => {
              if (registerStep === RegisterStep.SetupAccount) {
                e.preventDefault()
                setRegisterStep(RegisterStep.VerifyEmail)
              }
            }}
          >
            <Icons.ChevronLeft size={16} />
          </IconButton>
          "Register"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <RegisterForm>
          {registerStep === RegisterStep.VerifyEmail && <VerifyEmail />}
          {registerStep === RegisterStep.SetupAccount && <SetupAccount />}
          <Button type="submit" className="mt-6" block>
            {registerStep === RegisterStep.VerifyEmail ? (
              "Next Step"
            ) : (
              "Confirm"
            )}
          </Button>
        </RegisterForm>
      </CardContent>
    </Card>
  )
}
