// import { Trans } from '@lingui/react/macro'

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Icons,
  Separator,
} from '@/components/ui'
import { DOGE_MINE_POOL_APP } from '@/constants'

export const SeparatorOr = () => (
  <div className="flex items-center gap-2 py-4">
    <Separator className="flex-1" />
    <div className="text-sm text-muted-foreground">
      "Or"
    </div>
    <Separator className="flex-1" />
  </div>
)

export const LoginPanel = () => {
  return (
    <Card className="w-[420px]">
      <CardHeader>
        <CardTitle>
          "Login"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <Button
            block
            LeftIcon={<Icons.Mail size={16} />}
            href={DOGE_MINE_POOL_APP.emailLogin}
          >
            "Email Login"
          </Button>
          <div className="text-center text-sm">
            <Trans>
              Don&apos;t have an account?{' '}
              <Button
                variant="link"
                size="none"
                href={DOGE_MINE_POOL_APP.emailRegister}
              >
                Register
              </Button>
            </Trans>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
