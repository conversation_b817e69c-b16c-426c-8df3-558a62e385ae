'use client'

// import { Trans } from '@lingui/react/macro'
import type { ColumnDef } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useMemo } from 'react'

import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Icons,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui'

const Assets = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          "My Assets"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div>
          <div className="text-sm text-muted-foreground">
            "Asset Valuation (BTC)"
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <p className="text-xl font-medium">0.11235678</p>
              <p className="text-xs text-muted-foreground">~$1,234.11</p>
            </div>
            <Button
              size="sm"
              href="/assets/history"
              RightIcon={<Icons.ArrowRight className="ml-1 size-4" />}
            >
              "History"
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const columns: ColumnDef<{
  currency: string
  balance: string
  value: string
}>[] = [
  {
    header: 'Currency',
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-2">
          <div className="size-8 rounded-full bg-muted" />
          <p>{row.original.currency}</p>
        </div>
      )
    },
  },
  {
    header: 'Balance',
    cell: ({ row }) => row.original.balance,
  },
  {
    header: 'Value',
    cell: ({ row }) => row.original.value,
  },
  {
    header: 'Actions',
    cell: ({ row }) => {
      return (
        <Button variant="link" size="sm">
          "Withdraw"
        </Button>
      )
    },
  },
]

const CurrencyTable = () => {
  const { data } = {
    data: [
      {
        currency: 'BTC',
        balance: '0.11235678',
        value: '$1,234.11',
      },
      {
        currency: 'ETH',
        balance: '0.11235678',
        value: '$1,234.11',
      },
      {
        currency: 'USDT',
        balance: '0.11235678',
        value: '$1,234.11',
      },
    ],
  }

  const table = useReactTable({
    data: useMemo(() => data, [data]),
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <Card>
      <CardContent className="pt-6">
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export const Content = () => {
  return (
    <div className="flex flex-col gap-6 p-6">
      <Assets />
      <CurrencyTable />
    </div>
  )
}
