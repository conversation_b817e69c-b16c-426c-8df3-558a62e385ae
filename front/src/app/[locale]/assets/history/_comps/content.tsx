'use client'

// import { Trans, useLingui } from '@lingui/react/macro'
import type { ColumnDef } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useMemo } from 'react'

import { Crumbs } from '@/components/common/crumbs'
import {
  Card,
  CardContent,
  Segmented,
  SegmentedList,
  SegmentedTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui'

const columns: ColumnDef<{
  time: string
  currency: string
  type: string
  amount: string
  address: string
  txid: string
  status: string
}>[] = [
  {
    header: 'Time',
    cell: ({ row }) => row.original.time,
  },
  {
    header: 'Currency',
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-2">
          <div className="size-8 rounded-full bg-muted" />
          <p>{row.original.currency}</p>
        </div>
      )
    },
  },
  {
    header: 'Type',
    cell: ({ row }) => row.original.type,
  },
  {
    header: 'Amount',
    cell: ({ row }) => row.original.amount,
  },
  {
    header: 'Address',
    cell: ({ row }) => row.original.address,
  },
  {
    header: 'Transaction ID',
    cell: ({ row }) => row.original.txid,
  },
  {
    header: 'Status',
    cell: ({ row }) => row.original.status,
  },
]

const HistoryTable = () => {
  const { data } = {
    data: [
      {
        time: '2024-01-01 10:00:00',
        currency: 'BTC',
        type: 'Deposit',
        amount: '0.11235678',
        address: '0x1234567890',
        txid: '1234567890',
        status: 'Completed',
      },
      {
        time: '2024-01-01 10:00:00',
        currency: 'ETH',
        type: 'Withdraw',
        amount: '0.11235678',
        address: '0x1234567890',
        txid: '1234567890',
        status: 'Pending',
      },
      {
        time: '2024-01-01 10:00:00',
        currency: 'USDT',
        type: 'Transfer',
        amount: '0.11235678',
        address: '0x1234567890',
        txid: '1234567890',
        status: 'Completed',
      },
    ],
  }

  const table = useReactTable({
    data: useMemo(() => data, [data]),
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <Card>
      <CardContent className="pt-4">
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export function Content() {
  // const { t } = useLingui()

  return (
    <div className="flex flex-col gap-4 p-6">
      <Crumbs
        items={[
          { label: `Assets`, href: '/assets' },
          { label: `History`, href: '/assets/history' },
        ]}
      />

      <div className="flex flex-col gap-4">
        <Segmented defaultValue="all">
          <SegmentedList>
            <SegmentedTrigger value="all" className="min-w-[110px]">
              "All"
            </SegmentedTrigger>
            <SegmentedTrigger value="completed" className="min-w-[110px]">
              "Completed"
            </SegmentedTrigger>
            <SegmentedTrigger value="pending" className="min-w-[110px]">
              "Pending"
            </SegmentedTrigger>
          </SegmentedList>
        </Segmented>
        <HistoryTable />
      </div>
    </div>
  )
}
