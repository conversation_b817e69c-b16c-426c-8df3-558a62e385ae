'use client'

import dynamic from 'next/dynamic'
import { useEffect, useMemo, useRef, useState } from 'react'

import type {DashboardOverview} from '@/lib/api/services';
import { api  } from '@/lib/api/services'
import { useWalletConnection } from '@/lib/hooks/use-wallet-connection'

import { NotConnectedDashboard } from './_comps/not-connected-dashboard'

const ReactECharts = dynamic(() => import('echarts-for-react'), { ssr: false })

export default function DashboardPage() {
  const { isConnected } = useWalletConnection()
  const [timeRange, setTimeRange] = useState<'24 h' | '7 d' | '30 d'>('24 h')
  const [dashboardData, setDashboardData] = useState<DashboardOverview | null>(null)
  const [chartData, setChartData] = useState<{
    range: string
    unit: string
    data: Array<{
      timestamp: number
      hashrate: number
    }>
  } | null>(null)
  const [loading, setLoading] = useState(false)
  const chartRef = useRef<any>(null);
  
  // 监听窗口大小变化，重新调整图表大小
  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current && chartRef.current.getEchartsInstance) {
        chartRef.current.getEchartsInstance().resize();
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    // 组件挂载后立即调整一次大小
    setTimeout(() => {
      handleResize();
    }, 100);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 获取仪表盘数据
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!isConnected) return;
      
      setLoading(true);
      try {
        const response = await api.dashboard.getOverview()
        if (response.success && response.data) {

          setDashboardData(response.data)
        } else {
          console.warn('⚠️ 仪表盘 API 返回了空数据或失败状态：', response)
        }
      } catch (error) {
        console.error('❌ 仪表盘 API 调用失败：', error)
        if (error instanceof Error) {
          console.error('错误详情：', {
            message: error.message,
            name: error.name,
            stack: error.stack
          })
        }
      } finally {
        setLoading(false);
      }
    }

    fetchDashboardData()
  }, [isConnected]);

  // 获取图表数据
  useEffect(() => {
    const fetchChartData = async () => {
      if (!isConnected) return;
      
      try {
        // 根据当前选择的时间范围映射到 API 参数
        const rangeMap = {
          '24 h': '24h',
          '7 d': '7d', 
          '30 d': '30d'
        }
        const range = rangeMap[timeRange]
        
        const response = await api.dashboard.getChartData('hashrate', range)
        if (response.success && response.data) {
          console.log('✅ 图表数据获取成功：', response.data)
          setChartData(response.data)
        } else {
          console.warn('⚠️ 图表 API 返回了空数据或失败状态：', response)
          setChartData(null)
        }
      } catch (error) {
        console.error('❌ 图表 API 调用失败：', error)
        if (error instanceof Error) {
          console.error('错误详情：', {
            message: error.message,
            name: error.name,
            stack: error.stack
          })
        }
        setChartData(null)
      }
    }

    fetchChartData()
  }, [isConnected, timeRange]);

  // 获取账户列表数据
  useEffect(() => {
    const fetchAccountsData = async () => {
      if (!isConnected) return;
      
      try {
        const response = await api.dashboard.getAccounts()
        if (response.success && response.data) {
          console.log('✅ 账户列表数据获取成功：', response.data)
        } else {
          console.warn('⚠️ 账户列表 API 返回了空数据或失败状态：', response)
        }
      } catch (error) {
        console.error('❌ 账户列表 API 调用失败：', error)
        if (error instanceof Error) {
          console.error('错误详情：', {
            message: error.message,
            name: error.name,
            stack: error.stack
          })
        }
      }
    }

    fetchAccountsData()
  }, [isConnected]);

  // 格式化数字显示
  const formatNumber = (value: string | number | undefined, unit = '') => {
    if (value === undefined || value === null) return '--'
    
    const numValue = typeof value === 'string' ? Number.parseFloat(value) : value
    if (Number.isNaN(numValue)) return '--'
    
    if (numValue >= 1e12) {
      return `${(numValue / 1e12).toFixed(2)}T ${unit}`
    } else if (numValue >= 1e9) {
      return `${(numValue / 1e9).toFixed(2)}B ${unit}`
    } else if (numValue >= 1e6) {
      return `${(numValue / 1e6).toFixed(2)}M ${unit}`
    } else if (numValue >= 1e3) {
      return `${(numValue / 1e3).toFixed(2)}K ${unit}`
    } else {
      return `${numValue.toFixed(2)} ${unit}`
    }
  }

  // 格式化哈希率显示
  const formatHashrate = (value: string | number | undefined) => {
    if (value === undefined || value === null) return '-- Ph/s'
    const numValue = typeof value === 'string' ? Number.parseFloat(value) : value
    if (Number.isNaN(numValue)) return '-- Ph/s'
    return `${numValue.toFixed(2)} Ph/s`
  }

  // 格式化收益显示
  const formatEarnings = (value: string | number | undefined) => {
    if (value === undefined || value === null) return '--'
    const numValue = typeof value === 'string' ? Number.parseFloat(value) : value
    if (Number.isNaN(numValue)) return '--'
    return `${numValue.toFixed(2)}`
  }
  
  // 处理图表实例
  const onChartReady = (echarts: any) => {
    chartRef.current = echarts;
  };
  
  // ECharts 配置
  const getChartOption = useMemo(() => {
    // 处理图表数据
    const labels: string[] = []
    const data: number[] = []
    
    if (chartData && chartData.data.length > 0) {
      chartData.data.forEach(item => {
        // 将时间戳转换为可读的时间格式
        const date = new Date(item.timestamp * 1000)
        const timeString = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
        labels.push(timeString)
        data.push(item.hashrate)
      })
    }

    return {
      backgroundColor: 'rgba(0,0,0,0)',
      title: {
        text: 'Hashrate(ph/s)',
        left: '20px',
        top: '10px',
        textStyle: {
          fontFamily: 'Styrene B',
          fontSize: 12,
          fontWeight: 400,
          color: '#CC785C'
        }
      },
      grid: {
        left: '60px',
        right: '20px',
        bottom: '50px',
        top: '50px',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#999',
          fontSize: 11
        },
        data: labels.length > 0 ? labels : ['6/24 10:00', '6/24 13:00', '6/24 16:00', '6/24 19:00', '6/24 22:00', '6/25 1:00', '6/25 4:00']
      },
      yAxis: {
        type: 'value',
        min(value: any) {
          return Math.floor(value.min * 0.9);
        },
        max(value: any) {
          return Math.ceil(value.max * 1.1);
        },
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          formatter: '{value}',
          color: '#999',
          fontSize: 11
        }
      },
      series: [
        {
          name: 'Hashrate',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 2,
            color: '#E27152'
          },
          itemStyle: {
            color: '#E27152',
            borderWidth: 2,
            borderColor: '#FFFFFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(226, 113, 82, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(226, 113, 82, 0)'
                }
              ]
            }
          },
          data: data.length > 0 ? data : [700, 820, 550, 750, 650, 580, 450]
        }
      ]
    }
  }, [chartData])

  // 根据钱包连接状态显示不同内容
  if (!isConnected) {
    return <NotConnectedDashboard />
  }

  // 已连接钱包时显示的 Dashboard
  return (
      <div className="flex min-h-screen w-full flex-col bg-[#FAF9F5] p-4 md:p-6">
        <h1 className="mb-6 text-2xl font-bold text-gray-900">Dashboard</h1>

        {/* 白色大卡片容器 */}
        <div className="rounded-2xl bg-white p-2 shadow-sm">
          {/* 数据卡片网格 - 顶部行 */}
          <div className="mb-[8px] grid grid-cols-2 gap-[8px] md:grid-cols-4">
            {/* 24h Hashrate */}
            <div className="h-[146.59px] w-full rounded-2xl bg-[#F7F5EE] p-4 px-6">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-[#ECE8E0]">
                  <img src={"/images/dashboard-hashrate.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">24 h Hashrate*</span>
              <div className="mt-2" style={{ fontFamily: 'Styrene B', fontWeight: 500, fontSize: '20px', lineHeight: '30px', color: '#141413' }}>
                {loading ? '加载中...' : formatHashrate(dashboardData?.hashrate_24h)}
              </div>
            </div>

            {/* 24h Shares */}
            <div className="h-[146.59px] w-full rounded-2xl bg-[#F7F5EE] p-4 px-6">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-white/50">
                  <img src={"/images/dashboard-Shares.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">24 h Shares*</span>
              <div className="mt-2" style={{ fontFamily: 'Styrene B', fontWeight: 500, fontSize: '20px', lineHeight: '30px', color: '#141413' }}>
                {loading ? '加载中...' : formatNumber(dashboardData?.shares_24h, 'sh')}
              </div>
            </div>

            {/* 1h Hashrate */}
            <div className="h-[146.59px] w-full rounded-2xl bg-[#F7F5EE] p-4 px-6">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-[#ECE8E0]">
                  <img src={"/images/dashboard-hashrate.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">1 h Hashrate*</span>
              <div className="mt-2" style={{ fontFamily: 'Styrene B', fontWeight: 500, fontSize: '20px', lineHeight: '30px', color: '#141413' }}>
                {loading ? '加载中...' : formatHashrate(dashboardData?.hashrate_1h)}
              </div>
            </div>

            {/* 1h Shares */}
            <div className="h-[146.59px] w-full rounded-2xl bg-[#F7F5EE] p-4 px-6">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-white/50">
                  <img src={"/images/dashboard-Shares.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">1 h Shares*</span>
              <div className="mt-2" style={{ fontFamily: 'Styrene B', fontWeight: 500, fontSize: '20px', lineHeight: '30px', color: '#141413' }}>
                {loading ? '加载中...' : formatNumber(dashboardData?.shares_1h, 'sh')}
              </div>
            </div>
          </div>

          {/* 中间行 - 两列卡片 */}
          <div className="mb-[8px] grid grid-cols-1 gap-[8px] md:grid-cols-2">
            {/* Total Staked */}
            <div className="relative flex h-[146.59px] flex-col overflow-hidden rounded-2xl bg-[#F7F5EE] p-4 px-6">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-[#ECE8E0]">
                  <img src={"/images/dashboard-Staked.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">Total Staked</span>
              <div className="z-10 mt-2" style={{ fontFamily: 'Styrene B', fontWeight: 500, fontSize: '20px', lineHeight: '30px', color: '#141413' }}>
                {loading ? '加载中...' : formatHashrate(dashboardData?.total_staked)}
              </div>
              <div className="absolute right-0 top-0 h-full w-4/5">
                <img
                    src="/images/dashboard-Frame.svg"
                    alt="Background pattern"
                    className="size-full object-cover object-left"
                />
              </div>
            </div>

            {/* Price */}
            <div className="relative flex h-[146.59px] flex-col overflow-hidden rounded-2xl bg-[#F7F5EE] p-4 px-6">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-[#E2F7EF]">
                  <img src={"/images/dashboard-Price.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">xx Price</span>
              <div className="z-10 mt-2" style={{ fontFamily: 'Styrene B', fontWeight: 500, fontSize: '20px', lineHeight: '30px', color: '#141413' }}>
                {loading ? '加载中...' : '--'}
              </div>
              <div className="absolute right-0 top-0 h-full w-3/5">
                <img
                    src="/images/dashboard-price.png"
                    alt="Background pattern"
                    className="size-full object-cover object-left"
                />
              </div>
            </div>
          </div>

          {/* 底部网格 - 四个卡片 */}
          <div className="mb-[8px] grid grid-cols-1 gap-[8px] md:grid-cols-5">
            {/* TAO Earnings */}
            <div className="h-[146.59px] w-full rounded-2xl bg-[#F7F5EE] p-4 px-6 md:col-span-1">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-[#ECE8E0]">
                  <img src={"/images/dashboard-Tao.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">TAO Earnings</span>
              <div className="mt-2" style={{
                fontFamily: 'Styrene B',
                fontWeight: 500,
                fontSize: '20px',
                lineHeight: '30px',
                color: '#141413'
              }}>
                {loading ? '加载中...' : formatEarnings(dashboardData?.tao_earnings)}
              </div>
            </div>

            {/* Doge Earnings */}
            <div className="h-[146.59px] w-full rounded-2xl bg-[#F7F5EE] p-4 px-6 md:col-span-1">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-[#ECE8E0]">
                  <img src={"/images/dashboard-Doge.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">Doge Earnings</span>
              <div className="mt-2" style={{
                fontFamily: 'Styrene B',
                fontWeight: 500,
                fontSize: '20px',
                lineHeight: '30px',
                color: '#141413'
              }}>
                {loading ? '加载中...' : formatEarnings(dashboardData?.doge_earnings)}
              </div>
            </div>

            {/* LTC Earnings */}
            <div className="h-[146.59px] w-full rounded-2xl bg-[#F7F5EE] p-4 px-6 md:col-span-1">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-[#ECE8E0]">
                  <img src={"/images/dashboard-LTC.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">LTC Earnings</span>
              <div className="mt-2" style={{
                fontFamily: 'Styrene B',
                fontWeight: 500,
                fontSize: '20px',
                lineHeight: '30px',
                color: '#141413'
              }}>
                {loading ? '加载中...' : formatEarnings(dashboardData?.ltc_earnings)}
              </div>
            </div>

            {/* Active Miners */}
            <div
                className="relative h-[146.59px] w-full overflow-hidden rounded-2xl bg-[#F7F5EE] p-4 px-6 md:col-span-2">
              <div className="flex items-center">
                <div className="mr-2 flex size-8 items-center justify-center rounded-md bg-white/50">
                  <img src={"/images/dashboard-Active.svg"}/>
                </div>
              </div>
              <span className="text-xs text-gray-600">Active Miners*</span>
              <div className="mt-2" style={{
                fontFamily: 'Styrene B',
                fontWeight: 500,
                fontSize: '20px',
                lineHeight: '30px',
                color: '#141413'
              }}>
                {loading ? '加载中...' : dashboardData?.active_miners || '--'}
              </div>
              <div className="absolute right-0 top-0 h-full w-[50%]">
                <img
                    src="/images/dashboard-Active.png"
                    alt="Background pattern"
                    className="size-full object-cover object-left"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 图表区域 */}
        <div className="mt-6 w-full">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">Hashrate History</h2>
            <div className="flex items-center gap-2 rounded-full bg-[#E9E5DD] p-1">
              {(['24 h', '7 d', '30 d'] as const).map((range) => (
                  <button
                      key={range}
                      onClick={() => setTimeRange(range)}
                      className={`rounded-full px-4 py-1 text-sm transition-colors ${
                          timeRange === range
                              ? 'bg-white text-gray-900'
                              : 'text-gray-700 hover:bg-white/50'
                      }`}
                  >
                    {range}
                  </button>
              ))}
            </div>
          </div>

          <div className="h-[40vh] max-h-[500px] min-h-[300px] w-full rounded-2xl bg-[#F7F5EE] p-4 md:h-[45vh] md:max-h-[600px]">
            <ReactECharts
                option={getChartOption}
                onChartReady={onChartReady}
                style={{height: '100%', width: '100%'}}
                opts={{renderer: 'svg'}}
                className="size-full"
                theme="light"
                notMerge={true}
                lazyUpdate={false}
                onEvents={{
                  'finished': () => {
                    // 图表渲染完成后调整大小
                    if (chartRef.current && chartRef.current.getEchartsInstance) {
                      setTimeout(() => {
                        chartRef.current.getEchartsInstance().resize();
                      }, 0);
                    }
                  }
                }}
            />
          </div>
        </div>
      </div>
  )
} 