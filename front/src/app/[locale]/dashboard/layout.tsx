'use client'

import type { ReactNode} from 'react';
import { useEffect, useState } from 'react'

import { AppLayout } from '@/components/layout/app-layout'
import { Sidebar } from '@/components/sidebar/sidebar'

interface DashboardLayoutProps {
  children: ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isMobile, setIsMobile] = useState(false)
  
  // Check if the screen is mobile size
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768) // Consider 768px as mobile breakpoint
    }
    
    // Initial check
    checkIfMobile()
    
    // Listen for resize events
    window.addEventListener('resize', checkIfMobile)
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])
  
  if (isMobile) {
    // Mobile layout
    return <AppLayout>{children}</AppLayout>
  }
  
  // Desktop layout
  return (
    <div className="flex h-screen">
      <Sidebar />
      <main className="flex-1 overflow-y-auto">
        {children}
      </main>
    </div>
  )
} 