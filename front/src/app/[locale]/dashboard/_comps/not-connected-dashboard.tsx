'use client'

import { BittensorWalletConnect } from '@/components/web3/bittensor-wallet-connect'

export function NotConnectedDashboard() {
  return (
    <div 
      className="flex min-h-screen w-full flex-col items-center" 
      style={{ backgroundColor: '#E8E6DC' }}
    >
      {/* 主要容器 */}
      <div className="flex w-full max-w-md flex-col items-center gap-5 px-4 pb-8 pt-[120px] md:max-w-lg md:gap-6 md:pt-[150px] lg:max-w-xl">
        
        {/* 插画区域 */}
        <div className="w-full">
          <div 
            className="relative flex w-full items-center justify-center overflow-hidden rounded-xl"
            style={{ height: '202px' }}
          >
            {/* 使用 doge 角色插画 */}
            <div className="relative flex size-full items-center justify-center">
              <img 
                src="/images/flamingo.svg"
                alt="DogeMine Character" 
                className="max-h-full max-w-full object-contain opacity-80"
              />
            </div>
          </div>
        </div>

        {/* 标题和图标 */}
        <div className="flex items-center justify-center" style={{ gap: '12px' }}>
          <h1 
            className="text-center"
            style={{
              fontFamily: 'Styrene B',
              fontWeight: 400,
              fontSize: '28px',
              lineHeight: '1.2857142857142858',
              color: '#141413'
            }}
          >
            Connect wallet first
          </h1>
          
          {/* 使用真实的笑脸图标 */}
          <div className="flex size-7 items-center justify-center">
            <img 
              src="/images/smile.svg" 
              alt="Smile" 
              className="size-7"
            />
          </div>
        </div>

        {/* 描述文字 */}
        <div className="w-full max-w-[330px]">
          <p 
            className="text-center"
            style={{
              fontFamily: 'Styrene B',
              fontWeight: 400,
              fontSize: '16px',
              lineHeight: '1.5',
              color: '#141413'
            }}
          >
            Please connect your wallet to access and view your data
          </p>
        </div>
                 <BittensorWalletConnect
             isCollapsed={false}
             isMobile={false}
             variant="landing"
         />
      </div>
    </div>
  )
}