'use client'

import type { ReactNode} from 'react';
import { useEffect, useState } from 'react'

import { AppLayout } from '@/components/layout/app-layout'
import { Sidebar } from '@/components/sidebar/sidebar'

interface ValidatorLayoutProps {
  children: ReactNode
}

export default function ValidatorLayout({ children }: ValidatorLayoutProps) {
  const [isMobile, setIsMobile] = useState(false)
  
  // Check if the screen is mobile size
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768) // Consider 768px as mobile breakpoint
    }
    
    // Initial check
    checkIfMobile()
    
    // Listen for resize events
    window.addEventListener('resize', checkIfMobile)
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])
  
  if (isMobile) {
    // Mobile layout
    return <AppLayout>{children}</AppLayout>
  }
  
  // Desktop layout
  return (
      <div className="flex h-screen">
        <Sidebar/>
        <main className="flex-1 overflow-y-auto bg-[#FAF9F5] p-6 dark:bg-gray-900">
          {children}
        </main>
      </div>
  )
} 