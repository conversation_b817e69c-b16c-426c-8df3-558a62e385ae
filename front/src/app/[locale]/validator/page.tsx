'use client'

import 'react-datepicker/dist/react-datepicker.css'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useState } from 'react'
import DatePicker from 'react-datepicker'

export default function ValidatorPage() {
  const params = useParams()
  const locale = params.locale as string
  const [activeTab, setActiveTab] = useState('validator')
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  
  // Wallet 状态管理
  const [ltcWallet, setLtcWallet] = useState({ name: '', address: '' })
  const [dogeWallet, setDogeWallet] = useState({ name: '', address: '' })

  // 检查输入是否完整
  const isLtcWalletValid = ltcWallet.name.trim() && ltcWallet.address.trim()
  const isDogeWalletValid = dogeWallet.name.trim() && dogeWallet.address.trim()

  // 钱包处理函数
  const handleAddLtcWallet = () => {
    if (isLtcWalletValid) {
      console.log('Adding LTC wallet:', ltcWallet)
      // 这里可以添加 API 调用来保存钱包信息
      alert(`LTC Wallet added: ${ltcWallet.name} - ${ltcWallet.address}`)
      setLtcWallet({ name: '', address: '' })
    }
  }

  const handleAddDogeWallet = () => {
    if (isDogeWalletValid) {
      console.log('Adding Doge wallet:', dogeWallet)
      // 这里可以添加 API 调用来保存钱包信息
      alert(`Doge Wallet added: ${dogeWallet.name} - ${dogeWallet.address}`)
      setDogeWallet({ name: '', address: '' })
    }
  }

  const validators = [
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      score: '98'
    }
  ]

  return (
    <>
      {/* Custom DatePicker Styles */}
      <style jsx global>{`
        .custom-datepicker .react-datepicker {
          font-family: 'Styrene B', sans-serif !important;
          border: 1px solid rgba(31, 30, 29, 0.15) !important;
          border-radius: 12px !important;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
          background-color: #FEFDFC !important;
        }
        
        .custom-datepicker .react-datepicker__header {
          background-color: #E8E6DC !important;
          border-bottom: 1px solid rgba(31, 30, 29, 0.15) !important;
          border-radius: 12px 12px 0 0 !important;
          padding: 16px !important;
        }
        
        .custom-datepicker .react-datepicker__current-month {
          color: #141413 !important;
          font-weight: 500 !important;
          font-size: 16px !important;
          font-family: 'Styrene B', sans-serif !important;
          margin-bottom: 8px !important;
        }
        
        .custom-datepicker .react-datepicker__day-names {
          margin-bottom: 8px !important;
        }
        
        .custom-datepicker .react-datepicker__day-name {
          color: #3D3D3A !important;
          font-size: 12px !important;
          font-weight: 500 !important;
          width: 2.5rem !important;
          line-height: 2rem !important;
          margin: 0.166rem !important;
        }
        
        .custom-datepicker .react-datepicker__day {
          color: #141413 !important;
          width: 2.5rem !important;
          line-height: 2.5rem !important;
          margin: 0.166rem !important;
          border-radius: 8px !important;
          font-size: 14px !important;
          transition: all 0.2s ease !important;
        }
        
        .custom-datepicker .react-datepicker__day:hover {
          background-color: #F0EEE6 !important;
          border-radius: 8px !important;
        }
        
        .custom-datepicker .react-datepicker__day--selected,
        .custom-datepicker .react-datepicker__day--range-start,
        .custom-datepicker .react-datepicker__day--range-end {
          background-color: #C96342 !important;
          color: white !important;
          border-radius: 8px !important;
        }
        
        .custom-datepicker .react-datepicker__day--keyboard-selected {
          background-color: #F0EEE6 !important;
          color: #141413 !important;
        }
        
        .custom-datepicker .react-datepicker__day--in-selecting-range,
        .custom-datepicker .react-datepicker__day--in-range {
          background-color: rgba(201, 99, 66, 0.15) !important;
          color: #141413 !important;
          border-radius: 0 !important;
        }
        
        .custom-datepicker .react-datepicker__day--disabled {
          color: #B0AEA5 !important;
          cursor: not-allowed !important;
        }
        
        .custom-datepicker .react-datepicker__navigation {
          top: 16px !important;
          width: 24px !important;
          height: 24px !important;
          border: none !important;
          background: none !important;
        }
        
        .custom-datepicker .react-datepicker__navigation--previous {
          left: 16px !important;
        }
        
        .custom-datepicker .react-datepicker__navigation--next {
          right: 16px !important;
        }
        
        .custom-datepicker .react-datepicker__navigation-icon::before {
          border-color: #73726C !important;
          border-width: 2px 2px 0 0 !important;
          width: 8px !important;
          height: 8px !important;
        }
        
        .custom-datepicker .react-datepicker__input-container input {
          width: 100% !important;
          background-color: white !important;
          border: 1px solid rgba(31, 30, 29, 0.15) !important;
          border-radius: 8px !important;
          padding: 8px 12px !important;
          font-family: 'Styrene B', sans-serif !important;
          font-size: 14px !important;
          color: #141413 !important;
          outline: none !important;
          transition: all 0.2s ease !important;
          min-width: 150px !important;
          height: 40px !important;
          box-sizing: border-box !important;
        }
        
        .custom-datepicker .react-datepicker__input-container input:focus {
          border-color: #C96342 !important;
          box-shadow: 0 0 0 3px rgba(201, 99, 66, 0.1) !important;
        }
        
        .custom-datepicker .react-datepicker__input-container input:hover {
          border-color: rgba(201, 99, 66, 0.3) !important;
        }
        
        .custom-datepicker .react-datepicker__input-container input::placeholder {
          color: #B0AEA5 !important;
        }
        
        .custom-datepicker .react-datepicker__triangle {
          display: none !important;
        }
      `}</style>
      
      <div className="flex w-full flex-col items-stretch justify-center gap-4 md:gap-6">
      {/* 钱包设置提醒横幅 */}
      <div 
        className="flex w-full items-center justify-between rounded-2xl p-4"
        style={{ backgroundColor: '#E8E6DC' }}
      >
        <div className="flex items-center gap-3">
          <div className="flex size-8 items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L13.5 8.5L20 7L14 12L20 17L13.5 15.5L12 22L10.5 15.5L4 17L10 12L4 7L10.5 8.5L12 2Z" stroke="#141413" strokeWidth="1.5" fill="none"/>
            </svg>
          </div>
          <div className="flex flex-col">
            <span className="text-base font-medium text-[#141413]">Wallet Setup Reminder</span>
            <span className="text-sm text-[#3D3D3A]">Please set up the reward wallets, otherwise you can not receive the earnings!</span>
          </div>
        </div>
        <button 
          className="rounded-lg bg-[#141413] px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-[#2D2D2B]"
        >
          Add Wallet
        </button>
      </div>

      {/* 主容器 */}
      <div
        className="flex w-full flex-col gap-6 rounded-2xl p-4 pb-3 md:gap-8 md:p-6"
        style={{ backgroundColor: '#FAF9F5' }}
      >
        {/* 标题区域 */}
        <div className="flex w-full items-stretch gap-4 pr-0 md:gap-6 md:pr-6">
          <div className="flex flex-1 flex-col gap-1">
            <div className="flex w-full items-center gap-4">
              <h1 className="font-serif text-xl font-semibold leading-[1.5em] text-[#141413] md:text-2xl">
                Validator Overview
              </h1>
            </div>
            <div className="flex min-h-[36px] flex-col justify-center">
              <p className="text-sm font-normal leading-[1.5em] text-[#3D3D3A] md:text-base">
                Monitor validator performance, network participation, and staking rewards across all nodes
              </p>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex w-full flex-col gap-4 md:gap-6">

          {/* 标签栏 */}
          <div className="flex w-full items-center gap-2.5">
            <div className="flex w-fit items-center justify-center gap-2 rounded-3xl bg-[#FAF9F5]">
              <button
                onClick={() => setActiveTab('validator')}
                className={`rounded-3xl px-3 py-2 text-center text-sm font-medium leading-[1.57em] transition-colors md:px-4 ${
                  activeTab === 'validator' 
                    ? 'bg-[#E8E6DC] text-[#141413]' 
                    : 'bg-[#E8E6DC] text-[#73726C]'
                }`}
              >
                Validator
              </button>
              <button
                onClick={() => setActiveTab('earning')}
                className={`rounded-3xl px-3 py-2 text-center text-sm font-medium leading-[1.57em] transition-colors md:px-4 ${
                  activeTab === 'earning' 
                    ? 'bg-[#E8E6DC] text-[#141413]' 
                    : 'bg-[#E8E6DC] text-[#73726C]'
                }`}
              >
                Earning
              </button>
              <button
                onClick={() => setActiveTab('wallet')}
                className={`rounded-3xl px-3 py-2 text-center text-sm font-medium leading-[1.57em] transition-colors md:px-4 ${
                  activeTab === 'wallet' 
                    ? 'bg-[#E8E6DC] text-[#141413]' 
                    : 'bg-[#E8E6DC] text-[#73726C]'
                }`}
              >
                Wallet
              </button>
            </div>
          </div>

          {/* 根据 activeTab 显示不同内容 */}
          {activeTab === 'validator' && (
            <>
              {/* 统计卡片区域 */}
              <div className="flex w-full flex-col gap-3">
                {/* 主要统计卡片 */}
                <div className="flex flex-col gap-1 rounded-lg px-2 py-4" style={{ backgroundColor: '#FEFDFC' }}>
                  <div
                    className="rounded-lg p-4"
                    style={{ backgroundColor: '#FEFDFC' }}
                  >
                    <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                      {/* 左侧 - TAO 信息 */}
                      <div className="flex flex-col gap-1 lg:flex-1">
                        <div className="flex items-center">
                          <span
                            className="text-2xl font-medium"
                            style={{
                              fontFamily: 'Styrene B',
                              color: '#141413'
                            }}
                          >
                            158.80 TAO
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span
                            className="text-sm"
                            style={{
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#73726C'
                            }}
                          >
                            Total Staked
                          </span>
                          <div
                            className="rounded-lg px-2 py-0.5"
                            style={{ backgroundColor: '#E5E4E7' }}
                          >
                            <span
                              className="text-sm"
                              style={{
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                color: '#38365D'
                              }}
                            >
                              Staking
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* 中间 - Node 信息 */}
                      <div className="lg:flex-0.5 flex flex-col gap-1 lg:ml-24">
                        <div className="flex items-center gap-2">
                          <span
                            className="text-base font-medium"
                            style={{
                              fontFamily: 'Styrene B',
                              color: '#141413'
                            }}
                          >
                            Node1
                          </span>
                          <div
                            className="rounded-lg px-2 py-0.5"
                            style={{ backgroundColor: '#E3EDE3' }}
                          >
                            <span
                              className="text-sm"
                              style={{
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                color: '#0B3629'
                              }}
                            >
                              Active
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span
                            className="text-sm"
                            style={{
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#73726C'
                            }}
                          >
                            ID
                          </span>
                        </div>
                      </div>

                      {/* 右侧 - 按钮组 */}
                      <div className="flex gap-3 lg:flex-1 lg:justify-end">
                        <button
                          className="rounded-lg border text-sm font-medium transition-colors hover:opacity-90"
                          style={{
                            width: '94px',
                            height: '40px',
                            backgroundColor: '#FAF9F5',
                            borderColor: 'rgba(31, 30, 29, 0.15)',
                            color: '#141413',
                            fontFamily: 'Styrene B',
                            padding: '0',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          Untake
                        </button>
                        <button
                          className="rounded-lg text-sm font-medium transition-colors hover:opacity-90"
                          style={{
                            width: '94px',
                            height: '40px',
                            backgroundColor: '#C96342',
                            color: '#FFFFFF',
                            fontFamily: 'Styrene B',
                            padding: '0',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          Stake
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 小统计卡片 */}
                <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
                  {[
                    { label: 'Miner', value: '1,024' },
                    { label: 'Latency', value: '86ms' },
                    { label: 'CPU', value: '95.2%' },
                    { label: 'Memory', value: '95.2%' }
                  ].map((item, index) => (
                    <div
                      key={index}
                      className="rounded-lg p-3"
                      style={{ backgroundColor: '#F4F2E7' }}
                    >
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center">
                          <span
                            className="text-sm"
                            style={{
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#73726C'
                            }}
                          >
                            {item.label}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span
                            className="text-lg font-medium"
                            style={{
                              fontFamily: 'Styrene B',
                              color: '#141413'
                            }}
                          >
                            {item.value}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 搜索栏 */}
                <div className="flex w-full items-center justify-between gap-4 rounded-2xl bg-[#FAF9F5] py-4">
                  <div className="max-w-md flex-1">
                    <div
                      className="rounded-lg border-0 bg-white px-4 py-2.5"
                      style={{ backgroundColor: '#F7F5EE' }}
                    >
                      <input
                        type="text"
                        placeholder="Search miners..."
                        className="w-full border-0 bg-transparent text-sm text-gray-600 placeholder-gray-400 focus:outline-none"
                        style={{
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          color: '#B0AEA5'
                        }}
                      />
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className="whitespace-nowrap text-xs"
                      style={{
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#B0AEA5'
                      }}
                    >
                      Last updated:15:00
                    </span>
                    <button
                      className="rounded border p-1.5 transition-colors hover:opacity-90"
                      style={{
                        backgroundColor: '#FAF9F5',
                        borderColor: 'rgba(31, 30, 29, 0.15)'
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M2 8a6 6 0 0 1 10.29-4.29" stroke="#73726C" strokeWidth="0.75" fill="none"/>
                        <path d="M14 8a6 6 0 0 1-10.29 4.29" stroke="#73726C" strokeWidth="0.75" fill="none"/>
                        <path d="M8 2v4l2 2" stroke="#141413" strokeWidth="0.75" fill="none"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              {/* 表格区域 */}
              <div className="flex w-full flex-col gap-4 md:gap-6">

        {/* 表格 */}
        <div 
          className="overflow-hidden rounded-2xl"
          style={{ backgroundColor: '#FAF9F5' }}
        >
          {/* 桌面端表格 */}
          <div className="hidden md:block">
            <table className="w-full">
              {/* 表格头部 */}
              <thead style={{ backgroundColor: '#E8E6DC' }}>
                <tr style={{ height: '42px' }}>
                  <th className="text-left" style={{ width: '150px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      Miner
                    </span>
                  </th>
                  <th className="text-left" style={{ width: '120px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      Status
                    </span>
                  </th>
                  <th className="text-left" style={{ width: '150px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      1h Hashrate
                    </span>
                  </th>
                  <th className="text-left" style={{ width: '150px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      24h Hashrate
                    </span>
                  </th>
                  <th className="text-left" style={{ width: '120px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      1h Shares
                    </span>
                  </th>
                  <th className="text-left" style={{ width: '120px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      24h Shares
                    </span>
                  </th>
                  <th className="text-left" style={{ width: '120px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      Score
                    </span>
                  </th>
                  <th className="text-left" style={{ width: '140px', paddingLeft: '8px', paddingRight: '8px' }}>
                    <span 
                      className="text-xs font-medium"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '1.5em',
                        color: '#3D3D3A'
                      }}
                    >
                      Operation
                    </span>
                  </th>
                </tr>
              </thead>

              {/* 表格内容 */}
              <tbody>
                {validators.map((validator, index) => (
                  <tr 
                    key={index}
                    style={{ 
                      backgroundColor: index === 3 ? '#F0EEE6' : 'transparent',
                      borderBottom: index < validators.length - 1 ? '1px solid #E8E6DC' : 'none'
                    }}
                  >
                    <td className="py-4" style={{ padding: '16px 8px', width: '150px' }}>
                      <span 
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.57em',
                          color: '#141413'
                        }}
                      >
                        {validator.id}
                      </span>
                    </td>
                    <td className="py-4" style={{ padding: '16px 8px', width: '120px' }}>
                      <div 
                        className="inline-flex items-center rounded-lg px-2 py-1"
                        style={{ backgroundColor: '#E3EDE3' }}
                      >
                        <span 
                          style={{ 
                            fontFamily: 'Styrene B',
                            fontWeight: 400,
                            fontSize: '14px',
                            lineHeight: '1.57em',
                            color: '#0B3629'
                          }}
                        >
                          {validator.status}
                        </span>
                      </div>
                    </td>
                    <td className="py-4" style={{ padding: '16px 8px', width: '150px' }}>
                      <span 
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.57em',
                          color: '#141413'
                        }}
                      >
                        {validator.hashrate1h}
                      </span>
                    </td>
                    <td className="py-4" style={{ padding: '16px 8px', width: '150px' }}>
                      <span 
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.57em',
                          color: '#141413'
                        }}
                      >
                        {validator.hashrate24h}
                      </span>
                    </td>
                    <td className="py-4" style={{ padding: '16px 8px', width: '120px' }}>
                      <span 
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.57em',
                          color: '#141413'
                        }}
                      >
                        {validator.shares1h}
                      </span>
                    </td>
                    <td className="py-4" style={{ padding: '16px 8px', width: '120px' }}>
                      <span 
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.57em',
                          color: '#141413'
                        }}
                      >
                        {validator.shares24h}
                      </span>
                    </td>
                    <td className="py-4" style={{ padding: '16px 8px', width: '120px' }}>
                      <span 
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.57em',
                          color: '#141413'
                        }}
                      >
                        {validator.score}
                      </span>
                    </td>
                    <td className="py-4" style={{ padding: '16px 8px', width: '140px' }}>
                      <Link href={`/${locale}/validator/${validator.id}`} className="flex items-center gap-1 transition-opacity hover:opacity-75">
                        <span 
                          style={{ 
                            fontFamily: 'Styrene B',
                            fontWeight: 500,
                            fontSize: '14px',
                            lineHeight: '1.57em',
                            color: '#141413'
                          }}
                        >
                          View details
                        </span>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path d="M6 12l4-4-4-4" stroke="#73726C" strokeWidth="1.2" fill="none"/>
                        </svg>
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 移动端表格 */}
          <div className="md:hidden">
            {/* 表格头部 - 移动端 */}
            <div 
              className="grid grid-cols-4 gap-2 px-4 py-3 text-xs font-medium"
              style={{ 
                backgroundColor: '#E8E6DC',
                fontFamily: 'Styrene B',
                fontWeight: 500,
                color: '#3D3D3A'
              }}
            >
              <div>Validator/Status</div>
              <div className="text-center">Hashrate</div>
              <div className="text-center">Shares</div>
              <div className="text-right">Score/Action</div>
            </div>

            {/* 表格内容 - 移动端 */}
            <div>
              {validators.map((validator, index) => (
                <div 
                  key={index}
                  className="grid grid-cols-4 gap-2 border-b border-gray-200 p-4"
                  style={{ 
                    backgroundColor: index === 3 ? '#F0EEE6' : 'transparent',
                    borderBottomColor: '#E8E6DC'
                  }}
                >
                  {/* 第一列：Validator + Status */}
                  <div className="space-y-2">
                    <div 
                      className="truncate text-sm"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#141413'
                      }}
                    >
                      {validator.id}
                    </div>
                    <div 
                      className="inline-flex items-center rounded px-2 py-0.5 text-xs"
                      style={{ 
                        backgroundColor: '#E3EDE3',
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#0B3629'
                      }}
                    >
                      {validator.status}
                    </div>
                  </div>

                  {/* 第二列：Hashrate */}
                  <div className="space-y-2 text-center text-xs">
                    <div 
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#141413'
                      }}
                    >
                      {validator.hashrate24h}
                    </div>
                    <div 
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#73726C',
                        fontSize: '10px'
                      }}
                    >
                      24h
                    </div>
                  </div>

                  {/* 第三列：Shares */}
                  <div className="space-y-2 text-center text-xs">
                    <div 
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#141413'
                      }}
                    >
                      {validator.shares24h}
                    </div>
                    <div 
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#73726C',
                        fontSize: '10px'
                      }}
                    >
                      24h
                    </div>
                  </div>

                  {/* 第四列：Score + Action */}
                  <div className="space-y-2 text-right">
                    <div 
                      className="text-sm"
                      style={{ 
                        fontFamily: 'Styrene B',
                        fontWeight: 400,
                        color: '#141413'
                      }}
                    >
                      {validator.score}
                    </div>
                    <Link href={`/${locale}/validator/${validator.id}`} className="ml-auto flex items-center gap-1 text-xs transition-opacity hover:opacity-75">
                      <span 
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontWeight: 500,
                          color: '#141413'
                        }}
                      >
                        Details
                      </span>
                      <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                        <path d="M6 12l4-4-4-4" stroke="#73726C" strokeWidth="1.2" fill="none"/>
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
              </>
            )}

            {/* Earning 标签页内容 */}
            {activeTab === 'earning' && (
              <>
                {/* 收益统计卡片 */}
                <div className="flex flex-col gap-3">
                  {/* 第一行：Yesterday Rewards */}
                  <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                    <div className="flex items-center gap-2 rounded-xl bg-[#F4F2E7] p-3">
                      <div className="flex size-8 items-center justify-center rounded-lg bg-[#F3E5F5]">
                        <img src={"/images/dashboard-Price.svg"}/>
                      </div>
                      <div className="flex flex-1 flex-col">
                        <span className="text-xs text-[#73726C]">Yesterday xxx Rewards</span>
                        <span className="text-sm font-medium text-[#141413]">158.80 USD</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 rounded-xl bg-[#F4F2E7] p-3">
                      <div className="flex size-8 items-center justify-center rounded-lg bg-[#E3F2FD]">
                        <img src={"/images/dashboard-LTC.svg"}/>
                      </div>
                      <div className="flex flex-1 flex-col">
                        <span className="text-xs text-[#73726C]">Yesterday LTC Rewards</span>
                        <span className="text-sm font-medium text-[#141413]">158.80 USD</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 rounded-xl bg-[#F4F2E7] p-3">
                      <div className="flex size-8 items-center justify-center rounded-lg bg-[#FFF3E0]">
                        <img src={"/images/dashboard-Doge.svg"}/>
                      </div>
                      <div className="flex flex-1 flex-col">
                        <span className="text-xs text-[#73726C]">Yesterday Doge Rewards</span>
                        <span className="text-sm font-medium text-[#141413]">158.80 USD</span>
                      </div>
                    </div>
                  </div>

                  {/* 第二行：Total Rewards */}
                  <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
                    <div className="flex items-center gap-2 rounded-xl bg-[#F4F2E7] p-3">
                      <div className="flex size-8 items-center justify-center rounded-lg bg-[#F3E5F5]">
                        <img src={"/images/validator-rewards.svg"}/>
                      </div>
                      <div className="flex flex-1 flex-col">
                        <span className="text-xs text-[#73726C]">Total xxx Rewards</span>
                        <span className="text-sm font-medium text-[#141413]">300.08</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 rounded-xl bg-[#F4F2E7] p-3">
                      <div className="flex size-8 items-center justify-center rounded-lg bg-[#E3F2FD]">
                        <img src={"/images/validator-LTC.svg"}/>
                      </div>
                      <div className="flex flex-1 flex-col">
                        <span className="text-xs text-[#73726C]">Total LTC Rewards</span>
                        <span className="text-sm font-medium text-[#141413]">200.00</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 rounded-xl bg-[#F4F2E7] p-3">
                      <div className="flex size-8 items-center justify-center rounded-lg bg-[#FFF3E0]">
                        <img src={"/images/dashboard-Doge.svg"}/>
                      </div>
                      <div className="flex flex-1 flex-col">
                        <span className="text-xs text-[#73726C]">Total Doge Rewards</span>
                        <span className="text-sm font-medium text-[#141413]">100.00</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 rounded-xl bg-[#F4F2E7] p-3">
                      <div className="flex size-8 items-center justify-center rounded-lg bg-[#E8E6DC]">
                        <img src={"/images/dashboard-Price.svg"}/>
                      </div>
                      <div className="flex flex-1 flex-col">
                        <span className="text-xs text-[#73726C]">Total Rewards</span>
                        <span className="text-sm font-medium text-[#141413]">158.80 USD</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Rewards Chart */}
                <div className="flex flex-col gap-4 rounded-2xl bg-[#FAF9F5] p-4">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-medium text-[#141413]">Rewards Chart</h2>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className="size-3 rounded-full bg-[#7B1FA2]" />
                        <span className="text-xs text-[#73726C]">xxx</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="size-3 rounded-full bg-[#1976D2]" />
                        <span className="text-xs text-[#73726C]">LTC</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="size-3 rounded-full bg-[#F57C00]" />
                        <span className="text-xs text-[#73726C]">Doge</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex h-[300px] w-full flex-col rounded-xl bg-[#F7F5EE] p-4">
                    <div className="relative flex h-full flex-col">
                      {/* Chart Y-axis */}
                      <div className="absolute left-3 top-2.5 z-10">
                        <span className="text-xs text-[#CC785C]">Rewards</span>
                      </div>
                      
                      <div className="flex size-full items-start gap-1 pt-8">
                        {/* Y-axis Labels */}
                        <div className="flex h-full w-[60px] flex-col justify-end gap-6">
                          <span className="text-xs text-[#3D3D3A]">1.00</span>
                          <span className="text-xs text-[#3D3D3A]">800.00</span>
                          <span className="text-xs text-[#3D3D3A]">600.00</span>
                          <span className="text-xs text-[#3D3D3A]">400.00</span>
                          <span className="text-xs text-[#3D3D3A]">200.00</span>
                          <span className="text-xs text-[#3D3D3A]">0.00</span>
                        </div>
                        
                        {/* Chart Area */}
                        <div className="flex h-full flex-1 flex-col">
                          <div className="relative flex-1">
                            {/* Grid lines */}
                            <div className="absolute inset-0 flex flex-col justify-between">
                              {Array.from({length: 6}).map((_, i) => (
                                <div key={i} className="border-t border-dashed border-[rgba(31,30,29,0.15)]" />
                              ))}
                            </div>
                            
                            {/* Multi-line chart */}
                            <div className="absolute inset-0">
                              <svg width="100%" height="100%" viewBox="0 0 1000 200" preserveAspectRatio="none">
                                {/* xxx line */}
                                <path d="M0,150 L100,120 L200,130 L300,110 L400,140 L500,120 L600,160 L700,140 L800,170 L900,150 L1000,180" 
                                      stroke="#7B1FA2" strokeWidth="2" fill="none" vectorEffect="non-scaling-stroke"/>
                                {/* LTC line */}
                                <path d="M0,180 L100,160 L200,170 L300,140 L400,160 L500,150 L600,180 L700,160 L800,190 L900,170 L1000,200" 
                                      stroke="#1976D2" strokeWidth="2" fill="none" vectorEffect="non-scaling-stroke"/>
                                {/* DOGE line */}
                                <path d="M0,120 L100,100 L200,110 L300,80 L400,120 L500,100 L600,140 L700,120 L800,150 L900,130 L1000,160" 
                                      stroke="#F57C00" strokeWidth="2" fill="none" vectorEffect="non-scaling-stroke"/>
                              </svg>
                            </div>
                          </div>
                          
                          {/* X-axis Labels */}
                          <div className="flex justify-between pt-4 text-xs text-[#73726C]">
                            <span>6/24 10:00</span>
                            <span>6/24 13:00</span>
                            <span>6/24 16:00</span>
                            <span>6/24 19:00</span>
                            <span>6/24 22:00</span>
                            <span>6/25 1:00</span>
                            <span>6/25 4:00</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* History Table */}
                <div className="flex w-full flex-col overflow-hidden rounded-2xl bg-[#FAF9F5]">
                  <div className="flex flex-col gap-4 p-4 md:flex-row md:items-center md:justify-between">
                    <h2 className="text-lg font-medium text-[#141413]">History</h2>
                    <div className="flex flex-col gap-2 md:flex-row md:items-center md:gap-2">
                      <select 
                        className="rounded-lg border border-[rgba(31,30,29,0.15)] bg-white px-3 py-2 text-sm"
                        style={{ 
                          fontFamily: 'Styrene B',
                          fontSize: '14px'
                        }}
                      >
                        <option>All</option>
                        <option>xxx</option>
                        <option>LTC</option>
                        <option>DOGE</option>
                      </select>
                                              <div className="flex gap-2">
                          <div className="custom-datepicker flex-1 md:flex-none">
                            <DatePicker
                              selected={startDate}
                              onChange={(date: Date | null) => setStartDate(date)}
                              selectsStart
                              startDate={startDate}
                              endDate={endDate}
                              maxDate={endDate || undefined}
                              placeholderText="Select start date"
                              dateFormat="yyyy/MM/dd"
                              className="w-full min-w-[150px]"
                              wrapperClassName="w-full"
                              popperClassName="custom-datepicker"
                              calendarClassName="custom-datepicker"
                            />
                          </div>
                          <div className="custom-datepicker flex-1 md:flex-none">
                            <DatePicker
                              selected={endDate}
                              onChange={(date: Date | null) => setEndDate(date)}
                              selectsEnd
                              startDate={startDate}
                              endDate={endDate}
                              minDate={startDate || undefined}
                              placeholderText="Select end date"
                              dateFormat="yyyy/MM/dd"
                              className="w-full min-w-[150px]"
                              wrapperClassName="w-full"
                              popperClassName="custom-datepicker"
                              calendarClassName="custom-datepicker"
                            />
                          </div>
                        </div>
                    </div>
                  </div>

                  {/* Desktop Table */}
                  <div className="hidden md:block">
                    <table className="w-full">
                      <thead style={{ backgroundColor: '#E8E6DC' }}>
                        <tr style={{ height: '42px' }}>
                          <th className="px-4 py-3 text-left">
                            <span className="text-xs font-medium text-[#3D3D3A]">Time</span>
                          </th>
                          <th className="px-4 py-3 text-left">
                            <span className="text-xs font-medium text-[#3D3D3A]">Avg. Hashrate</span>
                          </th>
                          <th className="px-4 py-3 text-left">
                            <span className="text-xs font-medium text-[#3D3D3A]">Earning</span>
                          </th>
                          <th className="px-4 py-3 text-left">
                            <span className="text-xs font-medium text-[#3D3D3A]">Wallet Address</span>
                          </th>
                          <th className="px-4 py-3 text-left">
                            <span className="text-xs font-medium text-[#3D3D3A]">Hash</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {Array.from({length: 4}).map((_, index) => (
                          <tr key={index} className="border-b border-[#E8E6DC]">
                            <td className="px-4 py-3">
                              <span className="text-sm text-[#141413]">11:11 2025/6/6</span>
                            </td>
                            <td className="px-4 py-3">
                              <span className="text-sm text-[#141413]">167.4PH/s</span>
                            </td>
                            <td className="px-4 py-3">
                              <span className="text-sm text-[#141413]">200xxx</span>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-[#141413]">0x4c6924...11a0fa</span>
                                <button className="flex size-5 items-center justify-center">
                                  <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                                    <path d="M5.5 3.5H9.5V1.5H5.5V3.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                    <path d="M2.5 4.5H10.5V12.5H2.5V4.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                  </svg>
                                </button>
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-[#141413]">
                                  {index === 0 ? '0x7a8b9c...4d5e6f' : 
                                   index === 1 ? '0x1f2e3d...9a8b7c' : 
                                   index === 2 ? '0x5g6h7i...2k3l4m' : 
                                   '0x9n8o7p...6q5r4s'}
                                </span>
                                <button 
                                  className="flex size-5 items-center justify-center transition-opacity hover:opacity-75"
                                  title="View transaction"
                                >
                                  <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                                    <path d="M6.5 1L11.5 6.5L6.5 12M1.5 6.5H11" stroke="#73726C" strokeWidth="1.2" fill="none"/>
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Mobile Table */}
                  <div className="md:hidden">
                    {Array.from({length: 4}).map((_, index) => (
                      <div key={index} className="border-b border-[#E8E6DC] p-4">
                        <div className="flex flex-col gap-2">
                          <div className="flex justify-between">
                            <span className="text-xs text-[#73726C]">Time:</span>
                            <span className="text-sm text-[#141413]">11:11 2025/6/6</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs text-[#73726C]">Hashrate:</span>
                            <span className="text-sm text-[#141413]">167.4PH/s</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs text-[#73726C]">Earning:</span>
                            <span className="text-sm text-[#141413]">200xxx</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-[#73726C]">Wallet:</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-[#141413]">0x4c6924...11a0fa</span>
                              <button className="flex size-5 items-center justify-center">
                                <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                                  <path d="M5.5 3.5H9.5V1.5H5.5V3.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                  <path d="M2.5 4.5H10.5V12.5H2.5V4.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                </svg>
                              </button>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-[#73726C]">Hash:</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-[#141413]">
                                {index === 0 ? '0x7a8b9c...4d5e6f' : 
                                 index === 1 ? '0x1f2e3d...9a8b7c' : 
                                 index === 2 ? '0x5g6h7i...2k3l4m' : 
                                 '0x9n8o7p...6q5r4s'}
                              </span>
                              <button 
                                className="flex size-5 items-center justify-center transition-opacity hover:opacity-75"
                                title="View transaction"
                              >
                                <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                                  <path d="M6.5 1L11.5 6.5L6.5 12M1.5 6.5H11" stroke="#73726C" strokeWidth="1.2" fill="none"/>
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Wallet 标签页内容 */}
            {activeTab === 'wallet' && (
              <div className="flex w-full flex-col gap-6">
                {/* LTC Wallet */}
                <div className="rounded-2xl bg-[#F7F5EE] p-4 md:p-5">
                  <div className="flex flex-col gap-6 md:flex-row md:items-end">
                    {/* 左侧：图标和标签 */}
                    <div className="flex items-center gap-2">
                      <div className="flex size-11 items-center justify-center rounded-lg bg-[#C9D3DA]">
                        <div className="size-6 overflow-hidden rounded-full">
                          <img 
                            src="/images/dashboard-LTC.svg" 
                            alt="Litecoin"
                            className="size-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-base font-medium text-[#141413]" style={{ fontFamily: 'Styrene B' }}>
                          LTC
                        </span>
                      </div>
                    </div>
                    
                    {/* 中间：输入框区域 */}
                    <div className="flex flex-1 flex-col gap-3 md:flex-row md:gap-3">
                      <div className="flex flex-1 flex-col gap-1">
                        <label className="text-sm text-[#3D3D3A]" style={{ fontFamily: 'Styrene B' }}>
                          Wallet Name
                        </label>
                        <input
                          type="text"
                          placeholder="eg: Wallet"
                          value={ltcWallet.name}
                          onChange={(e) => setLtcWallet({ ...ltcWallet, name: e.target.value })}
                          className="rounded-lg border border-[rgba(31,30,29,0.15)] bg-white px-4 py-2.5 text-sm text-[#141413] placeholder-[#B0AEA5] outline-none transition-colors focus:border-[rgba(31,30,29,0.28)]"
                          style={{ fontFamily: 'Styrene B' }}
                        />
                      </div>
                      <div className="flex flex-1 flex-col gap-1">
                        <label className="text-sm text-[#3D3D3A]" style={{ fontFamily: 'Styrene B' }}>
                          Address
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="eg:0xdjk2...93kaqp"
                            value={ltcWallet.address}
                            onChange={(e) => setLtcWallet({ ...ltcWallet, address: e.target.value })}
                            className="w-full rounded-lg border border-[rgba(31,30,29,0.15)] bg-white px-4 py-2.5 pr-10 text-sm text-[#141413] placeholder-[#B0AEA5] outline-none transition-colors focus:border-[rgba(31,30,29,0.28)]"
                            style={{ fontFamily: 'Styrene B' }}
                          />
                          {ltcWallet.address && (
                            <button
                              type="button"
                              onClick={() => setLtcWallet({ ...ltcWallet, address: '' })}
                              className="absolute right-3 top-1/2 flex size-4 -translate-y-1/2 items-center justify-center text-[#B0AEA5] transition-colors hover:text-[#73726C]"
                            >
                              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                                <path
                                  d="M9 3L3 9M3 3l6 6"
                                  stroke="currentColor"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* 右侧：Add 按钮 */}
                    <div className="flex justify-end md:justify-start">
                      <button 
                        onClick={handleAddLtcWallet}
                        disabled={!isLtcWalletValid}
                        className={`flex h-10 w-[88px] items-center justify-center rounded-lg text-sm font-medium transition-colors ${
                          isLtcWalletValid 
                            ? 'cursor-pointer bg-[#141413] text-white hover:bg-[#2D2D2B]' 
                            : 'cursor-not-allowed bg-[#AEADA8] text-white'
                        }`}
                        style={{ fontFamily: 'Styrene B' }}
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </div>

                {/* Doge Wallet */}
                <div className="rounded-2xl bg-[#F7F5EE] p-4 md:p-5">
                  <div className="flex flex-col gap-6 md:flex-row md:items-end">
                    {/* 左侧：图标和标签 */}
                    <div className="flex items-center gap-2">
                      <div className="flex size-11 items-center justify-center rounded-lg bg-[rgba(255,255,255,0.5)]">
                        <div className="size-6 overflow-hidden rounded-full">
                          <img 
                            src="/images/dashboard-Doge.svg" 
                            alt="Dogecoin"
                            className="size-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-base font-medium text-[#141413]" style={{ fontFamily: 'Styrene B' }}>
                          Doge
                        </span>
                      </div>
                    </div>
                    
                    {/* 中间：输入框区域 */}
                    <div className="flex flex-1 flex-col gap-3 md:flex-row md:gap-3">
                      <div className="flex flex-1 flex-col gap-1">
                        <label className="text-sm text-[#3D3D3A]" style={{ fontFamily: 'Styrene B' }}>
                          Wallet Name
                        </label>
                        <input
                          type="text"
                          placeholder="eg: Wallet"
                          value={dogeWallet.name}
                          onChange={(e) => setDogeWallet({ ...dogeWallet, name: e.target.value })}
                          className="rounded-lg border border-[rgba(31,30,29,0.15)] bg-white px-4 py-2.5 text-sm text-[#141413] placeholder-[#B0AEA5] outline-none transition-colors focus:border-[rgba(31,30,29,0.28)]"
                          style={{ fontFamily: 'Styrene B' }}
                        />
                      </div>
                      <div className="flex flex-1 flex-col gap-1">
                        <label className="text-sm text-[#3D3D3A]" style={{ fontFamily: 'Styrene B' }}>
                          Address
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="eg:0xdjk2...93kaqp"
                            value={dogeWallet.address}
                            onChange={(e) => setDogeWallet({ ...dogeWallet, address: e.target.value })}
                            className="w-full rounded-lg border border-[rgba(31,30,29,0.15)] bg-white px-4 py-2.5 pr-10 text-sm text-[#141413] placeholder-[#B0AEA5] outline-none transition-colors focus:border-[rgba(31,30,29,0.28)]"
                            style={{ fontFamily: 'Styrene B' }}
                          />
                          {dogeWallet.address && (
                            <button
                              type="button"
                              onClick={() => setDogeWallet({ ...dogeWallet, address: '' })}
                              className="absolute right-3 top-1/2 flex size-4 -translate-y-1/2 items-center justify-center text-[#B0AEA5] transition-colors hover:text-[#73726C]"
                            >
                              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                                <path
                                  d="M9 3L3 9M3 3l6 6"
                                  stroke="currentColor"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* 右侧：Add 按钮 */}
                    <div className="flex justify-end md:justify-start">
                      <button 
                        onClick={handleAddDogeWallet}
                        disabled={!isDogeWalletValid}
                        className={`flex h-10 w-[88px] items-center justify-center rounded-lg text-sm font-medium transition-colors ${
                          isDogeWalletValid 
                            ? 'cursor-pointer bg-[#141413] text-white hover:bg-[#2D2D2B]' 
                            : 'cursor-not-allowed bg-[#AEADA8] text-white'
                        }`}
                        style={{ fontFamily: 'Styrene B' }}
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
    </>
  )
}
