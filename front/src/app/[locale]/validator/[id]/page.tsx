'use client'

import { <PERSON>Left, Check, ChevronDown, X } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'

// 模拟数据
const validatorData = {
  id: '5EewuHxa...',
  status: 'Active',
  node: 'Node1',
  stats: {
    hashrate: {
      '5m': '320.05 Ph/s',
      '1h': '320.05 Ph/s', 
      '24h': '320.05 Ph/s',
      max: '420.15 Ph/s'
    },
    shares: {
      '5m': '66.78B',
      '1h': '66.78B',
      '24h': '66.78B', 
      total: '120.34B'
    },
    score: '98',
    staked: '158.80 TAO'
  }
}

// 节点数据
const nodeOptions = [
  { id: 'node1', name: 'Node1', url: 'http://80bliu.axshare.com/?id=29r9uPi_en&g=1' },
  { id: 'node2', name: 'Node2', url: 'http://80bliu.axshare.com/?id=29r9uPi_en&g=1' },
  { id: 'node3', name: 'Node3', url: 'http://80bliu.axshare.com/?id=29r9uPi_en&g=1' },
  { id: 'node4', name: 'Node4', url: 'http://80bliu.axshare.com/?id=29r9uPi_en&g=1' }
]

// 图表数据
const hashrateData = [
  { time: '6/24 10:00', value: 750 },
  { time: '6/24 13:00', value: 800 },
  { time: '6/24 16:00', value: 320 },
  { time: '6/24 19:00', value: 670 },
  { time: '6/24 22:00', value: 580 },
  { time: '6/25 1:00', value: 450 },
  { time: '6/25 4:00', value: 620 },
  { time: '6/25 4:00', value: 340 }
]

const sharesData = [
  { time: '6/24 17:00', value: 150 },
  { time: '6/24 20:00', value: 240 },
  { time: '6/24 23:00', value: 180 },
  { time: '6/25 02:00', value: 820 },
  { time: '6/24 05:00', value: 600 },
  { time: '6/25 08:00', value: 120 },
  { time: '6/25 11:00', value: 80 },
  { time: '6/25 14:00', value: 40 }
]

export default function ValidatorDetailPage({ params }: { params: Promise<{ id: string, locale: string }> }) {
  const [hashrateTimeRange, setHashrateTimeRange] = useState('24h')
  const [validatorId, setValidatorId] = useState<string>('')
  const [locale, setLocale] = useState<string>('')
  const [showChangeNodeModal, setShowChangeNodeModal] = useState(false)
  const [selectedNode, setSelectedNode] = useState('node4')
  const [currentNode, setCurrentNode] = useState('node1')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  useEffect(() => {
    params.then(({ id, locale }) => {
      setValidatorId(id)
      setLocale(locale)
    })
  }, [params])

  if (!validatorId || !locale) {
    return (
      <div className="w-full">
        <div className="flex h-96 items-center justify-center rounded-2xl bg-[#FAF9F5] p-6">
          <div className="text-[#73726C]">Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full">
      {/* 主容器 */}
      <div className="flex w-full flex-col gap-8 rounded-2xl bg-[#FAF9F5] p-6">
        
        {/* 返回按钮 */}
        <Link href={`/${locale}/validator`} className="flex w-fit items-center gap-2 text-[#3D3D3A] transition-opacity hover:opacity-75">
          <ArrowLeft className="size-4" strokeWidth={1} />
          <span className="text-sm font-normal">Back</span>
        </Link>

        {/* 标题区域 */}
        <div className="flex flex-col gap-4 px-2 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col gap-3 md:flex-row md:items-center">
            <h1 className="text-lg font-medium text-[#141413]">{validatorId}</h1>
            <div className="w-fit rounded-lg bg-[#E3EDE3] px-2 py-1">
              <span className="text-sm text-[#0B3629]">{validatorData.status}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="flex h-9 items-center justify-center rounded-lg bg-[#F4F2E7] px-4 py-2">
              <span className="text-sm font-normal text-[#73726C]">
                {nodeOptions.find(node => node.id === currentNode)?.name || 'Node1'}
              </span>
            </button>
            <button 
              onClick={() => setShowChangeNodeModal(true)}
              className="flex h-9 items-center justify-center rounded-lg border border-[rgba(31,30,29,0.15)] px-4 py-2 transition-colors hover:bg-gray-50"
            >
              <span className="text-sm font-medium text-[#141413]">Change</span>
            </button>
          </div>
        </div>

        {/* 统计卡片区域 */}
        <div className="flex flex-col gap-3">
          {/* 第一行 - Hashrate */}
          <div className="grid grid-cols-2 gap-2 md:grid-cols-4 md:gap-3">
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">5m Hashrate</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.hashrate['5m']}</span>
              </div>
            </div>
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">1h Hashrate</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.hashrate['1h']}</span>
              </div>
            </div>
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">24h Hashrate</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.hashrate['24h']}</span>
              </div>
            </div>
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">Max Hashrate:</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.hashrate.max}</span>
              </div>
            </div>
          </div>

          {/* 第二行 - Shares, Score, Staked */}
          <div className="grid grid-cols-2 gap-2 md:grid-cols-4 md:gap-3">
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">1h Shares</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.shares['1h']}</span>
              </div>
            </div>
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">24h Shares</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.shares['24h']}</span>
              </div>
            </div>
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">Score</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.score}</span>
              </div>
            </div>
            <div className="flex flex-col gap-1 rounded-lg bg-[#F4F2E7] p-3 md:p-2">
              <div className="flex items-center gap-1">
                <span className="text-sm text-[#73726C]">Total Staked</span>
              </div>
              <div className="flex h-[30px] items-center justify-center">
                <span className="text-base font-medium text-[#141413]">{validatorData.stats.staked}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Hashrate History 图表 */}
        <div className="flex flex-col gap-2">
          <div className="flex flex-col gap-4 px-2 md:flex-row md:items-center md:justify-between">
            <h2 className="text-lg font-medium text-[#141413]">Hashrate History</h2>
            
            {/* 时间范围选择器 */}
            <div className="flex w-fit items-center justify-center gap-2 self-start rounded-3xl bg-[#F7F5EE] p-2 md:self-auto">
              {['24h', '7d', '30d'].map((range) => (
                <button
                  key={range}
                  onClick={() => setHashrateTimeRange(range)}
                  className={`w-16 rounded-3xl px-3 py-2 text-sm font-medium transition-colors ${
                    hashrateTimeRange === range 
                      ? 'bg-[#E8E6DC] text-[#141413]' 
                      : 'bg-transparent text-[#73726C]'
                  }`}
                >
                  {range}
                </button>
              ))}
            </div>
          </div>

          {/* 图表容器 */}
          <div className="flex h-[348px] flex-col gap-6 rounded-xl bg-[#F7F5EE] p-4">
            <div className="flex flex-row items-start gap-1">
              {/* Y 轴标签 */}
              <div className="flex w-fit flex-col items-center gap-2 pb-4">
                <span className="text-xs text-[#CC785C]">Hashrate(ph/s)</span>
                <div className="flex h-full flex-col items-end justify-between gap-6 md:gap-8">
                  <span className="text-xs text-[#3D3D3A]">1.00</span>
                  <span className="text-xs text-[#3D3D3A]">800.00</span>
                  <span className="text-xs text-[#3D3D3A]">600.00</span>
                  <span className="text-xs text-[#3D3D3A]">400.00</span>
                  <span className="text-xs text-[#3D3D3A]">200.00</span>
                  <span className="text-xs text-[#3D3D3A]">0.00</span>
                </div>
              </div>

              {/* 图表区域 */}
              <div className="relative h-[240px] flex-1">
                {/* 网格线 */}
                <div className="absolute inset-0 flex flex-col justify-between">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div key={i} className="border-t border-dashed border-[rgba(31,30,29,0.15)]" />
                  ))}
                </div>
                
                {/* 折线图占位 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-0.5 w-full rounded bg-[#DA7A5B] opacity-75" />
                </div>
              </div>
            </div>

            {/* X 轴时间标签 */}
            <div className="flex justify-between px-2 text-xs text-[#73726C] md:px-4">
              {hashrateData.map((item, index) => (
                <span key={index} className="whitespace-nowrap text-xs">{item.time}</span>
              ))}
            </div>
          </div>
        </div>

        {/* Shares History 图表 */}
        <div className="flex flex-col gap-2">
          <div className="px-2">
            <h2 className="text-lg font-medium text-[#141413]">Shares History</h2>
          </div>

          {/* 图表容器 */}
          <div className="flex h-[348px] flex-col gap-6 rounded-xl bg-[#F7F5EE] p-4">
            <div className="flex flex-row items-start gap-1">
              {/* Y 轴标签 */}
              <div className="flex w-fit flex-col items-center gap-2 pb-4">
                <span className="text-xs text-[#CC785C]">Share(count)</span>
                <div className="flex h-full flex-col items-end justify-between gap-2.5 md:gap-3.5">
                  <span className="text-xs text-[#3D3D3A]">1600.0B</span>
                  <span className="text-xs text-[#3D3D3A]">1400.0B</span>
                  <span className="text-xs text-[#3D3D3A]">1200.0B</span>
                  <span className="text-xs text-[#3D3D3A]">1000.0B</span>
                  <span className="text-xs text-[#3D3D3A]">800.0B</span>
                  <span className="text-xs text-[#3D3D3A]">600.0B</span>
                  <span className="text-xs text-[#3D3D3A]">400.0B</span>
                  <span className="text-xs text-[#3D3D3A]">200.0B</span>
                  <span className="text-xs text-[#3D3D3A]">0.00</span>
                </div>
              </div>

              {/* 图表区域 */}
              <div className="relative h-[240px] flex-1">
                {/* 网格线 */}
                <div className="absolute inset-0 flex flex-col justify-between">
                  {Array.from({ length: 9 }).map((_, i) => (
                    <div key={i} className="border-t border-dashed border-[rgba(31,30,29,0.15)]" />
                  ))}
                </div>
                
                {/* 折线图占位 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-0.5 w-full rounded bg-[#DA7A5B] opacity-75" />
                </div>
              </div>
            </div>

            {/* X 轴时间标签 */}
            <div className="flex justify-between px-2 text-xs text-[#73726C] md:px-4">
              {sharesData.map((item, index) => (
                <span key={index} className="whitespace-nowrap text-xs">{item.time}</span>
              ))}
            </div>
          </div>
        </div>

      </div>

      {/* Change Node Modal */}
      {showChangeNodeModal && (
        <div 
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          onClick={() => setShowChangeNodeModal(false)}
        >
          <div 
            className="mx-4 w-full max-w-md rounded-2xl bg-white p-6"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="mb-6 flex items-center justify-between">
              <h3 className="text-lg font-medium text-[#141413]">Change Node</h3>
              <button 
                onClick={() => setShowChangeNodeModal(false)}
                className="text-[#73726C] transition-colors hover:text-[#141413]"
              >
                <X className="size-5" />
              </button>
            </div>

            {/* Change From */}
            <div className="mb-4">
              <p className="mb-2 text-sm text-[#73726C]">Change from</p>
              <div className="rounded-lg bg-[#F4F2E7] px-3 py-2">
                <span className="text-sm text-[#141413]">
                  {nodeOptions.find(node => node.id === currentNode)?.name}
                </span>
              </div>
            </div>

            {/* Arrow Down */}
            <div className="mb-4 flex justify-center">
              <ChevronDown className="size-4 text-[#73726C]" />
            </div>

            {/* Change To */}
            <div className="mb-6">
              <p className="mb-2 text-sm text-[#73726C]">To</p>
              <div className="relative">
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="flex w-full items-center justify-between rounded-lg bg-[#F4F2E7] px-3 py-2 text-left"
                >
                  <span className="text-sm text-[#141413]">
                    {nodeOptions.find(node => node.id === selectedNode)?.name}
                  </span>
                  <ChevronDown className={`size-4 text-[#73726C] transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                </button>

                {/* Dropdown Options */}
                {isDropdownOpen && (
                  <div className="absolute inset-x-0 top-full z-10 mt-1 overflow-hidden rounded-lg border border-[rgba(31,30,29,0.15)] bg-white shadow-lg">
                    {nodeOptions.map((node) => (
                      <button
                        key={node.id}
                        onClick={() => {
                          setSelectedNode(node.id)
                          setIsDropdownOpen(false)
                        }}
                        className="group flex w-full items-center justify-between px-3 py-2 text-left hover:bg-gray-50"
                      >
                        <div className="flex flex-col">
                          <span className="text-sm font-medium text-[#141413]">{node.name}</span>
                          <span className="truncate text-xs text-[#B0AEA5]">{node.url}</span>
                        </div>
                        {selectedNode === node.id && (
                          <Check className="size-4 flex-shrink-0 text-orange-500" />
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center gap-3">
              <button
                onClick={() => {
                  setShowChangeNodeModal(false)
                  setIsDropdownOpen(false)
                }}
                className="flex-1 rounded-lg border border-[rgba(31,30,29,0.15)] px-4 py-2 text-sm font-medium text-[#141413] transition-colors hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setCurrentNode(selectedNode)
                  setShowChangeNodeModal(false)
                  setIsDropdownOpen(false)
                  // Here you would typically make an API call to change the node
                }}
                className="flex-1 rounded-lg bg-[#141413] px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-[#2D2D2B]"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}