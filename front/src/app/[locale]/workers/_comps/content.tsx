'use client'

// import { Trans } from '@lingui/react/macro'
import type { ColumnDef } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useMemo } from 'react'

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Segmented,
  SegmentedList,
  SegmentedTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui'

const columns: ColumnDef<{
  name: string
  avg: string
  avg1h: string
  avg24h: string
  rejectionRate: string
  lastSubmitTime: string
  status: string
}>[] = [
  {
    header: 'Miner',
    cell: ({ row }) => row.original.name,
  },
  {
    header: '10 Min Avg',
    cell: ({ row }) => `${row.original.avg} EH/S`,
  },
  {
    header: '1 Hour Avg',
    cell: ({ row }) => `${row.original.avg1h} EH/S`,
  },
  {
    header: '24 Hour Avg',
    cell: ({ row }) => `${row.original.avg24h} EH/S`,
  },
  {
    header: 'Rejection Rate',
    cell: ({ row }) => `${row.original.rejectionRate}%`,
  },
  {
    header: 'Last Submit Time',
    cell: ({ row }) => row.original.lastSubmitTime,
  },
  {
    header: 'Status',
    cell: ({ row }) => row.original.status,
  },
  {
    header: 'Actions',
    cell: ({ row }) => {
      return (
        <div className="flex gap-2">
          <Button size="sm" variant="link">
            "Delete"
          </Button>
          <Button size="sm" variant="link">
            "Pause"
          </Button>
        </div>
      )
    },
  },
]

const EarningsTable = () => {
  const { data } = {
    data: [
      {
        name: 'Miner 1',
        avg: '1000',
        avg1h: '1000',
        avg24h: '1000',
        rejectionRate: '1000',
        lastSubmitTime: '1000',
        status: 'Invalid',
      },
      {
        name: 'Miner 2',
        avg: '1000',
        avg1h: '1000',
        avg24h: '1000',
        rejectionRate: '1000',
        lastSubmitTime: '1000',
        status: 'offline',
      },
      {
        name: 'Miner 3',
        avg: '1000',
        avg1h: '1000',
        avg24h: '1000',
        rejectionRate: '1000',
        lastSubmitTime: '1000',
        status: 'active',
      },
    ],
  }

  const table = useReactTable({
    data: useMemo(() => data, [data]),
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          "Earnings Details"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Segmented defaultValue="all" className="mb-4">
          <SegmentedList>
            <SegmentedTrigger value="all" className="min-w-[110px] space-x-1">
              <span>
                "All"
              </span>
              <span className="text-muted-foreground">(0)</span>
            </SegmentedTrigger>
            <SegmentedTrigger
              value="active"
              className="min-w-[110px] space-x-1"
            >
              <span>
                "Active"
              </span>
              <span className="text-muted-foreground">(0)</span>
            </SegmentedTrigger>
            <SegmentedTrigger
              value="offline"
              className="min-w-[110px] space-x-1"
            >
              <span>
                "Offline"
              </span>
              <span className="text-muted-foreground">(0)</span>
            </SegmentedTrigger>
            <SegmentedTrigger
              value="invalid"
              className="min-w-[110px] space-x-1"
            >
              <span>
                "Invalid"
              </span>
              <span className="text-muted-foreground">(0)</span>
            </SegmentedTrigger>
          </SegmentedList>
        </Segmented>
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export const Content = () => {
  return (
    <div className="flex flex-col gap-4 p-6">
      <EarningsTable />
    </div>
  )
}
