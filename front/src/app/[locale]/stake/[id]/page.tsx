'use client'

import { ArrowLeft } from 'lucide-react'
import { useState } from 'react'

export default function StakeDetailPage() {
  const [activeTab, setActiveTab] = useState<'stake' | 'unstake'>('stake')
  const [fromAmount, setFromAmount] = useState('0.00')
  const [toAmount, setToAmount] = useState('0.00')
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)

  return (
    <div >
      {/* 头部导航 */}
      <div className="mb-6 flex items-center gap-6">
        <button className="border-3 flex size-8 items-center justify-center rounded-lg border-[#F4F2E7] bg-white">
          <ArrowLeft className="size-4 text-[#141413]" />
        </button>
        <div className="flex items-center gap-2">
          <h1 className="font-eb-garamond text-2xl font-semibold text-[#141413]">
            Stake
          </h1>
          <span className="font-eb-garamond text-2xl font-semibold text-[#DA7A5B]">
            5dhdskk9a
          </span>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex flex-col gap-4">
        {/* 标签切换 */}


        {/* 输入框区域 */}
        <div className="rounded-2xl bg-[#F4F2E7] p-3 sm:p-4">
          <div className="mb-2 flex justify-center">
            <div className="flex rounded-lg bg-[#F7F5EE] p-1">
              <button
                  onClick={() => setActiveTab('stake')}
                  className={`rounded-md px-3 py-2 font-styrene-b text-sm font-medium transition-colors ${
                      activeTab === 'stake'
                          ? 'bg-[#E8E6DC] text-[#141413]'
                          : 'opacity-32 bg-transparent text-[#73726C]'
                  }`}
              >
                Stake
              </button>
              <button
                  onClick={() => setActiveTab('unstake')}
                  className={`rounded-md px-3 py-2 font-styrene-b text-sm font-medium transition-colors ${
                      activeTab === 'unstake'
                          ? 'bg-[#E8E6DC] text-[#141413]'
                          : 'opacity-32 bg-transparent text-[#73726C]'
                  }`}
              >
                Unstake
              </button>
            </div>
          </div>
          <div className="relative flex flex-col gap-3 sm:gap-4 lg:flex-row">
            {/* 浮动箭头按钮 - 浮在两个输入框上方，一边占一半 */}
            <button
                className="absolute left-1/2 top-1/2 z-20 flex size-8 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-lg border-[3px] border-[#F4F2E7] bg-white p-1.5 shadow-sm transition-shadow hover:shadow-md">
              {/* PC 端显示向右箭头，移动端显示向下箭头 */}
              <img src={"/images/state-left.svg"} className="hidden lg:block"/>
              <img src={"/images/state-bottom.svg"} className="block lg:hidden"/>
            </button>
            {/* From 输入框 - 左侧 */}
            <div className="flex-1 rounded-2xl border border-[rgba(31,30,29,0.15)] bg-white p-3 sm:p-4">
              <div className="space-y-2">
                {/* 头部信息 */}
                <div className="flex flex-wrap items-center justify-between gap-2">
                  <div className="flex items-center gap-2">
                    <span className="font-styrene-b text-sm text-[#B0AEA5]">
                      From Tao Balance
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="font-styrene-b text-xs text-[#B0AEA5] sm:text-sm">
                      Available:15.8420
                    </span>
                    <div className="flex size-4 flex-shrink-0 items-center justify-center">
                      {/* TAO 图标 */}
                      <img src={"/images/state-ltc.svg"}/>
                    </div>
                  </div>
                </div>

                {/* 输入区域 */}
                <div className="flex items-center justify-between gap-2">
                  <div className="flex min-w-0 flex-1 items-center gap-2">
                    <input
                        type="text"
                        value={fromAmount}
                        onChange={(e) => setFromAmount(e.target.value)}
                        className="w-full min-w-0 bg-transparent font-styrene-b text-2xl font-bold text-[#141413] outline-none sm:text-3xl"
                        placeholder="0.00"
                    />
                  </div>
                  <div className="flex flex-shrink-0 items-center gap-2">
                    <div className="flex size-6 items-center justify-center rounded-full bg-[#E8E6DC]">
                      {/* TAO 图标 */}
                      <img src={"/images/state-ltc-icon.svg"}/>
                    </div>
                  </div>
                </div>

                {/* 底部信息 */}
                <div className="flex flex-wrap items-center justify-between gap-2">
                  <span className="font-styrene-b text-sm text-[#B0AEA5] sm:text-base">
                    $4,128.50
                  </span>
                  <div className="flex gap-1 sm:gap-2">
                    <button
                        className="rounded-full bg-[#FAF9F5] px-2 py-1 font-styrene-b text-xs uppercase text-[#141413] transition-colors hover:bg-[#F0EFE5] sm:px-3">
                      half
                    </button>
                    <button
                        className="rounded-full bg-[#FAF9F5] px-2 py-1 font-styrene-b text-xs uppercase text-[#141413] transition-colors hover:bg-[#F0EFE5] sm:px-3">
                      max
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* To 输入框 - 右侧 */}
            <div className="flex-1 rounded-2xl border border-[rgba(31,30,29,0.15)] bg-white p-3 sm:p-4">
              <div className="space-y-2">
                {/* 头部信息 */}
                <div className="flex flex-wrap items-center justify-between gap-2">
                  <div className="flex items-center gap-1">
                    <span className="font-styrene-b text-sm text-[#B0AEA5]">
                      Stake To
                    </span>
                    <span className="font-styrene-b text-sm text-[#3D3D3A]">
                      Dogemine
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="font-styrene-b text-xs text-[#B0AEA5] sm:text-sm">
                      Balance:0
                    </span>
                    <div className="flex size-4 flex-shrink-0 items-center justify-center">
                      {/* Dogemine 图标 */}
                      <img src={"/images/state-doge.svg"}/>
                    </div>
                  </div>
                </div>

                {/* 输入区域 */}
                <div className="flex items-center justify-between gap-2">
                  <div className="flex min-w-0 flex-1 items-center gap-2">
                    <input
                        type="text"
                        value={toAmount}
                        onChange={(e) => setToAmount(e.target.value)}
                        className="w-full min-w-0 bg-transparent font-styrene-b text-2xl font-bold text-[#141413] outline-none sm:text-3xl"
                        placeholder="0.00"
                    />
                  </div>
                  <div className="flex flex-shrink-0 items-center gap-2">
                    <div className="flex size-6 items-center justify-center rounded-full bg-[#1E293B]">
                      {/* Dogemine 图标 */}
                      <img src={"/images/state-doge-btc.svg"}/>
                    </div>
                  </div>
                </div>

                {/* 底部信息 */}
                <div className="flex items-center justify-between">
                  <span className="font-styrene-b text-base text-[#B0AEA5] sm:text-lg">
                    $3,508.75
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
                      <div className="mt-3 flex gap-2 sm:mt-4">
              <button
                  className="font-inter flex-1 rounded-lg border border-[rgba(31,30,29,0.15)] bg-transparent py-2 text-sm font-semibold text-[#141413] transition-colors hover:bg-[#F0EFE5]">
                Reset
              </button>
                          <button
                  onClick={() => setShowConfirmModal(true)}
                  className="font-inter flex-1 rounded-lg bg-[#C96342] py-2 text-sm font-semibold text-white transition-colors hover:bg-[#B55A3B]">
                Confirm
              </button>
          </div>
        </div>
      </div>

      {/* 确认弹窗 */}
      {showConfirmModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative w-full max-w-md rounded-2xl bg-white p-6 shadow-xl">
            {/* 弹窗头部 */}
            <div className="mb-6 flex items-center justify-between">
              <h2 className="font-styrene-b text-xl font-semibold text-[#141413]">
                Confirm
              </h2>
              <button
                onClick={() => setShowConfirmModal(false)}
                className="flex size-8 items-center justify-center rounded-lg transition-colors hover:bg-gray-100"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className="text-[#141413]">
                  <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>

            {/* 主要内容区域 */}
            <div className="relative mb-6">
              {/* 两个数字显示 */}
              <div className="mb-4 flex items-center justify-between">
                <div className="text-center">
                  <div className="mb-2 font-styrene-b text-3xl font-bold text-[#141413]">
                    {fromAmount}
                  </div>
                  <div className="flex items-center justify-center gap-1">
                    <span className="font-styrene-b text-sm text-[#B0AEA5]">From TAO</span>
                    <div className="flex size-4 items-center justify-center">
                      <img src={"/images/state-ltc.svg"} className="size-3"/>
                    </div>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="mb-2 font-styrene-b text-3xl font-bold text-[#141413]">
                    {toAmount}
                  </div>
                  <div className="flex items-center justify-center gap-1">
                    <span className="font-styrene-b text-sm text-[#B0AEA5]">To Dogemine</span>
                    <div className="flex size-4 items-center justify-center">
                      <img src={"/images/state-doge-btc.svg"} className="size-4"/>
                    </div>
                  </div>
                </div>
              </div>

              {/* 浮动箭头按钮 */}
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                <div className="flex size-8 items-center justify-center rounded-lg border-[3px] border-[#F4F2E7] bg-white p-1.5 shadow-sm">
                  <img src={"/images/state-left.svg"} className="size-4"/>
                </div>
              </div>
            </div>

            {/* 底部按钮 */}
            <div className="flex gap-3">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="font-inter flex-1 rounded-lg border border-[rgba(31,30,29,0.15)] bg-white py-3 text-sm font-semibold text-[#141413] transition-colors hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setShowConfirmModal(false)
                  setShowSuccessModal(true)
                }}
                className="font-inter flex-1 rounded-lg bg-[#141413] py-3 text-sm font-semibold text-white transition-colors hover:bg-[#2A2A2A]"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 成功弹窗 */}
      {showSuccessModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative w-full max-w-md rounded-2xl bg-white p-6 shadow-xl">
            {/* 弹窗头部 - 关闭按钮 */}
            <div className="mb-4 flex justify-end">
              <button
                onClick={() => setShowSuccessModal(false)}
                className="flex size-8 items-center justify-center rounded-lg transition-colors hover:bg-gray-100"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className="text-[#141413]">
                  <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>

            {/* 主要内容区域 */}
            <div className="mb-6 text-center">
              {/* 成功图标 */}
              <div className="mb-4 flex justify-center">
                <div className="relative">
                  {/* 主圆形背景 */}
                 <img src={"/images/state-succeed.svg"} />
                </div>
              </div>

              {/* 标题 */}
              <h2 className="mb-4 font-styrene-b text-2xl font-bold text-[#141413]">
                Staked Successful
              </h2>

              {/* 描述文字 */}
              <p className="font-inter mb-6 text-sm leading-relaxed text-[#73726C]">
                Congratulations! Your TAO staking is successful and your exclusive miner credentials are ready. Please safeguard your credentials - they're your exclusive passport to the AI mining world.
              </p>

              {/* 质押金额显示 */}
              <div className="flex items-center justify-between border-t border-[#F4F2E7] py-3">
                <span className="font-styrene-b text-sm text-[#B0AEA5]">
                  Staked Amount
                </span>
                <span className="font-styrene-b text-sm font-semibold text-[#141413]">
                  {fromAmount} TAO
                </span>
              </div>
            </div>

            {/* 底部按钮 */}
            <div className="flex gap-3">
              <button
                onClick={() => setShowSuccessModal(false)}
                className="font-inter flex-1 rounded-lg border border-[rgba(31,30,29,0.15)] bg-white py-3 text-sm font-semibold text-[#141413] transition-colors hover:bg-gray-50"
              >
                Continue Stake
              </button>
              <button
                onClick={() => setShowSuccessModal(false)}
                className="font-inter flex-1 rounded-lg bg-[#141413] py-3 text-sm font-semibold text-white transition-colors hover:bg-[#2A2A2A]"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}