'use client'

import { ChevronDown,ChevronLeft, ChevronRight, Search } from 'lucide-react'
import { useParams,useRouter  } from 'next/navigation'
import { useState } from 'react'

export default function StakePage() {
  const router = useRouter()
  const params = useParams()
  const locale = params.locale as string
  const [activeTab, setActiveTab] = useState<'all' | 'my'>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [rowsPerPage, setRowsPerPage] = useState(12)

  // 模拟节点数据
  const nodes = [
    {
      id: '5dhdskk9a',
      status: 'Active',
      hashrate24h: '320.05 Ph/s',
      hashrate1h: '320.05 Ph/s',
      totalStake: '500 TAO',
      weight: '98',
      emission: '100'
    },
    {
      id: '5dhdskk9a',
      status: 'Active',
      hashrate24h: '320.05 Ph/s',
      hashrate1h: '320.05 Ph/s',
      totalStake: '500 TAO',
      weight: '98',
      emission: '100'
    },
    {
      id: '5dhdskk9a',
      status: 'Active',
      hashrate24h: '320.05 Ph/s',
      hashrate1h: '320.05 Ph/s',
      totalStake: '500 TAO',
      weight: '98',
      emission: '100'
    },
    {
      id: '5dhdskk9a',
      status: 'Active',
      hashrate24h: '320.05 Ph/s',
      hashrate1h: '320.05 Ph/s',
      totalStake: '500 TAO',
      weight: '98',
      emission: '100'
    },
    {
      id: '5dhdskk9a',
      status: 'Active',
      hashrate24h: '320.05 Ph/s',
      hashrate1h: '320.05 Ph/s',
      totalStake: '500 TAO',
      weight: '98',
      emission: '100'
    },
    {
      id: '5dhdskk9a',
      status: 'Active',
      hashrate24h: '320.05 Ph/s',
      hashrate1h: '320.05 Ph/s',
      totalStake: '500 TAO',
      weight: '98',
      emission: '100'
    }
  ]

  const handleStakeClick = (nodeId: string) => {
    router.push(`/${locale}/stake/${nodeId}`)
  }

  return (
    <div>
      {/* 标题区域 */}
      <div className="relative mx-4 mb-6 mt-4 rounded-2xl bg-[#F7F5EE] p-8 text-center md:mx-0 md:mt-0 md:p-14" style={{ marginBottom: '24px', height: '208px' }}>
        {/* 背景装饰元素 */}
        <div className="absolute inset-0 overflow-hidden rounded-2xl">
          <img src={"/images/stake-bg.png"}/>
        </div>
        
        <div className="relative z-10">
          <h1 className="mb-2 font-eb-garamond text-3xl font-semibold text-[#141413] md:text-4xl">
            Title
          </h1>
          <p className="font-styrene-b text-lg text-[#141413] md:text-xl">
            Participate in staking to gain higher weight and more rewards
          </p>
        </div>
      </div>

      {/* 标签切换和搜索区域 */}
      <div className="mb-6 flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between">
        {/* 标签切换 */}
        <div className="flex">
          <div className="flex rounded-3xl bg-[#F7F5EE] p-1">
            <button
              onClick={() => setActiveTab('all')}
              className={`rounded-3xl px-4 py-2 font-styrene-b text-sm font-medium transition-colors ${
                activeTab === 'all'
                  ? 'bg-[#E8E6DC] text-[#141413]'
                  : 'bg-transparent text-[#73726C]'
              }`}
            >
              All Node
            </button>
            <button
              onClick={() => setActiveTab('my')}
              className={`rounded-3xl px-4 py-2 font-styrene-b text-sm font-medium transition-colors ${
                activeTab === 'my'
                  ? 'bg-[#E8E6DC] text-[#141413]'
                  : 'bg-transparent text-[#73726C]'
              }`}
            >
              My Node
            </button>
          </div>
        </div>

        {/* 搜索框 */}
        <div className="flex">
          <div className="relative w-full max-w-md">
            <div className="flex items-center rounded-lg bg-[#F7F5EE] px-4 py-2">
              <Search className="mr-2 size-4 text-[#141413]" />
              <input
                type="text"
                placeholder="Search miner..."
                className="flex-1 bg-transparent font-styrene-b text-sm text-[#141413] placeholder-[#B0AEA5] focus:outline-none"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 节点列表 */}
      <div className="space-y-4">
        {nodes.map((node, index) => (
          <div
            key={index}
            className="rounded-2xl border border-[rgba(31,30,29,0.15)] bg-white p-6"
          >
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
              {/* 节点信息 */}
              <div className="flex items-center gap-2">
                <span className="font-styrene-b text-lg font-medium text-[#141413]">
                  {node.id}
                </span>
                <span className="rounded-lg bg-[#E3EDE3] px-2 py-1 font-styrene-b text-sm text-[#0B3629]">
                  {node.status}
                </span>
              </div>

              {/* 数据字段 */}
              <div className="flex flex-col gap-3 lg:flex-row lg:flex-wrap lg:flex-nowrap lg:gap-6">
                <div className="flex items-center justify-between lg:flex-col lg:items-start lg:gap-1">
                  <span className="font-styrene-b text-sm text-[#73726C]">24h Hashrate</span>
                  <span className="font-styrene-b text-sm font-medium text-[#141413]">
                    {node.hashrate24h}
                  </span>
                </div>
                <div className="flex items-center justify-between lg:flex-col lg:items-start lg:gap-1">
                  <span className="font-styrene-b text-sm text-[#73726C]">1h Hashrate</span>
                  <span className="font-styrene-b text-sm font-medium text-[#141413]">
                    {node.hashrate1h}
                  </span>
                </div>
                <div className="flex items-center justify-between lg:flex-col lg:items-start lg:gap-1">
                  <span className="font-styrene-b text-sm text-[#73726C]">Total Stake</span>
                  <span className="font-styrene-b text-sm font-medium text-[#141413]">
                    {node.totalStake}
                  </span>
                </div>
                <div className="flex items-center justify-between lg:flex-col lg:items-start lg:gap-1">
                  <span className="font-styrene-b text-sm text-[#73726C]">Weight</span>
                  <span className="font-styrene-b text-sm font-medium text-[#141413]">
                    {node.weight}
                  </span>
                </div>
                <div className="flex items-center justify-between lg:flex-col lg:items-start lg:gap-1">
                  <span className="font-styrene-b text-sm text-[#73726C]">xxx Emission</span>
                  <span className="font-styrene-b text-sm font-medium text-[#141413]">
                    {node.emission}
                  </span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end">
                <button
                  onClick={() => handleStakeClick(node.id)}
                  className="w-full rounded-lg bg-[#C96342] px-4 py-2 font-styrene-b text-sm font-medium text-white transition-all duration-300 ease-out hover:scale-105 hover:bg-[#B55A3A] lg:h-10 lg:w-auto"
                >
                  Stake
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 分页组件 */}
      <div className="mt-6 flex flex-col items-center justify-between gap-4 rounded-lg bg-white p-3 lg:flex-row">
        {/* 显示信息 */}
        <div className="font-styrene-b text-xs text-[#73726C]">
          Showing 1 - 10 out of 30
        </div>

        {/* 分页按钮 */}
        <div className="flex items-center gap-1">
          <button className="flex size-8 items-center justify-center rounded-lg text-[#73726C] hover:bg-gray-100">
            <ChevronLeft className="size-4" />
          </button>
          
          <button className="flex size-8 items-center justify-center rounded-lg bg-[#E8E6DC] font-styrene-b text-base text-[#0D1421]">
            1
          </button>
          <button className="flex size-8 items-center justify-center rounded-lg font-styrene-b text-base text-[#0D1421] hover:bg-gray-100">
            2
          </button>
          <button className="flex size-8 items-center justify-center rounded-lg font-styrene-b text-base text-[#0D1421] hover:bg-gray-100">
            3
          </button>
          <button className="flex size-8 items-center justify-center rounded-lg font-styrene-b text-base text-[#0D1421] hover:bg-gray-100">
            4
          </button>
          <button className="flex size-8 items-center justify-center rounded-lg font-styrene-b text-base text-[#0D1421] hover:bg-gray-100">
            5
          </button>
          
          <span className="flex size-8 items-center justify-center font-styrene-b text-xs text-[#0D1421]">
            •••
          </span>
          
          <button className="flex size-8 items-center justify-center rounded-lg font-styrene-b text-base text-[#0D1421] hover:bg-gray-100">
            12
          </button>
          
          <button className="flex size-8 items-center justify-center rounded-lg text-[#73726C] hover:bg-gray-100">
            <ChevronRight className="size-4" />
          </button>
        </div>

        {/* 每页显示行数 */}
        <div className="flex items-center gap-3">
          <span className="font-styrene-b text-xs text-[#73726C]">Show rows</span>
          <div className="flex items-center gap-2 rounded-lg bg-[#E8E6DC] px-2 py-1">
            <span className="font-styrene-b text-xs text-[#141413]">12</span>
            <ChevronDown className="size-4 text-[#141413]" />
          </div>
        </div>
      </div>
    </div>
  )
} 