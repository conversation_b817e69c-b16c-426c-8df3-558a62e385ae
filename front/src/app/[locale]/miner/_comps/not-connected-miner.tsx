'use client'

export function NotConnectedMiner() {
  const steps = [
    {
      id: 1,
      title: 'Create a Bittensor wallet',
      bgColor: '#F4F2E7',
      stepBgColor: '#DA7A5B',
      textColor: '#DA7A5B',
      stepTextColor: '#FFFFFF',
      icon: 'bittensor'
    },
    {
      id: 2,
      title: 'Install dependencies and projects',
      bgColor: '#F8ECEC',
      stepBgColor: 'rgba(138, 36, 36, 0.6)',
      textColor: '#8A2424',
      stepTextColor: '#FFFFFF',
      icon: 'miner'
    },
    {
      id: 3,
      title: 'Configure Redis',
      bgColor: '#E5E4E7',
      stepBgColor: '#9B87F5',
      textColor: '#38365D',
      stepTextColor: '#FFFFFF',
      icon: 'redis'
    },
    {
      id: 4,
      title: 'Configure miner parameters',
      bgColor: '#D3E5F8',
      stepBgColor: '#6A98BC',
      textColor: '#1B67B2',
      stepTextColor: '#FFFFFF',
      icon: 'config'
    },
    {
      id: 5,
      title: 'Start the miner node',
      bgColor: '#E3EDE3',
      stepBgColor: '#637F76',
      textColor: '#637F76',
      stepTextColor: '#FFFFFF',
      icon: 'node'
    },
    {
      id: 6,
      title: 'Connect the physical mining machine to the proxy',
      bgColor: '#F1E6D0',
      stepBgColor: 'rgba(255, 255, 255, 0.5)',
      textColor: '#CC785C',
      stepTextColor: '#141413',
      icon: 'connect'
    }
  ]

  return (
    <div 
      className="flex min-h-screen w-full flex-col items-center p-4 md:px-6 md:py-8"
      style={{ backgroundColor: '#FAF9F5' }}
    >
      {/* 主容器 */}
      <div className="flex w-full max-w-[588px] flex-col items-center gap-6 md:gap-8">
        
        {/* 主标题 */}
        <div className="flex flex-col items-center gap-4 pb-2 pt-8 md:gap-7 md:pb-3 md:pt-16">
          <h1 
            className="text-center text-2xl md:text-3xl"
            style={{
              fontFamily: 'EB Garamond',
              fontWeight: 700,
              lineHeight: '1.375',
              color: '#141413'
            }}
          >
            Be a Worker
          </h1>
        </div>

        {/* 步骤列表 */}
        <div className="flex w-full max-w-[540px] flex-col gap-3 md:gap-4">
          <div className="flex flex-col gap-2 rounded-xl bg-white p-2">
            {steps.map((step, index) => (
              <div 
                key={step.id}
                className="flex items-center gap-3 overflow-hidden rounded-xl p-0"
                style={{ backgroundColor: step.bgColor }}
              >
                {/* 图标区域 */}
                <div 
                  className="flex size-20 flex-shrink-0 items-center justify-center md:h-[102px] md:w-[102px]"
                  style={{ 
                    backgroundColor: '#E3DFCE',
                    borderRadius: '12px 0px 0px 12px'
                  }}
                >
                  {/* 根据不同步骤显示不同图标 */}
                  <div className="flex items-center justify-center">
                    {step.icon === 'bittensor' && (
                      <div className="flex flex-col items-center justify-center">
                       <img src={"/images/Miner-Step1.svg"}/>
                      </div>
                    )}
                    {step.icon === 'miner' && (
                        <div className="flex flex-col items-center justify-center">
                          <img src={"/images/Miner-Step2.svg"}/>
                        </div>
                    )}
                    {step.icon === 'redis' && (
                        <div className="flex flex-col items-center justify-center">
                          <img src={"/images/Miner-Step3.svg"}/>
                        </div>
                    )}
                    {step.icon === 'config' && (
                        <div className="flex flex-col items-center justify-center">
                          <img src={"/images/Miner-Step4.svg"}/>
                        </div>
                    )}
                    {step.icon === 'node' && (
                        <div className="flex flex-col items-center justify-center">
                          <img src={"/images/Miner-Step5.svg"}/>
                        </div>
                    )}
                    {step.icon === 'connect' && (
                        <div className="flex flex-col items-center justify-center">
                          <img src={"/images/Miner-Step6.svg"}/>
                        </div>
                    )}
                  </div>
                </div>

                {/* 内容区域 */}
                <div className="flex flex-1 flex-col justify-center gap-1.5 p-2 md:gap-2 md:p-3">
                  {/* 步骤标签 */}
                  <div className="flex items-center">
                    <div 
                      className="rounded-lg px-1.5 py-0 md:px-2"
                      style={{ backgroundColor: step.stepBgColor }}
                    >
                      <span 
                        className="text-xs font-normal leading-6 tracking-wide md:text-sm md:leading-7"
                        style={{ 
                          fontFamily: 'Styrene B',
                          color: step.stepTextColor,
                          letterSpacing: '2.5%'
                        }}
                      >
                        Step{step.id}
                      </span>
                    </div>
                  </div>
                  
                  {/* 步骤描述 */}
                  <div className="flex flex-col">
                    <span 
                      className="text-sm font-normal leading-5 md:text-base md:leading-6"
                      style={{ 
                        fontFamily: 'Styrene B',
                        color: step.textColor
                      }}
                    >
                      {step.title}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Learn More 按钮 */}

          <button
            className="flex w-full items-center justify-center gap-1 rounded-lg border px-3 py-2.5 transition-colors hover:opacity-90 md:w-auto md:px-4 md:py-3"
            style={{
              backgroundColor: 'transparent',
              borderColor: 'rgba(31, 30, 29, 0.15)',
              borderWidth: '1px',
              maxWidth: '524px'
            }}
          >
            <span 
              className="text-sm leading-5 md:text-base md:leading-6"
              style={{
                fontFamily: 'Styrene B',
                fontWeight: 500,
                color: '#141413'
              }}
            >
              Learn More
            </span>
          </button>


      </div>
    </div>
  )
}