'use client'

import {RotateCw, Search } from 'lucide-react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useState } from 'react'

import { useWalletConnection } from '@/lib/hooks/use-wallet-connection'

import { NotConnectedMiner } from './_comps/not-connected-miner'

export default function MinerPage() {
  const params = useParams()
  const locale = params.locale as string
  const [activeTab, setActiveTab] = useState<'worker' | 'earning'>('worker')
  const [lastUpdated] = useState('15:00')
  const [toggleState, setToggleState] = useState(false)
  const { isConnected } = useWalletConnection()

  // 如果钱包未连接，显示未连接状态页面
  if (!isConnected) {
    return <NotConnectedMiner />
  }
  
  const handleTabChange = (tab: 'worker' | 'earning') => {
    setActiveTab(tab)
  }

  // 处理 hash 点击，跳转到 Bittensor 区块链浏览器
  const handleHashClick = (hash: string) => {
    // 使用 Bittensor 网络的区块链浏览器
    // 可以根据实际需要选择不同的浏览器
    let explorerUrl = ''
    
    // 尝试 taostats.io (主要的 Bittensor 浏览器)
    if (hash.startsWith('0x')) {
      // 如果是交易 hash，可能需要调整 URL 格式
      explorerUrl = `https://taostats.io/extrinsics?search=${hash}`
    } else {
      // 如果是其他格式的 hash
      explorerUrl = `https://taostats.io/extrinsics?search=${hash}`
    }
    
    // 备选：使用 bittensor.com 的浏览器
    // explorerUrl = `https://bittensor.com/scan/search?query=${hash}`
    
    window.open(explorerUrl, '_blank')
  }

  // 模拟矿工数据
  const miners = [
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      node: 'node1',
      stake: '500 TAO',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      node: 'node1',
      stake: '500 TAO',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      node: 'node1',
      stake: '500 TAO',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      node: 'node1',
      stake: '500 TAO',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      node: 'node1',
      stake: '500 TAO',
      score: '98'
    },
    {
      id: '5EewuHxa...',
      status: 'Active',
      hashrate1h: '320.05 Ph/s',
      hashrate24h: '320.05 Ph/s',
      shares1h: '66.78B',
      shares24h: '66.78B',
      node: 'node1',
      stake: '500 TAO',
      score: '98'
    }
  ]

  // 模拟收益数据
  const earningsData = [
    {
      time: '11:11 2025/6/6',
      worker: '5EewuHxa',
      earning: '200xxx',
      walletAddress: '0x4c6924...11a0fa',
      hash: '0x4c6924...11a0fa'
    },
    {
      time: '11:11 2025/6/6',
      worker: '5EewuHxa',
      earning: '200xxx',
      walletAddress: '0x4c6924...11a0fa',
      hash: '0x4c6924...11a0fa'
    },
    {
      time: '11:11 2025/6/6',
      worker: '5EewuHxa',
      earning: '200xxx',
      walletAddress: '0x4c6924...11a0fa',
      hash: '0x4c6924...11a0fa'
    },
    {
      time: '11:11 2025/6/6',
      worker: '5EewuHxa',
      earning: '200xxx',
      walletAddress: '0x4c6924...11a0fa',
      hash: '0x4c6924...11a0fa'
    }
  ]

  return (
    <div className="flex w-full flex-col items-stretch justify-center gap-4 md:gap-6">
      {/* 主容器 */}
      <div className="flex w-full flex-col gap-6 rounded-2xl bg-[#FAF9F5] p-4 pb-3 md:gap-8 md:p-6">
        
        {/* 标题区域 */}
        <div className="flex w-full items-stretch gap-4 pr-0 md:gap-6 md:pr-6">
          <div className="flex flex-1 flex-col gap-1">
            <div className="flex w-full items-center gap-4">
              <h1 className="font-serif text-xl font-semibold leading-[1.5em] text-[#141413] md:text-2xl">
                Miner Overview
              </h1>
            </div>
            <div className="flex min-h-[36px] flex-col justify-center">
              <p className="text-sm font-normal leading-[1.5em] text-[#3D3D3A] md:text-base">
                Track hashrate, stakes, scores and rewards across all your active workers
              </p>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex w-full flex-col gap-4 md:gap-6">
          
          {/* 标签导航 */}
          <div className="flex w-full items-center gap-2.5">
            <div className="flex w-fit items-center justify-center gap-2 rounded-3xl bg-[#FAF9F5]">
              <button
                onClick={() => handleTabChange('worker')}
                className={`rounded-3xl px-3 py-2 text-center text-sm font-medium leading-[1.57em] transition-colors md:px-4 ${
                  activeTab === 'worker' 
                    ? 'bg-[#E8E6DC] text-[#141413]' 
                    : 'bg-[#E8E6DC] text-[#73726C]'
                }`}
              >
                Worker
              </button>
              <button
                onClick={() => handleTabChange('earning')}
                className={`rounded-3xl px-3 py-2 text-center text-sm font-medium leading-[1.57em] transition-colors md:px-4 ${
                  activeTab === 'earning' 
                    ? 'bg-[#E8E6DC] text-[#141413]' 
                    : 'bg-[#E8E6DC] text-[#73726C]'
                }`}
              >
                Earning
              </button>
            </div>
          </div>

          {/* 控制区域 - 只在 Worker 标签时显示 */}
          {activeTab === 'worker' && (
            <div className="flex w-full flex-col gap-3">
              {/* 搜索和控制栏 */}
              <div className="flex w-full flex-col items-center justify-center gap-6 rounded-2xl bg-[#FAF9F5]">
                <div className="flex w-full flex-col pt-1">
                  <div className="flex w-full flex-col gap-4 md:flex-row md:items-stretch md:justify-stretch md:gap-0">
                    <div className="flex flex-1 flex-col items-center gap-2 md:h-9 md:flex-row">
                      {/* 搜索框和控制区域 */}
                      <div className="flex w-full flex-1 flex-col items-center gap-2 pr-6 md:h-9 md:flex-row">
                        {/* 搜索框 */}
                        <div className="flex w-full flex-col items-center justify-center rounded-[8px] bg-[#F7F5EE] px-4 md:w-[400px] md:max-w-full">
                          <div className="flex w-full flex-col items-center justify-center py-2">
                            <div className="flex w-full items-center gap-2">
                              <Search className="size-4 text-[#141413]" strokeWidth={1} />
                              <input
                                type="text"
                                placeholder="Search miner..."
                                className="w-full bg-transparent text-sm leading-[1.57em] text-[#141413] outline-none placeholder:text-[#B0AEA5]"
                              />
                            </div>
                          </div>
                        </div>

                        {/* 更新时间和控制 */}
                        <div className="flex w-full items-center justify-between gap-2 md:flex-1 md:justify-between">
                          <div className="flex items-center gap-2">
                            <span className="whitespace-nowrap text-xs leading-[1.5em] text-[#B0AEA5]">
                              Last updated:{lastUpdated}
                            </span>
                            {/* 刷新按钮 - 紧挨着更新时间 */}
                            <button className="flex size-6 items-center justify-center rounded-md border border-[rgba(31,30,29,0.15)] bg-[#FAF9F5]">
                              <RotateCw className="size-4 text-[#73726C]" strokeWidth={0.75} />
                            </button>
                          </div>
                          {/* 切换开关 - 单独在最右边 */}
                          <button
                            onClick={() => setToggleState(!toggleState)}
                            className={`relative h-6 w-10 rounded-full transition-colors ${
                              toggleState ? 'bg-blue-500' : 'border border-[rgba(31,30,29,0.15)] bg-[#FAF9F5]'
                            }`}
                          >
                            <div className={`absolute top-1 size-4 rounded-full transition-transform ${
                              toggleState ? 'left-5 bg-white' : 'left-1 bg-[#B0AEA5]'
                            }`} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 根据标签显示不同内容 */}
          {activeTab === 'worker' && (
            <>
              {/* 数据表格容器 */}
              <div className="flex w-full flex-col items-center justify-center overflow-hidden rounded-2xl bg-[#FAF9F5]">
                {/* 桌面端表格 */}
                <div className="hidden w-full md:block">
                  <table className="w-full">
                    {/* 表格头部 */}
                    <thead style={{ backgroundColor: '#E8E6DC' }}>
                      <tr style={{ height: '42px' }}>
                        <th className="text-left" style={{ width: '120px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            Miner
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '100px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            Status
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '130px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            1h Hashrate
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '130px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            24h Hashrate
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '110px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            1h Shares
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '110px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            24h Shares
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '80px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            Node
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '100px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            Stake
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '80px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            Score
                          </span>
                        </th>
                        <th className="text-left" style={{ width: '140px', paddingLeft: '8px', paddingRight: '8px' }}>
                          <span 
                            className="text-xs font-medium"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 500,
                              fontSize: '12px',
                              lineHeight: '1.5em',
                              color: '#3D3D3A'
                            }}
                          >
                            Operation
                          </span>
                        </th>
                      </tr>
                    </thead>

                    {/* 表格内容 */}
                    <tbody>
                      {miners.map((miner, index) => (
                        <tr 
                          key={index}
                          className="transition-colors hover:bg-[#F0EEE6]"
                          style={{ 
                            backgroundColor: 'transparent',
                            borderBottom: index < miners.length - 1 ? '1px solid #E8E6DC' : 'none'
                          }}
                        >
                          <td className="py-4" style={{ padding: '16px 8px', width: '120px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.id}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '100px' }}>
                            <div 
                              className="inline-flex items-center rounded-lg px-2 py-1"
                              style={{ backgroundColor: '#E3EDE3' }}
                            >
                              <span 
                                style={{ 
                                  fontFamily: 'Styrene B',
                                  fontWeight: 400,
                                  fontSize: '14px',
                                  lineHeight: '1.57em',
                                  color: '#0B3629'
                                }}
                              >
                                {miner.status}
                              </span>
                            </div>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '130px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.hashrate1h}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '130px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.hashrate24h}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '110px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.shares1h}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '110px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.shares24h}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '80px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.node}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '100px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.stake}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '80px' }}>
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 400,
                                fontSize: '14px',
                                lineHeight: '1.57em',
                                color: '#141413'
                              }}
                            >
                              {miner.score}
                            </span>
                          </td>
                          <td className="py-4" style={{ padding: '16px 8px', width: '140px' }}>
                            <Link href={`/${locale}/miner/${miner.id}`} className="flex items-center gap-1 transition-opacity hover:opacity-75">
                              <span 
                                style={{ 
                                  fontFamily: 'Styrene B',
                                  fontWeight: 500,
                                  fontSize: '14px',
                                  lineHeight: '1.57em',
                                  color: '#141413'
                                }}
                              >
                                View details
                              </span>
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M6 12l4-4-4-4" stroke="#73726C" strokeWidth="1.2" fill="none"/>
                              </svg>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 移动端表格 */}
                <div className="w-full md:hidden">
                  {/* 表格头部 - 移动端 */}
                  <div 
                    className="grid grid-cols-4 gap-2 px-4 py-3 text-xs font-medium"
                    style={{ 
                      backgroundColor: '#E8E6DC',
                      fontFamily: 'Styrene B',
                      fontWeight: 500,
                      color: '#3D3D3A'
                    }}
                  >
                    <div>Miner/Status</div>
                    <div className="text-center">Hashrate</div>
                    <div className="text-center">Node/Stake</div>
                    <div className="text-right">Score/Action</div>
                  </div>

                  {/* 表格内容 - 移动端 */}
                  <div>
                    {miners.map((miner, index) => (
                      <div 
                        key={index}
                        className="grid grid-cols-4 gap-2 border-b border-gray-200 p-4"
                        style={{ 
                          backgroundColor: 'transparent',
                          borderBottomColor: '#E8E6DC'
                        }}
                      >
                        {/* 第一列：Miner + Status */}
                        <div className="space-y-2">
                          <div 
                            className="truncate text-sm"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#141413'
                            }}
                          >
                            {miner.id}
                          </div>
                          <div 
                            className="inline-flex items-center rounded px-2 py-0.5 text-xs"
                            style={{ 
                              backgroundColor: '#E3EDE3',
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#0B3629'
                            }}
                          >
                            {miner.status}
                          </div>
                        </div>

                        {/* 第二列：Hashrate */}
                        <div className="space-y-2 text-center text-xs">
                          <div 
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#141413'
                            }}
                          >
                            {miner.hashrate24h}
                          </div>
                          <div 
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#141413'
                            }}
                          >
                            {miner.shares24h}
                          </div>
                        </div>

                        {/* 第三列：Node + Stake */}
                        <div className="space-y-2 text-center text-xs">
                          <div 
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#141413'
                            }}
                          >
                            {miner.node}
                          </div>
                          <div 
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#141413'
                            }}
                          >
                            {miner.stake}
                          </div>
                        </div>

                        {/* 第四列：Score + Action */}
                        <div className="space-y-2 text-right">
                          <div 
                            className="text-sm"
                            style={{ 
                              fontFamily: 'Styrene B',
                              fontWeight: 400,
                              color: '#141413'
                            }}
                          >
                            {miner.score}
                          </div>
                          <Link href={`/${locale}/miner/${miner.id}`} className="ml-auto flex items-center gap-1 text-xs transition-opacity hover:opacity-75">
                            <span 
                              style={{ 
                                fontFamily: 'Styrene B',
                                fontWeight: 500,
                                color: '#141413'
                              }}
                            >
                              Details
                            </span>
                            <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                              <path d="M6 12l4-4-4-4" stroke="#73726C" strokeWidth="1.2" fill="none"/>
                            </svg>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </>
            )}

            {/* Earning 内容 */}
            {activeTab === 'earning' && (
              <>
                {/* Earnings Cards */}
                <div className="mb-4 flex flex-col gap-2 md:flex-row">
                  {/* Today Rewards */}
                  <div className="flex flex-1 items-center gap-2 rounded-xl bg-[#F4F2E7] p-2">
                    <img src={"/images/Miner-Earning.svg"}/>
                    <div className="flex flex-1 flex-col">
                      <span className="text-sm font-normal leading-[1.571] text-[#73726C]">Today xx Rewards</span>
                      <span className="text-base font-medium leading-normal text-[#141413]">158.80 USD</span>
                    </div>
                  </div>

                  {/* Yesterday Rewards */}
                  <div className="flex flex-1 items-center gap-2 rounded-xl bg-[#F4F2E7] p-2">
                    <img src={"/images/Miner-Earning.svg"}/>
                    <div className="flex flex-1 flex-col">
                      <span className="text-sm font-normal leading-[1.571] text-[#73726C]">Yesterday xx Rewards</span>
                      <span className="text-base font-medium leading-normal text-[#141413]">158.80 USD</span>
                    </div>
                  </div>

                  {/* Total Rewards */}
                  <div className="flex flex-1 items-center gap-2 rounded-xl bg-[#F4F2E7] p-2">
                    <img src={"/images/Miner-Earning.svg"}/>
                    <div className="flex flex-1 flex-col">
                      <span className="text-sm font-normal leading-[1.571] text-[#73726C]">Total xx Rewards</span>
                      <span className="text-base font-medium leading-normal text-[#141413]">158.80 USD</span>
                    </div>
                  </div>
                </div>

                {/* Rewards Chart */}
                <div className="mb-4 flex flex-col items-center gap-2 rounded-2xl bg-[#FAF9F5] p-4">
                  <div className="flex w-full flex-col items-center gap-2">
                    <div className="flex w-full items-center gap-4 px-2">
                      <h2 className="text-lg font-medium leading-[1.556] text-[#141413]">Rewards Chart</h2>
                    </div>
                    <div className="flex h-[348px] w-full flex-col rounded-xl bg-[#F7F5EE] p-4 sm:h-[280px] md:h-[348px]">
                      <div className="relative flex h-full flex-col">
                        {/* Chart Title */}
                        <div className="absolute left-3 top-2.5 z-10 md:left-5">
                          <span className="text-xs font-normal leading-normal text-[#CC785C]">Rewards</span>
                        </div>
                        
                        {/* Chart Labels */}
                        <div className="flex size-full items-start gap-1 pt-8">
                          {/* Y-axis Labels */}
                          <div className="flex h-full w-[45px] flex-col justify-end gap-4 pt-4 md:w-[60px] md:gap-8">
                            <span className="text-center text-[10px] text-[#3D3D3A] md:text-xs">1.00</span>
                            <span className="text-center text-[10px] text-[#3D3D3A] md:text-xs">800</span>
                            <span className="text-center text-[10px] text-[#3D3D3A] md:text-xs">600</span>
                            <span className="text-center text-[10px] text-[#3D3D3A] md:text-xs">400</span>
                            <span className="text-center text-[10px] text-[#3D3D3A] md:text-xs">200</span>
                            <span className="text-center text-[10px] text-[#3D3D3A] md:text-xs">0.00</span>
                          </div>
                          
                          {/* Chart Area */}
                          <div className="flex h-full flex-1 flex-col">
                            <div className="relative flex-1">
                              {/* Grid lines */}
                              <div className="absolute inset-0 flex flex-col justify-between">
                                {Array.from({length: 6}).map((_, i) => (
                                  <div key={i} className="border-t border-dashed border-[rgba(31,30,29,0.15)]" />
                                ))}
                              </div>
                              
                              {/* Chart line */}
                              <div className="absolute inset-0 flex items-center">
                                <svg width="100%" height="100%" viewBox="0 0 1000 200" className="absolute inset-0" preserveAspectRatio="none">
                                  <path d="M0,150 Q100,120 200,130 T400,110 T600,140 T800,120 L1000,160" stroke="#DA7A5B" strokeWidth="2" fill="none" vectorEffect="non-scaling-stroke"/>
                                  <defs>
                                    <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                      <stop offset="0%" stopColor="rgba(218, 122, 91, 0.2)" />
                                      <stop offset="100%" stopColor="rgba(218, 122, 91, 0)" />
                                    </linearGradient>
                                  </defs>
                                  <path d="M0,150 Q100,120 200,130 T400,110 T600,140 T800,120 L1000,160 L1000,200 L0,200 Z" fill="url(#areaGradient)"/>
                                </svg>
                              </div>
                            </div>
                            
                            {/* X-axis Labels */}
                            <div className="flex justify-between overflow-hidden pt-4 text-[10px] text-[#73726C] md:text-xs">
                              {/* 移动端显示简化标签 */}
                              <span className="block md:hidden">10:00</span>
                              <span className="hidden md:block">6/24 10:00</span>
                              
                              <span className="block md:hidden">13:00</span>
                              <span className="hidden md:block">6/24 13:00</span>
                              
                              <span className="block md:hidden">16:00</span>
                              <span className="hidden md:block">6/24 16:00</span>
                              
                              <span className="block md:hidden">19:00</span>
                              <span className="hidden md:block">6/24 19:00</span>
                              
                              <span className="block md:hidden">22:00</span>
                              <span className="hidden md:block">6/24 22:00</span>
                              
                              <span className="block md:hidden">1:00</span>
                              <span className="hidden md:block">6/25 1:00</span>
                              
                              <span className="block md:hidden">4:00</span>
                              <span className="hidden md:block">6/25 4:00</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* History Table */}
                <div className="flex w-full flex-col items-center justify-center overflow-hidden rounded-2xl bg-[#FAF9F5]">
                  <div className="flex w-full flex-col rounded-2xl bg-[#FAF9F5] p-4">
                    <div className="flex items-stretch gap-4 pb-3">
                      <h2 className="text-lg font-medium leading-[1.556] text-[#141413]">History</h2>
                    </div>

                    {/* Table Header */}
                    <div className="hidden w-full items-center bg-[#E8E6DC] px-2 md:flex">
                      <div className="w-[266px] px-0 py-3 pr-1.5">
                        <span className="text-xs font-medium leading-normal text-[#3D3D3A]">Time</span>
                      </div>
                      <div className="w-[210px] px-0 py-3">
                        <span className="text-xs font-medium leading-normal text-[#3D3D3A]">Worker</span>
                      </div>
                      <div className="w-[186px] px-0 py-3">
                        <span className="text-xs font-medium leading-normal text-[#3D3D3A]">Earning</span>
                      </div>
                      <div className="w-[297px] px-0 py-3">
                        <span className="text-xs font-medium leading-normal text-[#3D3D3A]">Wallet Address</span>
                      </div>
                      <div className="w-[104px] px-0 py-3">
                        <span className="text-xs font-medium leading-normal text-[#3D3D3A]">Hash</span>
                      </div>
                    </div>

                    {/* Table Rows */}
                    <div className="flex w-full flex-col">
                      {earningsData.map((item, index) => (
                        <div key={index} className={`flex w-full flex-col gap-2 border-b border-[rgba(31,30,29,0.15)] bg-[#FAF9F5] px-2 py-3 md:flex-row md:items-center md:gap-0 md:py-1.5`}>
                          {/* Mobile Layout */}
                          <div className="flex flex-col gap-2 md:hidden">
                            <div className="flex justify-between">
                              <span className="text-xs text-[#73726C]">Time:</span>
                              <span className="text-sm text-[#141413]">{item.time}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-xs text-[#73726C]">Worker:</span>
                              <span className="text-sm text-[#141413]">{item.worker}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-xs text-[#73726C]">Earning:</span>
                              <span className="text-sm text-[#141413]">{item.earning}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-[#73726C]">Wallet:</span>
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-[#141413]">{item.walletAddress}</span>
                                <div className="flex size-6 items-center justify-center rounded-lg bg-[rgba(31,30,29,0.05)]">
                                  <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                                    <path d="M5.5 3.5H9.5V1.5H5.5V3.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                    <path d="M2.5 4.5H10.5V12.5H2.5V4.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                  </svg>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Desktop Layout */}
                          <div className="hidden md:flex md:w-full md:items-center">
                            <div className="w-[266px] p-1">
                              <span className="text-sm font-normal leading-[1.571] text-[#141413]">{item.time}</span>
                            </div>
                            <div className="w-[210px] py-2.5">
                              <span className="text-sm font-normal leading-[1.571] text-[#141413]">{item.worker}</span>
                            </div>
                            <div className="w-[186px]">
                              <span className="text-sm font-normal leading-[1.571] text-[#141413]">{item.earning}</span>
                            </div>
                            <div className="flex w-[297px] items-center gap-2 py-2.5">
                              <span className="truncate text-sm font-normal leading-[1.571] text-[#141413]">{item.walletAddress}</span>
                              <div className="flex size-6 items-center justify-center rounded-lg bg-[rgba(31,30,29,0.05)]">
                                <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                                  <path d="M5.5 3.5H9.5V1.5H5.5V3.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                  <path d="M2.5 4.5H10.5V12.5H2.5V4.5Z" stroke="#73726C" strokeWidth="1" fill="none"/>
                                </svg>
                              </div>
                            </div>
                            <div className="flex w-[104px] items-center gap-2 py-2.5">
                              <span
                                  className="truncate text-sm font-normal leading-[1.571] text-[#141413]">{item.hash}</span>
                              <button
                                onClick={() => handleHashClick(item.hash)}
                                className="flex size-6 cursor-pointer items-center justify-center rounded transition-colors hover:bg-[rgba(31,30,29,0.05)]"
                                title="在区块链浏览器中查看"
                              >
                                <img src={"/images/Miner-Earning-share.svg"} alt="查看交易" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </>
            )}
        </div>
      </div>
    </div>
  )
} 