'use client'

import Link from 'next/link'
import { useSelectedLayoutSegment } from 'next/navigation'
import type { HTMLAttributeAnchorTarget } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'

import { cn } from '@/lib/utils/cn'

type Menu = {
  label: React.ReactNode
  link: string
  activeSegment?: string
  linkTarget?: HTMLAttributeAnchorTarget
}

export const Nav = memo(function Nav() {
  const activeSegment = useSelectedLayoutSegment()
  const { t } = useTranslation()

  const NAV_MENUS: Menu[] = [
    {
      label: t('navigation.pool'),
      link: '/pool',
      activeSegment: 'pool',
    },
    {
      label: t('navigation.workers'),
      link: '/workers',
      activeSegment: 'workers',
    },
    {
      label: t('navigation.earnings'),
      link: '/earnings',
      activeSegment: 'earnings',
    },
    {
      label: t('navigation.assets'),
      link: '/assets',
      activeSegment: 'assets',
    },
  ]

  return (
    <nav className={cn('flex h-full gap-8')}>
      {NAV_MENUS.map((menu, i) => {
        const isActiveNav = activeSegment === menu.activeSegment

        return (
          <Link
            key={`${menu.link + i.toString()}`}
            href={menu.link}
            className={cn(
              'inline-flex items-center text-lg font-medium transition-colors hover:text-gray-900',
              isActiveNav
                ? 'text-gray-900'
                : 'text-gray-600',
            )}
            target={menu.linkTarget}
          >
            {menu.label}
          </Link>
        )
      })}
    </nav>
  )
})
