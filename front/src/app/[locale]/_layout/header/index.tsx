'use client'

import Image from 'next/image'
import Link from 'next/link'
import { memo } from 'react'

import { Button } from '@/components/ui'
import { DOGE_MINE_POOL_APP } from '@/constants'
import { useSafeTranslation } from '@/lib/use-safe-translation'
import { cn } from '@/lib/utils/cn'

// import { Nav } from './nav'

export const Header = memo(function Header() {
  const { t } = useSafeTranslation()

  return (
    <header
      className={cn(
        'sticky top-0 z-50 flex h-[80px] items-center justify-between bg-[#FAF9F5]/95 px-16 backdrop-blur-sm dark:bg-[#141413]/95',
      )}
    >
      <div className={cn('flex items-center')}>
        <Link href="/" className={cn('flex items-center gap-2')}>
          <Image
            src="/images/design/dogemine-logo.svg"
            alt="Dogemine Logo"
            width={101.58}
            height={24}
            // className="h-12 w-auto"
          />
        </Link>
      </div>

      <div className={cn('flex items-center gap-6')}>
        <Button 
          asChild 
          size="lg" 
          className="h-[48px] w-[150px] min-w-[120px] rounded-[12px] bg-[#141413] px-6 py-3 text-white hover:opacity-90 dark:bg-[#141413] dark:text-white"
        >
          <Link href={DOGE_MINE_POOL_APP.login}>
            {t('navigation.startMining', 'Start Mining')}
          </Link>
        </Button>
        {/*<LanguageChanger />*/}
        {/*<ThemeModeChanger />*/}
        {/*<UserInfo />*/}
      </div>
    </header>
  )
})
