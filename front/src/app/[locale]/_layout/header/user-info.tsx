'use client'

import Link from 'next/link'
import { useTranslation } from 'react-i18next'

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui'
import { Icons } from '@/components/ui/icons'
import { DOGE_MINE_POOL_APP } from '@/constants/link'

type MenuItem = {
  label: React.ReactNode
  handler?: () => void
  link?: string
  icon?: React.ReactNode
}

export function UserInfo() {
  const { t } = useTranslation()

  const menuItems: MenuItem[] = [
    {
      label: t('settings.account'),
      link: DOGE_MINE_POOL_APP.accountSetting,
      icon: <Icons.User className="size-4" />,
    },
    {
      label: t('settings.withdrawal'),
      link: DOGE_MINE_POOL_APP.withdrawSetting,
      icon: <Icons.HandCoins className="size-4" />,
    },
    {
      label: t('settings.notification'),
      link: DOGE_MINE_POOL_APP.notificationSetting,
      icon: <Icons.Bell className="size-4" />,
    },
    {
      label: t('auth.logout'),
      link: DOGE_MINE_POOL_APP.login,
      icon: <Icons.LogOut className="size-4" />,
    },
  ]
  const { data } = {
    data: {
      name: 'John Doe',
      avatarUrl: 'https://cdn.vuetifyjs.com/images/john.jpg',
    },
  }

  return (
    <HoverCard openDelay={0} closeDelay={0}>
      <HoverCardTrigger asChild>
        <div className="group flex h-[66px] items-center">
          <Button variant="outline" className="flex items-center gap-2">
            <Avatar className="size-5">
              <AvatarImage src={data?.avatarUrl ?? ''} alt={data?.name} />
              <AvatarFallback>{data?.name?.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="">{data?.name}</span>
          </Button>
        </div>
      </HoverCardTrigger>
      <HoverCardContent className="w-[256px]" align="end" sideOffset={-10}>
        {menuItems.map(({ label, handler, icon, link }, index) => {
          const Item = (
            <Button
              key={index}
              variant="ghost"
              className="w-full px-2"
              onClick={handler}
            >
              <div className="flex flex-1 items-center gap-2">
                {icon && <div>{icon}</div>}
                <div>{label}</div>
              </div>
              <Icons.ArrowRight className="size-4 opacity-0 transition-opacity group-hover:opacity-100" />
            </Button>
          )

          if (link) {
            return (
              <Link key={index} href={link}>
                {Item}
              </Link>
            )
          }

          // eslint-disable-next-line @eslint-react/no-missing-key
          return <>{Item}</>
        })}
      </HoverCardContent>
    </HoverCard>
  )
}
