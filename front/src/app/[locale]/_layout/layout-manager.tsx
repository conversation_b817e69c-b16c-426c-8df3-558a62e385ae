'use client'

import { usePathname } from 'next/navigation'
import type { ReactNode } from 'react'

import { Header } from './header'

interface LayoutManagerProps {
  children: ReactNode
}

export function LayoutManager({ children }: LayoutManagerProps) {
  const pathname = usePathname()
  
  // Check if the current route has a sidebar
  // This assumes routes with sidebar are under specific paths
  const hasSidebar = [
    '/dashboard',
    '/miner',
    '/validator',
    '/stake',
    '/settings',
    '/workers',
    '/earnings',
    '/assets',
    '/pool'
  ].some(path => pathname.includes(path))
  
  return (
    <>
      {/* Only render Header when not on a sidebar page */}
      {!hasSidebar && <Header />}
      {children}
    </>
  )
} 