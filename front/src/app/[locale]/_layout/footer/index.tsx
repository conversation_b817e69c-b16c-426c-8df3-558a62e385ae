'use client'

import Image from 'next/image'
import Link from 'next/link'
import { memo } from 'react'

import { useSafeTranslation } from '@/lib/use-safe-translation'
import { cn } from '@/lib/utils/cn'

export const Footer = memo(function Footer() {
  const { t } = useSafeTranslation()

  return (
    <footer className={cn('bg-gray-900 text-white')}>
      <div className="mx-auto max-w-7xl px-6 py-12 lg:px-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <Link href="/" className="flex items-center gap-2">
              <Image
                src="/images/design/dogemine-logo.svg"
                alt="Dogemine Logo"
                width={120}
                height={40}
                className="h-10 w-auto brightness-0 invert"
              />
            </Link>
            <p className="mt-4 text-gray-400">
              {t('footer.description', 'The most trusted Dogecoin mining pool platform with advanced technology and transparent rewards.')}
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold">
              {t('footer.quickLinks', 'Quick Links')}
            </h3>
            <ul className="mt-4 space-y-2">
              <li>
                <Link href="/pool" className="text-gray-400 hover:text-white">
                  {t('navigation.pool', 'Pool')}
                </Link>
              </li>
              <li>
                <Link href="/workers" className="text-gray-400 hover:text-white">
                  {t('navigation.workers', 'Workers')}
                </Link>
              </li>
              <li>
                <Link href="/earnings" className="text-gray-400 hover:text-white">
                  {t('navigation.earnings', 'Earnings')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-semibold">
              {t('footer.support', 'Support')}
            </h3>
            <ul className="mt-4 space-y-2">
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  {t('footer.documentation', 'Documentation')}
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  {t('footer.contactUs', 'Contact Us')}
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  {t('footer.faq', 'FAQ')}
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 border-t border-gray-800 pt-8 text-center text-gray-400">
          <p>
            {t('footer.copyright', '© 2024 DogeMine. All rights reserved.')}
          </p>
        </div>
      </div>
    </footer>
  )
})
