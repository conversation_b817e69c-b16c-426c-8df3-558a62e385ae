'use client'

import { useEffect, useRef } from 'react'

import { CoreFeaturesSection } from './core-features-section'
import { CTASection } from './cta-section'
import { GetStartedSection } from './get-started-section'
import { HeroSection } from './hero-section'
import { WhyDogeMineSection } from './why-dogemine-section'

export function LandingPage() {
  // 为每个部分创建引用
  const heroRef = useRef(null)
  const featuresRef = useRef(null)

  useEffect(() => {
    // 创建 Intersection Observer，当元素进入视口时触发动画
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          // 当元素进入视口
          if (entry.isIntersecting) {
            // 添加动画类
            entry.target.classList.add('fade-in')
            // 元素已经显示，不再需要观察
            observer.unobserve(entry.target)
          }
        })
      },
      {
        // 设置触发阈值，元素有 20% 进入视口时触发
        threshold: 0.2,
        // 设置根边距，提前触发动画
        rootMargin: '0px 0px -100px 0px'
      }
    )

    // 开始观察所有部分
    const sections = [
      heroRef.current,
      featuresRef.current
    ]

    sections.forEach((section) => {
      if (section) {
        observer.observe(section)
      }
    })

    // 清理函数
    return () => {
      sections.forEach((section) => {
        if (section) {
          observer.unobserve(section)
        }
      })
    }
  }, [])

  // 部分的基本样式
  const sectionBaseStyle = {
    opacity: 0,
    transform: 'translateY(30px)',
    transition: 'opacity 0.8s ease, transform 0.8s ease'
  }

  return (
    <div className="min-h-screen bg-[#FAF9F5]">
      <style jsx global>{`
        .fade-in {
          opacity: 1 !important;
          transform: translateY(0) !important;
        }

        /* 为不同部分添加不同的动画效果 */
        .slide-in-left {
          opacity: 0;
          transform: translateX(-50px);
          transition: opacity 0.8s ease, transform 0.8s ease;
        }
        
        .slide-in-left.fade-in {
          opacity: 1;
          transform: translateX(0);
        }

        .scale-in {
          opacity: 0;
          transform: scale(0.95);
          transition: opacity 0.8s ease, transform 0.8s ease;
        }
        
        .scale-in.fade-in {
          opacity: 1;
          transform: scale(1);
        }
      `}</style>

      {/* Hero Section - 从下方淡入 */}
      <div ref={heroRef} style={sectionBaseStyle}>
        <HeroSection />
      </div>

      {/* Get Started Section - 不再使用外部动画控制 */}
      <div>
        <GetStartedSection />
      </div>

      {/* Core Features Section - 缩放效果 */}
      <div ref={featuresRef} className="scale-in">
        <CoreFeaturesSection />
      </div>

      {/* Why DogeMine Section - 不再使用外部动画控制 */}
      <div>
        <WhyDogeMineSection />
      </div>

      {/* CTA Section - 不再使用外部动画控制 */}
      <div>
        <CTASection />
      </div>
    </div>
  )
}