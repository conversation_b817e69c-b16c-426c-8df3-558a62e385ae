'use client'

import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

import { useSafeTranslation } from '@/lib/use-safe-translation'

export function GetStartedSection() {
  const { t } = useSafeTranslation()
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [visibleElements, setVisibleElements] = useState({
    title: false,
    steps: [false, false, false]
  })
  
  // 创建引用
  const titleRef = useRef<HTMLDivElement>(null)
  const stepRefs = useRef<Array<HTMLDivElement | null>>([null, null, null])
  
  const steps = [
    {
      number: '01',
      title: t('sections.getStarted.steps.createAccount.title', 'Create Account'),
      image: '/images/get-started-step-01.svg',
      bgColor: 'bg-white/50',
      hoverBgColor: 'bg-white',
      stepBg: 'bg-[#CC785C]',
      stepText: 'text-white'
    },
    {
      number: '02', 
      title: t('sections.getStarted.steps.setupMiner.title', 'Setup Miner'),
      image: '/images/get-started-step-02.svg',
      bgColor: 'bg-white/50',
      hoverBgColor: 'bg-white',
      stepBg: 'bg-[#E3DFCE]',
      stepText: 'text-[#141413]'
    },
    {
      number: '03',
      title: t('sections.getStarted.steps.startEarning.title', 'Start Earning'), 
      image: '/images/get-started-step-03.svg',
      bgColor: 'bg-white/50',
      hoverBgColor: 'bg-white',
      stepBg: 'bg-[#F1E6D0]',
      stepText: 'text-[#141413]'
    }
  ]

  // 设置 ref 的回调函数
  const setStepRef = (el: HTMLDivElement | null, index: number) => {
    stepRefs.current[index] = el
  }

  useEffect(() => {
    // 创建 Intersection Observer 实例
    const observerOptions = {
      threshold: 0.2,
      rootMargin: '0px 0px -100px 0px'
    }

    // 标题的观察器
    const titleObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setVisibleElements(prev => ({ ...prev, title: true }))
          titleObserver.unobserve(entry.target)
        }
      })
    }, observerOptions)

    // 步骤卡片的观察器 - 使用延迟显示
    const stepObservers = stepRefs.current.map((_, index) => {
      return new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // 使用 setTimeout 创建级联效果，每个卡片比前一个晚 200ms 显示
            setTimeout(() => {
              setVisibleElements(prev => {
                const newSteps = [...prev.steps]
                newSteps[index] = true
                return { ...prev, steps: newSteps }
              })
            }, 200 * index)
            
            stepObservers[index].unobserve(entry.target)
          }
        })
      }, observerOptions)
    })

    // 开始观察元素
    if (titleRef.current) {
      titleObserver.observe(titleRef.current)
    }

    stepRefs.current.forEach((ref, index) => {
      if (ref && stepObservers[index]) {
        stepObservers[index].observe(ref)
      }
    })

    // 清理函数
    return () => {
      if (titleRef.current) {
        titleObserver.unobserve(titleRef.current)
      }
      
      stepRefs.current.forEach((ref, index) => {
        if (ref && stepObservers[index]) {
          stepObservers[index].unobserve(ref)
        }
      })
    }
  }, [])

  return (
    <section className="w-full py-16">
      <div className="mx-auto max-w-[1440px] px-16">
        {/* Section Header - 添加动画类 */}
        <div 
          ref={titleRef}
          className={`mb-12 text-center transition-all duration-700 ease-out ${
            visibleElements.title ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}
        >
          <h2 className="font-styrene-b text-[40px] font-medium leading-[56px] text-[#141413]">
            {t('sections.getStarted.title', 'Get started')}
          </h2>
        </div>

        {/* Steps Grid */}
        <div className="flex gap-4">
          {steps.map((step, index) => (
            <div 
              key={index} 
              ref={(el) => setStepRef(el, index)}
              className={`flex-1 transition-all duration-700 ease-out ${
                visibleElements.steps[index] ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
              }`}
              style={{ transitionDelay: `${200 * index}ms` }}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <div className={`${hoveredIndex === index ? step.hoverBgColor : step.bgColor} cursor-pointer rounded-xl p-4 pb-6 transition-all duration-300 ease-in-out ${hoveredIndex === index ? 'shadow-lg' : ''}`}>
                {/* Image Container */}
                <div className="mb-4 flex h-[240px] items-center justify-center rounded-lg bg-white p-2">
                  <div className="relative size-full">
                    <Image
                      src={step.image}
                      alt={step.title}
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>

                {/* Step Info */}
                <div className="flex items-center gap-2">
                  {/* Step Number */}
                  <div className={`${step.stepBg} flex size-8 items-center justify-center rounded-full`}>
                    <span className={`font-styrene-b text-[11.2px] font-medium ${step.stepText}`}>
                      {step.number}
                    </span>
                  </div>

                  {/* Step Title */}
                  <div className="flex-1">
                    <h3 className="font-styrene-b text-xl font-medium text-[#141413]">
                      {step.title}
                    </h3>
                  </div>

                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
} 