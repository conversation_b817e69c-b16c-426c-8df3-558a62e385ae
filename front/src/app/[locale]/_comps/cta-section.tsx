'use client'

import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useEffect, useRef, useState } from 'react'

import { Button } from '@/components/ui/button'
import { useSafeTranslation } from '@/lib/use-safe-translation'

export function CTASection() {
  const { t } = useSafeTranslation()
  const [hoverX, setHoverX] = useState(false)
  const [hoverTelegram, setHoverTelegram] = useState(false)
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const sectionRef = useRef<HTMLDivElement>(null)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [visibleElements, setVisibleElements] = useState({
    titleLine1: false,
    titleLine2: false,
    button: false,
    contactInfo: false,
    footer: false
  })

  // 在客户端挂载后设置 mounted 状态
  useEffect(() => {
    setMounted(true)
  }, [])

  // 监听滚动事件并触发动画
  useEffect(() => {
    if (!sectionRef.current) return;

    // 创建观察器实例来按顺序触发元素渐入效果
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          // 当 section 进入视口时，按顺序触发动画
          setTimeout(() => setVisibleElements(prev => ({ ...prev, titleLine1: true })), 100);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, titleLine2: true })), 600);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, button: true })), 1100);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, contactInfo: true })), 1600);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, footer: true })), 2100);
          
          // 移除观察器
          observer.unobserve(entry.target);
        }
      },
      {
        threshold: 0.15, // 当 15% 的元素可见时触发
        rootMargin: "0px 0px -100px 0px" // 提前触发
      }
    );

    // 开始观察 section
    observer.observe(sectionRef.current);

    // 处理滚动事件以计算滚动进度
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // 计算 section 在视口中的位置
      const sectionTop = rect.top;
      
      // 计算滚动进度 (0 表示 section 顶部刚进入视口底部，1 表示 section 已完全滚过视口)
      let progress = 0;
      
      // 当 section 顶部处于视口底部到视口顶部之间时
      if (sectionTop <= windowHeight && sectionTop >= 0) {
        progress = 1 - sectionTop / windowHeight;
      } 
      // 当 section 顶部已经超过视口顶部时
      else if (sectionTop < 0) {
        progress = 1;
      }
      
      // 将进度值限制在 0-1 之间
      progress = Math.max(0, Math.min(1, progress));
      setScrollProgress(progress);
    };

    // 初始计算一次
    handleScroll();
    
    // 添加滚动事件监听
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 默认颜色（暗色模式适配）
  const defaultColor = mounted && theme === 'dark' ? "#FFFFFF" : "#141413"
  const [isDarkMode, setIsDarkMode] = useState(false)

  // 计算元素的视差动画效果
  const getParallaxStyles = () => {
    return {
      titleLine1Transform: `translateY(${-scrollProgress * 15}px)`,
      titleLine2Transform: `translateY(${-scrollProgress * 20}px)`,
      buttonTransform: `translateY(${-scrollProgress * 25}px)`,
    };
  };

  const parallaxStyles = getParallaxStyles();

  return (
    <section className="w-full bg-[#F4F2E7] pb-4 pt-14 dark:bg-[#1A1A1A]" ref={sectionRef}>
      <div className="mx-auto max-w-[1440px] px-16">
        <div className="text-center">
          {/* Main CTA */}
          <div className="mx-auto mb-16 max-w-[810px]">
            <h2 className="mb-6 font-styrene-b text-[40px] font-medium leading-[56px] text-[#141413] dark:text-white">
              {t('sections.cta.title', 'Mine More. Earn More.\nTriple your mining profits today').split('\n').map((line, index) => (
                <span 
                  key={index} 
                  className={`block transition-all duration-1000 ease-out ${
                    index === 0 
                      ? visibleElements.titleLine1 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                      : visibleElements.titleLine2 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                  }`}
                  style={{ 
                    transitionDelay: index === 0 ? '0.1s' : '0.5s',
                    transform: index === 0 ? parallaxStyles.titleLine1Transform : parallaxStyles.titleLine2Transform
                  }}
                >
                  {line}
                </span>
              ))}
            </h2>
            
            <div 
              className={`transition-all duration-1000 ease-out ${
                visibleElements.button ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
              }`}
              style={{ 
                transitionDelay: '0.8s',
                transform: parallaxStyles.buttonTransform
              }}
            >
              <Button className="rounded-xl border border-[#141413] bg-[#141413] px-4 py-3 font-styrene-b text-base text-white transition-transform duration-300 ease-out hover:scale-105 hover:bg-[#2D2D2B] dark:border-white dark:bg-white dark:text-[#141413] dark:hover:bg-gray-200">
                {t('sections.cta.button', 'Start Earning')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-10 bg-[#F4F2E7] dark:bg-[#1A1A1A]">
        {/* Upper Footer with Contact and Social Icons */}
        <div 
          className={`w-full transition-all duration-1000 ease-out ${
            visibleElements.contactInfo ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}
          style={{ transitionDelay: '1.2s' }}
        >
          <div className="mx-auto max-w-[1400px] px-5">
            <div className="flex items-center justify-between py-4">
              {/* Left Side - Contact */}
              <div className="flex items-center gap-8">
                <div className="flex gap-8">
                  <span className="font-styrene-b text-base uppercase leading-7 tracking-[12.5%] text-[#141413] dark:text-white">
                    {t('sections.cta.business', '💬 Business')}
                  </span>
                  <div className="flex gap-2">
                    <span className="font-styrene-b text-base uppercase leading-7 tracking-[12.5%] text-[#141413] dark:text-white">
                      {t('sections.cta.support', '🐛 Support:')}
                    </span>
                    <span className="font-styrene-b text-base uppercase leading-7 tracking-[12.5%] text-[#141413] dark:text-white">
                      {t('sections.cta.github', 'GitHub')}
                    </span>
                  </div>
                </div>
              </div>

              {/* Right Side - Social Icons */}
              <div className="flex gap-4">
                {/* X (Twitter) Icon */}
                <div 
                  className={`flex size-8 cursor-pointer items-center justify-center rounded-full transition-colors duration-300 ${hoverX ? 'bg-[#141413]' : 'border border-[#141413] dark:border-white'}`}
                  onMouseEnter={() => setHoverX(true)}
                  onMouseLeave={() => setHoverX(false)}
                >
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" clipRule="evenodd"
                          d="M13.685 14.005H9.625L5.88 8.545L1.19 14.005H0L5.355 7.775L0 0.00500488H4.06L7.595 5.15L12.04 0.00500488H13.23L8.155 5.92L13.685 14.005ZM12.04 13.13H10.185L1.645 0.915005H3.5L12.04 13.13Z"
                          fill={hoverX ? "#FFFFFF" : defaultColor}
                    />
                  </svg>
                </div>
                
                {/* Telegram Icon */}
                <div 
                  className={`flex size-8 cursor-pointer items-center justify-center rounded-full transition-colors duration-300 ${hoverTelegram ? 'bg-[#141413]' : 'border border-[#141413] dark:border-white'}`}
                  onMouseEnter={() => setHoverTelegram(true)}
                  onMouseLeave={() => setHoverTelegram(false)}
                >
                  <svg width="18" height="14" viewBox="0 0 18 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M16.4419 0.54895C16.6756 0.462986 16.7822 0.523608 16.813 0.552856C16.8432 0.581619 16.9307 0.702759 16.8892 1.02356L16.8628 1.17395L14.2983 13.0499C14.217 13.4054 14.1012 13.4792 14.0718 13.4923C14.0417 13.5056 13.9202 13.5364 13.6372 13.3859L9.75635 10.5782L9.42041 10.3351L9.11963 10.6202L7.35498 12.2882L7.55615 9.4718L14.645 3.18176C14.756 3.0848 14.9217 2.90291 14.9302 2.6427C14.941 2.30616 14.6971 2.11639 14.5122 2.0509C14.3405 1.99015 14.1614 1.99945 14.0161 2.03137C13.8653 2.06453 13.7107 2.1297 13.563 2.22473L4.80127 7.64368L1.16064 6.52747C1.08099 6.50304 1.02142 6.47666 0.977051 6.45422C1.05463 6.39721 1.17912 6.32747 1.36572 6.25598L16.4419 0.54895Z"
                      stroke={hoverTelegram ? "#FFFFFF" : defaultColor}
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div 
          className={`w-full border-t border-[#141413]/10 transition-all duration-1000 ease-out dark:border-white/10 ${
            visibleElements.footer ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}
          style={{ transitionDelay: '1.6s' }}
        >
          <div className="mx-auto max-w-[1400px] px-5">
            <div className="flex items-center justify-between py-4">
              {/* Copyright */}
              <div>
                <span className="font-['Work_Sans'] text-[15px] leading-6 text-[#141413] dark:text-white">
                  {t('sections.cta.copyright', '© 2025 Uplift Founders')}
                </span>
              </div>

              {/* Logo */}
              <div className="flex items-center">
                <Image
                    src="/images/dogemine-footer-logo.svg"
                    alt="DogeMine Logo"
                    width={120}
                    height={36}
                    className="h-auto"
                />
              </div>

              {/* Links */}
              <div className="flex gap-8">
                <span className="cursor-pointer font-['Work_Sans'] text-[15px] leading-6 text-[#141413] hover:underline dark:text-white">
                  {t('sections.cta.imprint', 'Imprint')}
                </span>
                <span className="cursor-pointer font-['Work_Sans'] text-[15px] leading-6 text-[#141413] hover:underline dark:text-white">
                  {t('sections.cta.privacy', 'Privacy')}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 