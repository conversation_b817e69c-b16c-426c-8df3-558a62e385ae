'use client'

import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

import { useSafeTranslation } from '@/lib/use-safe-translation'

// Define the feature interface
interface Feature {
  title: string
  description: string
  bgColor: string
  icon: string
  additionalPoints?: string[]
}

export function WhyDogeMineSection() {
  const { t } = useSafeTranslation()
  const sectionRef = useRef<HTMLDivElement>(null)
  const [visibleElements, setVisibleElements] = useState({
    searchBar: false,
    title: false,
    description: false,
    features: [false, false, false, false]
  })
  const [scrollProgress, setScrollProgress] = useState(0)
  
  const features: Feature[] = [
    {
      title: t('sections.whyDogemine.features.tripleReward.title', 'Triple Reward Boost'),
      description: t('sections.whyDogemine.features.tripleReward.description', 'Hashrate reuse technology enables single rig to generate LTC+DOGE+Alpha simultaneously, proven 200%-300% higher yield vs traditional pools.'),
      bgColor: '#C9D3DA',
      icon: '/images/triple-reward-icon.svg'
    },
    {
      title: t('sections.whyDogemine.features.zeroCost.title', 'Zero-Cost Onboarding'), 
      description: t('sections.whyDogemine.features.zeroCost.description', 'Permanent 0% pool fee, compatible with all Scrypt miners, 5-minute setup with no hardware modification.'),
      bgColor: '#E3DFCE',
      icon: '/images/zero-cost-icon.svg'
    },
    {
      title: t('sections.whyDogemine.features.hyperStable.title', 'Hyper-Stable Node Network'),
      description: t('sections.whyDogemine.features.hyperStable.description', '13 global self-operated validator clusters (latency<50ms) with load balancing + hot backup, ensuring 99.99% uptime to eliminate hashrate fluctuation losses.'),
      bgColor: '#E9D6DE',
      icon: '/images/network-stability-icon.svg'
    },
    {
      title: t('sections.whyDogemine.features.transparency.title', 'Full Transparency'),
      description: t('sections.whyDogemine.features.transparency.description', 'On-chain real-time disclosure:'),
      additionalPoints: [
        t('sections.whyDogemine.features.transparency.point1', '① Hashrate distribution'),
        t('sections.whyDogemine.features.transparency.point2', '② Reward records'),
        t('sections.whyDogemine.features.transparency.point3', '③ Alpha weights, verifiable via block explorer.')
      ],
      bgColor: '#E3EDE3',
      icon: '/images/transparency-icon.svg'
    }
  ]

  // 监听滚动事件并计算视差效果
  useEffect(() => {
    if (!sectionRef.current) return;

    // 创建观察器实例来按顺序触发元素渐入效果
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          // 当 section 进入视口时，按顺序触发动画
          setTimeout(() => setVisibleElements(prev => ({ ...prev, searchBar: true })), 100);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, title: true })), 600);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, description: true })), 1100);
          
          // 卡片从左到右依次显示，间隔更大
          features.forEach((_, index) => {
            setTimeout(() => {
              setVisibleElements(prev => {
                const newFeatures = [...prev.features];
                newFeatures[index] = true;
                return { ...prev, features: newFeatures };
              });
            }, 1600 + (index * 300)); // 每张卡片延迟 300ms（原来是 200ms）
          });
          
          // 移除观察器
          observer.unobserve(entry.target);
        }
      },
      {
        threshold: 0.15, // 当 15% 的元素可见时触发
        rootMargin: "0px 0px -100px 0px" // 提前触发
      }
    );

    // 开始观察 section
    observer.observe(sectionRef.current);

    // 处理滚动事件以计算滚动进度
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const offsetTop = rect.top + scrollTop;
      const windowHeight = window.innerHeight;

      // 计算元素在视口中的位置
      const elementTop = rect.top;
      const elementHeight = rect.height;
      
      // 计算滚动进度
      let progress = 0;
      
      // 当元素进入视口并直到完全离开视口上方
      if (elementTop < windowHeight && elementTop > -elementHeight) {
        // 将视口范围映射为 0-1 的进度值
        progress = Math.min(1, Math.max(0, 1 - (elementTop / windowHeight)));
      } else if (elementTop <= -elementHeight) {
        progress = 1; // 元素已完全离开视口上方
      }
      
      setScrollProgress(progress);
    };

    // 初始执行一次
    handleScroll();
    
    // 添加滚动事件监听
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
      window.removeEventListener('scroll', handleScroll);
    };
  }, [features.length]);

  // 计算元素的视差动画效果
  const getParallaxStyles = () => {
    return {
      searchBarTransform: `translateY(${-scrollProgress * 20}px)`,
      titleTransform: `translateY(${-scrollProgress * 25}px)`,
      descriptionTransform: `translateY(${-scrollProgress * 30}px)`,
    };
  };

  const parallaxStyles = getParallaxStyles();

  // 获取卡片动画样式
  const getFeatureStyle = (index: number) => {
    // 基本样式 - 从下方进入，初始时有轻微的比例缩小和轻微旋转
    let baseTransform = 'translateY(45px) scale(0.98) rotate(0.5deg)';
    let opacity = 0;
    let filter = 'blur(5px)';
    
    // 如果可见，则应用正常位置
    if (visibleElements.features[index]) {
      baseTransform = 'translateY(0) scale(1) rotate(0deg)';
      opacity = 1;
      filter = 'blur(0)';
    }
    
    // 应用滚动视差效果 - 滚动时轻微上移
    const scrollOffset = scrollProgress * 25 * (1 + index * 0.15); // 每个卡片有不同的移动速度
    const parallaxTransform = `translateY(${-scrollOffset}px)`;
    
    return {
      transform: `${baseTransform} ${parallaxTransform}`,
      opacity,
      filter,
      transitionProperty: 'opacity, transform, filter',
      transitionDuration: '1.5s',
      transitionTimingFunction: 'cubic-bezier(0.22, 1, 0.36, 1)',
      transitionDelay: `${index * 0.2 + 0.1}s`,
    };
  };

  return (
    <section className="w-full overflow-y-auto py-16" ref={sectionRef}>
      <div className="mx-auto max-w-[1440px] px-16">
        {/* Search Bar */}
        <div 
          className={`mx-auto mb-6 max-w-[324px] transition-all duration-700 ease-out ${
            visibleElements.searchBar ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ transform: parallaxStyles.searchBarTransform }}
        >
          <div className="flex h-[40px] items-center overflow-hidden rounded-lg bg-[#E3DFCE]">
            <div className="flex h-[40px] w-[40px] flex-shrink-0 items-center justify-center bg-[#141413]">
              <div className="relative size-6">
                <Image
                    src="/images/emoji.svg"
                    alt="Emoji"
                    fill
                    className="object-contain"
                />
              </div>
            </div>
            <div className="flex-1 px-1">
              <span className="font-merriweather text-xs text-[#3D3D3A]">
               {t('sections.whyDogemine.searchPlaceholder', 'Search features, benefits, and technical details...')}
              </span>
            </div>
          </div>
        </div>

        {/* Section Header */}
        <div className="mb-8 text-center">
          <h2 
            className={`mb-4 font-styrene-b text-[40px] font-medium leading-[56px] text-[#141413] transition-all duration-700 ease-out ${
              visibleElements.title ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
            }`}
            style={{ transform: parallaxStyles.titleTransform }}
          >
            {t('sections.whyDogemine.title', 'Why Dogeminee?')}
          </h2>
          <div className="mx-auto max-w-[998px]">
            <p 
              className={`font-merriweather text-base leading-6 text-[#141413] transition-all duration-700 ease-out ${
                visibleElements.description ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
              }`}
              style={{ transform: parallaxStyles.descriptionTransform }}
            >
              {t('sections.whyDogemine.description', "Join the mining revolution that's reshaping the future of AI and cryptocurrency. DogeMine doesn't just mine coins—we're building the infrastructure for tomorrow's intelligent networks while maximizing your returns today.")}
            </p>
          </div>
        </div>

        {/* Features Grid - 4 cards in a row */}
        <div className="mx-auto max-w-[1312px] px-16">
          <div className="flex flex-row gap-4">
            {features.map((feature, index) => (
              <div 
                key={index} 
                className="flex-1 overflow-hidden rounded-3xl" 
                style={getFeatureStyle(index)}
              >
                <div
                  className="flex h-full flex-col items-center gap-4 p-6"
                  style={{backgroundColor: feature.bgColor}}
                >
                  {/* Icon */}
                  <div className="flex h-[100px] w-[100px] items-center justify-center">
                    <Image
                      src={feature.icon}
                      alt={feature.title}
                      width={100}
                      height={100}
                      className="object-contain"
                    />
                  </div>

                  {/* Content */}
                  <div className="w-full flex-1 text-left">
                    <h3 className="mb-2 whitespace-nowrap font-merriweather text-lg font-bold leading-[30px] text-[#141413]">
                      {feature.title}
                    </h3>
                    <p className="font-merriweather text-sm leading-6 text-[#141413]">
                      {feature.description}
                    </p>
                    {/* Add additional points if they exist */}
                    {feature.additionalPoints && (
                      <div className="mt-2">
                        {feature.additionalPoints.map((point, idx) => (
                          <p key={idx} className="font-merriweather text-sm leading-6 text-[#141413]">
                            {point}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}