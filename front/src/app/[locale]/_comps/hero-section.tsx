'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useParams } from 'next/navigation'

import { Button } from '@/components/ui/button'
import { useSafeTranslation } from '@/lib/use-safe-translation'

export function HeroSection() {
  const { t } = useSafeTranslation()
  const params = useParams()
  const locale = params.locale as string

  return (
      <section className="w-full">
        <div className="mx-auto max-w-[1440px] px-16">
          <div className="flex min-h-[646px] items-center justify-center gap-8 pb-[136px] pt-[40.21px]">
            {/* Left Content */}
            <div className="max-w-[556px] flex-1">
              <div className="space-y-3">
                {/* Main Heading */}
                <div className="space-y-1">
                  <h1 className="font-eb-garamond text-[56px] font-bold uppercase leading-[68px] text-[#141413] dark:text-white">
                    {t('landing.title', 'Triple Mining, Triple Rewards')}
                  </h1>
                </div>

                {/* Description */}
                <div className="py-1">
                  <p className="font-merriweather text-[18px] leading-[28px] text-[#3D3D3A] dark:text-gray-300">
                    {t('landing.description', 'Dogemine is the world\'s first mining pool enabling Scrypt miners to join Bittensor subnet. Our merged mining technology allows traditional miners to earn Alpha tokens through subnet validation while mining LTC/DOGE, boosting ROI by 300% per kWh.')}
                  </p>
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-wrap gap-4 pt-3">
                  <Link href={`/${locale}/dashboard`}>
                    <Button
                        className="h-[57px] w-[156px] rounded-xl bg-[#141413] px-[32px] pb-[19px] pt-[18px] font-styrene-b text-base text-white transition-transform duration-300 ease-out hover:scale-105 hover:bg-[#2D2D2B]"
                    >
                      {t('landing.getStarted', 'Get started')}
                    </Button>
                  </Link>
                  <Button
                      variant="outline"
                      className="h-[58px] w-[157px] rounded-xl border border-[#141413] bg-transparent px-[33px] py-[19px] font-styrene-b text-base text-[#141413] transition-transform duration-300 ease-out hover:scale-105 hover:bg-transparent dark:border-white dark:bg-transparent dark:text-white dark:hover:bg-transparent"
                  >
                    {t('landing.learnMore', 'Learn More')}
                  </Button>
                </div>
              </div>
            </div>

            {/* Right Image */}
            <div className="relative h-[544px] max-w-screen-sm flex-1 rounded-lg  p-12">
              <div className="relative size-full">
                <Image
                    src="/images/doge-character-hero.svg"
                    alt="DogeMine Character"
                    fill
                    className="object-contain"
                    priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>
  )
} 