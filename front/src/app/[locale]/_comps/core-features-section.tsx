'use client'

import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

import { useSafeTranslation } from '@/lib/use-safe-translation'

export function CoreFeaturesSection() {
  const [activeCardIndex, setActiveCardIndex] = useState(0) // 当前激活的卡片索引
  const sectionRef = useRef<HTMLDivElement>(null) // 用于监测整个 section 的位置
  const [sectionProgress, setSectionProgress] = useState(0) // 整个部分的滚动进度 (0-1)
  const [sectionHeight, setSectionHeight] = useState(600) // section 的高度，根据视口高度动态设置
  const [visibleElements, setVisibleElements] = useState({
    searchBar: false,
    title: false,
    description: false,
    cards: false
  })
  const { t } = useSafeTranslation()

  const cards = [
    {
      id: 1,
      title: t('sections.coreFeatures.features.hybridMining.title', 'Hybrid Mining Engine'),
      description: t('sections.coreFeatures.features.hybridMining.description', 'Our patented merged mining protocol enables single PoW to simultaneously mine LTC/DOGE and validate Bittensor subnet tasks, boosting miner efficiency by 300% for "triple rewards with one rig".'),
      backgroundColor: '#FFFFFF',
      backgroundImage: '/images/feature-card-1-bg.png',
      iconSize: { width: 106, height: 108 },
      titleFont: 'Styrene_B',
      titleColor: '#141413'
    },
    {
      id: 2,
      title: t('sections.coreFeatures.features.dynamicWeight.title', 'Dynamic Weight Allocation'),
      description: t('sections.coreFeatures.features.dynamicWeight.description', 'An on-chain verifiable model ensures fair Alpha distribution, while fixed coin rewards provide stable baseline income.'),
      backgroundColor: '#FFFFFF',
      backgroundImage: '/images/feature-card-2-bg.png',
      iconSize: { width: 106, height: 108 },
      titleFont: 'Merriweather_Sans',
      titleColor: '#0F0F0E'
    },
    {
      id: 3,
      title: t('sections.coreFeatures.features.taskScheduler.title', 'Intelligent Task Scheduler'),
      description: t('sections.coreFeatures.features.taskScheduler.description', 'Real-time chain analytics auto-switches optimal mining chain (prioritizes DOGE/LTC based on profitability and network conditions).'),
      backgroundColor: '#FFFFFF',
      backgroundImage: '/images/feature-card-3-bg.png',
      iconSize: { width: 106, height: 108 },
      titleFont: 'Merriweather_Sans',
      titleColor: '#0F0F0E'
    }
  ]

  // 设置组件高度
  useEffect(() => {
    const updateSectionHeight = () => {
      // 根据视口高度设置容器高度
      if (typeof window !== 'undefined') {
        // 设置为视口高度的 75%，最小 600px
        setSectionHeight(Math.max(600, window.innerHeight * 0.75));
      }
    };
    
    updateSectionHeight();
    window.addEventListener('resize', updateSectionHeight);
    
    return () => {
      window.removeEventListener('resize', updateSectionHeight);
    };
  }, []);

  // 处理导航点击事件
  const handleDotClick = (index: number) => {
    if (index === activeCardIndex) return; // 避免重复点击当前激活的卡片
    setActiveCardIndex(index);
  };

  // 计算每张卡片的样式和位置
  const getCardStyle = (index: number) => {
    // 默认位置是完全隐藏在下方
    let translateY = '100%';
    let opacity = 0;
    let zIndex = 0;
    let scale = 1;
    
    if (index === activeCardIndex) {
      // 当前激活的卡片
      translateY = '0%';
      opacity = 1;
      zIndex = 10;
      scale = 1;
    } else if (index < activeCardIndex) {
      // 已经展示过的卡片移到上方
      translateY = '-100%';
      opacity = 0;
      zIndex = 5;
      scale = 0.98;
    } else {
      // 还未展示的卡片在下方等待
      translateY = '100%';
      opacity = 0;
      zIndex = 5;
      scale = 0.98;
    }

    return {
      transform: `translateY(${translateY}) scale(${scale})`,
      opacity,
      zIndex,
      transition: 'transform 0.65s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.5s ease-in-out',
    };
  };

  // 添加对整个 section 滚动进度的监听
  useEffect(() => {
    if (!sectionRef.current) return;

    // 创建观察器实例来按顺序触发元素渐入效果
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          // 当 section 进入视口时，按顺序触发动画
          setTimeout(() => setVisibleElements(prev => ({ ...prev, searchBar: true })), 100);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, title: true })), 600);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, description: true })), 1100);
          setTimeout(() => setVisibleElements(prev => ({ ...prev, cards: true })), 1600);
          
          // 移除观察器
          observer.unobserve(entry.target);
        }
      },
      {
        threshold: 0.15, // 当 15% 的元素可见时触发
        rootMargin: "0px 0px -100px 0px" // 提前触发
      }
    );

    // 开始观察 section
    observer.observe(sectionRef.current);

    const calculateSectionProgress = () => {
      if (!sectionRef.current) return;
      
      const rect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // 计算 section 在视口中的位置
      const sectionTop = rect.top;
      const sectionHeight = rect.height;
      
      // 计算滚动进度 (0 表示 section 顶部刚进入视口底部，1 表示 section 已完全滚过视口)
      let progress = 0;
      
      // 当 section 顶部处于视口底部到视口顶部之间时
      if (sectionTop <= windowHeight && sectionTop >= 0) {
        progress = 1 - sectionTop / windowHeight;
      } 
      // 当 section 顶部已经超过视口顶部时
      else if (sectionTop < 0) {
        const visiblePortion = Math.min(windowHeight, sectionHeight + sectionTop);
        if (visiblePortion > 0) {
          progress = Math.min(1, 1 + (-sectionTop) / sectionHeight);
        } else {
          progress = 1; // section 已完全滚过视口
        }
      }
      
      // 将进度值限制在 0-1 之间
      progress = Math.max(0, Math.min(1, progress));
      setSectionProgress(progress);
    };

    // 使用 requestAnimationFrame 来平滑处理滚动事件
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          calculateSectionProgress();
          ticking = false;
        });
        ticking = true;
      }
    };

    // 初始计算一次
    calculateSectionProgress();
    
    // 添加滚动事件监听
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 计算整个部分的视差效果
  const getSectionParallaxStyle = () => {
    // 根据整体部分的滚动进度计算视差效果
    return {
      titleTransform: `translateY(${-sectionProgress * 30}px)`,
      descriptionTransform: `translateY(${-sectionProgress * 20}px)`,
      searchBarTransform: `translateY(${-sectionProgress * 40}px)`,
      cardsContainerTransform: `translateY(${sectionProgress * 20}px)`,
    };
  };

  // 获取视差效果样式
  const parallaxStyle = getSectionParallaxStyle();

  // 设置主容器样式，用于创建固定高度的区域
  const sectionStyle = {
    height: `${sectionHeight}px`, // 固定高度
    position: 'relative' as const,
    overflow: 'hidden',
    zIndex: 1
  };

  // 计算固定显示区域的 z-index
  const getFixedZIndex = () => {
    // 根据滚动进度动态调整 z-index
    return sectionProgress >= 0.75 ? 5 : 30; // 靠近底部时降低 z-index
  };

  return (
    <section className="w-full bg-[#FAF9F5] dark:bg-gray-900" ref={sectionRef} style={sectionStyle}>
      {/* 固定显示区域 - 总是位于视口可见区域 */}
      <div className="sticky inset-x-0 top-0 overflow-hidden bg-[#FAF9F5] py-16 dark:bg-gray-900" style={{ 
        height: '100%',
        zIndex: getFixedZIndex(),
        transition: 'z-index 0.3s ease-out', // 平滑过渡
      }}>
        <div className="mx-auto max-w-[1440px] px-16">
          {/* Search Bar */}
          <div
            className={`mx-auto mb-6 max-w-[324px] transition-all duration-700 ease-out ${
              visibleElements.searchBar ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
            }`}
            style={{
              transform: parallaxStyle.searchBarTransform,
            }}
          >
            <div className="flex h-[40px] items-center overflow-hidden rounded-lg bg-[#E3DFCE]">
              <div className="flex h-[40px] w-[40px] flex-shrink-0 items-center justify-center bg-[#141413]">
                <div className="relative size-6">
                  <Image
                    src="/images/emoji.svg"
                    alt="Emoji"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
              <div className="flex-1 px-2">
                <span className="font-merriweather text-xs text-[#3D3D3A]">
                  {t('sections.coreFeatures.searchPlaceholder', 'Find guides, tutorials, and mining strategies...')}
                </span>
              </div>
            </div>
          </div>

          {/* Section Header */}
          <div className="mb-12 text-center">
            <h2
              className={`mb-4 font-styrene-b text-[40px] font-medium leading-[56px] text-[#141413] transition-all duration-700 ease-out dark:text-white ${
                visibleElements.title ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
              }`}
              style={{
                transform: parallaxStyle.titleTransform,
              }}
            >
              {t('sections.coreFeatures.title', 'Core Features That Set DogeMine Apart')}
            </h2>
            <div className="mx-auto max-w-[998px]">
              <p
                className={`font-merriweather text-base leading-6 text-[#3D3D3A] transition-all duration-700 ease-out dark:text-gray-300 ${
                  visibleElements.description ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
                }`}
                style={{
                  transform: parallaxStyle.descriptionTransform,
                }}
              >
                {t('sections.coreFeatures.description', 'DogeMine revolutionizes cryptocurrency mining with cutting-edge hybrid technology. Our platform combines traditional mining rewards with AI validation tasks, creating multiple income streams from a single setup while contributing to the future of decentralized AI.')}
              </p>
            </div>
          </div>

          {/* Feature Cards Display - 添加透视效果的容器 */}
          <div
            className={`mx-auto max-w-[1312px] px-16 transition-all duration-1000 ease-out ${
              visibleElements.cards ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'
            }`}
            style={{
              transform: parallaxStyle.cardsContainerTransform,
            }}
          >
            <div className="relative h-[200px] w-full overflow-hidden">
              {cards.map((card, index) => {
                const style = getCardStyle(index);

                return (
                  <div
                    key={card.id}
                    className="absolute inset-0 w-full"
                    style={{
                      transform: style.transform,
                      opacity: style.opacity,
                      zIndex: style.zIndex,
                      transition: style.transition,
                    }}
                  >
                    <div
                      className="h-[200px] w-full overflow-hidden rounded-3xl"
                      style={{ backgroundColor: card.backgroundColor }}
                    >
                      <div className="flex h-full">
                        {/* Image Section */}
                        <div className="relative h-full w-[232px] flex-shrink-0">
                          <Image
                            src={card.backgroundImage}
                            alt={`${card.title} Background`}
                            fill
                            className="object-cover"
                          />
                        </div>

                        {/* Content Section */}
                        <div className="flex flex-1 flex-col justify-center p-8">
                          <h3
                            className={`font-${card.titleFont === 'Styrene_B' ? 'styrene-b' : 'merriweather'} ${
                              card.titleFont === 'Styrene_B' ? 'font-medium' : 'font-normal'
                            } mb-4 text-2xl`}
                            style={{
                              color: card.titleColor,
                              lineHeight: '30px', // Standardized line height for all cards
                            }}
                          >
                            {card.title}
                          </h3>
                          <p className="font-merriweather text-base leading-6 text-[#141413] dark:text-gray-200">
                            {card.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 导航点 */}
            <div className="mt-8 flex justify-center gap-3">
              {cards.map((_, index) => (
                <button
                  key={index}
                  className={`size-3 rounded-full transition-all duration-300 ${
                    index === activeCardIndex 
                      ? 'scale-125 bg-[#141413]' 
                      : 'bg-[#E3DFCE] hover:bg-[#C0BDAC]'
                  }`}
                  onClick={() => handleDotClick(index)}
                  aria-label={`显示卡片 ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}