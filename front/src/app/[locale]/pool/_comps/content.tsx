'use client'

import { zodResolver } from '@hookform/resolvers/zod'
// import { Trans } from '@lingui/react/macro'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { ECharts } from '@/components/charts'
import { CopyButton } from '@/components/common/copy-button'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Icons,
  InputField,
  Segmented,
  SegmentedList,
  SegmentedTrigger,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  TextLink,
} from '@/components/ui'

const workerFormSchema = z.object({
  poolUrl1: z.string(),
  poolUrl2: z.string(),
  poolUrl3: z.string(),
})

const WorkerForm = () => {
  const form = useForm<z.infer<typeof workerFormSchema>>({
    resolver: zodResolver(workerFormSchema),
    defaultValues: {
      poolUrl1: 'stratum+tcp://ddvgg.io:3333',
      poolUrl2: 'stratum+tcp://btc.viabtc.io:25',
      poolUrl3: 'stratum+tcp://btc.viabtc.io:443',
    },
  })

  const onSubmit = (data: any) => {
    console.log(data)
  }

  return (
    <Card className="w-[500px] shrink-0">
      <CardHeader>
        <CardTitle>
          "Miner Configuration Example"
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Segmented defaultValue="recommend" className="mb-4">
          <SegmentedList>
            <SegmentedTrigger value="recommend" className="min-w-[110px]">
              "Recommended"
            </SegmentedTrigger>
          </SegmentedList>
        </Segmented>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-4">
              <FormField
                name="poolUrl1"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      "Pool 1 URL"
                    </FormLabel>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <InputField
                          value={field.value}
                          onChangeValue={field.onChange}
                          clean={false}
                          readOnly
                        />
                      </FormControl>
                      <CopyButton text={field.value} />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="poolUrl2"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      "Pool 2 URL"
                    </FormLabel>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <InputField
                          value={field.value}
                          onChangeValue={field.onChange}
                          clean={false}
                          readOnly
                        />
                      </FormControl>
                      <CopyButton text={field.value} />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="poolUrl3"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      "Pool 3 URL"
                    </FormLabel>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <InputField
                          value={field.value}
                          onChangeValue={field.onChange}
                          clean={false}
                          readOnly
                        />
                      </FormControl>
                      <CopyButton text={field.value} />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

const AddWorker = () => {
  const steps = [
    {
      title: 'Connect to LAN',
      descriptions: [
        <Trans key="1">Connect the computer to the miner&apos;s LAN;</Trans>,
        <Trans key="2">Obtain the miner&apos;s IP;</Trans>,
        <Trans key="3">
          Log in to the miner and backend in the computer browser.
        </Trans>,
      ],
    },
    {
      title: 'Configure Miner',
      descriptions: [
        <Trans key="1">
          Configure the mining address, i.e., the URL address shown in the
          example. Pool 3 URL can be filled with the backup pool&apos;s mining
          address.
        </Trans>,
        <Trans key="2">
          Configure Worker, Worker=AccountName.MinerName. The miner name must
          consist of numbers or lowercase letters, up to 64 characters.
        </Trans>,
        <Trans key="3">
          Configure password, can be left blank or entered arbitrarily.
        </Trans>,
      ],
    },
    {
      title: 'Check Hashrate',
      descriptions: [
        <Trans key="4">
          Save the configuration, and the miner will be automatically added to
          the miner management page in about 1 minute. The user panel and
          earnings record will also display hashrate and earnings data.
        </Trans>,
      ],
    },
  ]

  return (
    <Card>
      <CardContent className="py-0">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="item-1" className="border-none">
            <AccordionTrigger>
              <span className="group-data-[state=open]:hidden">
                <Trans>
                  You have not added a miner yet, expand to view the miner
                  configuration guide
                </Trans>
              </span>
              <span className="hidden group-data-[state=open]:block">
                "Add Miner"
              </span>
            </AccordionTrigger>
            <AccordionContent className="flex justify-between gap-10">
              <div className="flex flex-col gap-10">
                {steps.map((step, index) => (
                  <div className="flex flex-col gap-4" key={index}>
                    <div className="flex gap-4">
                      <h1 className="text-3xl font-bold">
                        {String(index + 1).padStart(2, '0')}
                      </h1>
                      <div>
                        <h3 className="text-base font-bold">{step.title}</h3>
                        <div className="flex flex-col gap-1 pt-4">
                          {step.descriptions.map((description, index) => (
                            <p className="text-xs" key={index}>
                              {description}
                            </p>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <WorkerForm />
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}

const Informations = () => {
  const option = {
    legend: {
      orient: 'vertical',
      right: '10%',
      top: 'middle',
      data: ['Active Miners', 'Offline Miners', 'Invalid Miners'],
    },
    series: [
      {
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        center: ['30%', '50%'],
        label: {
          show: true,
          position: 'center',
          formatter: '{c}\nTotal Miners',
          fontSize: 12,
          fontWeight: 'bold',
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 0, name: 'Active Miners', itemStyle: { color: '#ff9900' } },
          { value: 0, name: 'Offline Miners', itemStyle: { color: '#00cc94' } },
          { value: 0, name: 'Invalid Miners', itemStyle: { color: '#e0e0e0' } },
        ],
      },
    ],
    grid: {
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true,
    },
  }

  return (
    <div className="grid h-[280px] grid-cols-3 gap-4">
      <Card>
        <CardHeader className="border-b">
          <CardTitle>
            "Account Information"
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-1 flex-col items-center justify-between pt-6">
          <div className="flex flex-col items-center justify-between">
            <h3 className="pb-4 text-sm text-muted-foreground">
              "Earnings in the Last 24 Hours"
            </h3>
            <div className="flex items-end gap-2">
              <h1 className="text-4xl">0</h1>
              <div className="pb-1 text-sm font-bold">BTC</div>
            </div>
            <div className="text-sm">+ 0 FB</div>
          </div>
          <div className="mt-4 flex w-full justify-between px-6">
            <div className="flex flex-col text-xs">
              <span>0 BTC</span>
              <div className="mb-2 mt-1 h-1 w-[100px] bg-[linear-gradient(225deg,rgba(255,_201,_0,_0),_#00cc94)]" />
              <p>
                "Total Earnings"
              </p>
            </div>
            <div className="flex flex-col text-xs">
              <span>0 BTC</span>
              <div className="mb-2 mt-1 h-1 w-[100px] bg-[linear-gradient(225deg,rgba(255,_201,_0,_0),_#00cc94)]" />
              <p>
                "Account Balance"
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="border-b">
          <CardTitle>
            "Hashrate"
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-1 flex-col items-center justify-between  pt-6">
          <div className="flex flex-col items-center justify-between">
            <h3 className="pb-4 text-sm text-muted-foreground">
              "10-Minute Average Hashrate"
            </h3>
            <div className="flex items-end gap-2">
              <h1 className="text-4xl">0</h1>
            </div>
          </div>
          <div className="mt-4 flex w-full justify-between px-6">
            <div className="flex flex-col text-xs">
              <span>0</span>
              <div className="mb-2 mt-1 h-1 w-[100px] bg-[linear-gradient(225deg,rgba(255,_201,_0,_0),_#ff9900)]" />
              <p>
                "1-Hour Average Hashrate"
              </p>
            </div>
            <div className="flex flex-col text-xs">
              <span>0</span>
              <div className="mb-2 mt-1 h-1 w-[100px] bg-[linear-gradient(225deg,rgba(255,_201,_0,_0),_#ff9900)]" />
              <p>
                "1-Day Average Hashrate"
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="border-b">
          <CardTitle>
            "Miners"
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-1 flex-col items-center justify-center pt-6">
          <ECharts option={option} style={{ width: 360 }} />
        </CardContent>
      </Card>
    </div>
  )
}

const PowerCurve = () => {
  const [interval, setInterval] = useState<'min' | 'hour' | 'day'>('min')

  const generateRandomData = () =>
    Array.from({ length: 12 }, () => Math.floor(Math.random() * 100))

  const dataMap = {
    min: generateRandomData(),
    hour: generateRandomData(),
    day: generateRandomData(),
  }

  const rejectRateDataMap = {
    min: generateRandomData(),
    hour: generateRandomData(),
    day: generateRandomData(),
  }

  const getCurrentDayLabels = () => {
    const now = new Date()
    return Array.from({ length: 12 }, (_, i) => {
      const date = new Date(now)
      date.setDate(now.getDate() - i)
      return `${date.getMonth() + 1}/${date.getDate()}`
    }).reverse()
  }

  const xAxisData = {
    min: [
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
    ],
    hour: [
      '18:00',
      '19:00',
      '20:00',
      '21:00',
      '22:00',
      '23:00',
      '00:00',
      '01:00',
      '02:00',
      '03:00',
      '04:00',
      '05:00',
    ],
    day: getCurrentDayLabels(),
  }

  const option = {
    legend: {
      data: ['Hashrate', 'Rejection Rate'],
      bottom: 0,
    },
    xAxis: {
      type: 'category',
      data: xAxisData[interval],
    },
    yAxis: [
      {
        type: 'value',
        position: 'left',
      },
      {
        type: 'value',
        position: 'right',
      },
    ],
    series: [
      {
        name: 'Hashrate',
        data: dataMap[interval],
        type: 'line',
        smooth: true,
        yAxisIndex: 0,
      },
      {
        name: 'Rejection Rate',
        data: rejectRateDataMap[interval],
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
      },
    ],
    tooltip: {
      trigger: 'axis',
      formatter: (params: { axisValue: string; data: number }[]) => {
        const [power, reject] = params
        return `${power.axisValue}<br/>Hashrate: ${power.data}<br/>Rejection Rate: ${reject.data}%`
      },
    },
    grid: {
      top: '20px',
      left: '40px',
      right: '40px',
      bottom: '60px',
      containLabel: true,
    },
  }

  return (
    <Card>
      <CardHeader className="border-b">
        <CardTitle>
          "Hashrate Curve"
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-4 pt-6">
        <Segmented
          defaultValue="min"
          className="mb-4"
          // @ts-ignore
          onValueChange={setInterval}
        >
          <SegmentedList>
            <SegmentedTrigger value="min" className="min-w-[110px]">
              "Every 10 Minutes"
            </SegmentedTrigger>
            <SegmentedTrigger value="hour" className="min-w-[110px]">
              "Every Hour"
            </SegmentedTrigger>
            <SegmentedTrigger value="day" className="min-w-[110px]">
              "Every Day"
            </SegmentedTrigger>
          </SegmentedList>
        </Segmented>

        <ECharts option={option} style={{ height: 400, width: '100%' }} />
      </CardContent>
    </Card>
  )
}

const Support = () => {
  return (
    <Card>
      <CardHeader className="border-b">
        <CardTitle>
          "Technical Support"
        </CardTitle>
      </CardHeader>
      <CardContent className="py-4">
        <div className="flex gap-40">
          <div className="flex items-center gap-2">
            <div className="text-sm text-muted-foreground">
              "Email:"
            </div>
            <TextLink href="mailto:<EMAIL>" className="text-sm">
              <EMAIL>
            </TextLink>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-muted-foreground">
              "Telegram:"
            </div>
            <TextLink href="https://t.me/xxx" className="text-sm">
              https://t.me/xxx
            </TextLink>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const PoolSetting = () => {
  return (
    <Card>
      <CardContent className="flex items-center justify-between py-4">
        <Select defaultValue="btc">
          <SelectTrigger className="w-auto min-w-[200px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="btc">
                <div className="flex items-center gap-2">
                  <div className="size-6 rounded-full bg-muted" />
                  <span>BTC</span>
                </div>
              </SelectItem>
              <SelectItem value="eth">
                <div className="flex items-center gap-2">
                  <div className="size-6 rounded-full bg-muted" />
                  <span>ETH</span>
                </div>
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
        <Button size="sm" LeftIcon={<Icons.Settings size={16} />}>
          "Mining Settings"
        </Button>
      </CardContent>
    </Card>
  )
}

export const PoolContent = () => {
  return (
    <div className="flex flex-col gap-4 p-6">
      <PoolSetting />
      <AddWorker />
      <Informations />
      <PowerCurve />
      <Support />
    </div>
  )
}
