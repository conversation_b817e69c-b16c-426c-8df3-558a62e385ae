export enum Locale {
  en = 'en',
  zh_CN = 'zh-CN',
  zh_TW = 'zh-TW',
}

export const DEFAULT_LOCALE = Locale.en

export const LOCALE_MESSAGE_MODULE_NAMES = [
  'setting',
  'login',
  'components',
  'root',
  'assets',
  'workers',
  'setting',
  'earnings',
]

export const LOCALE_LABEL_MAP: Record<Locale, string> = {
  [Locale.en]: 'English',
  [Locale.zh_CN]: '简体中文',
  [Locale.zh_TW]: '繁體中文',
}

export type LocaleKey = object

export const LOCALE_KEY_MAP: Record<Locale, LocaleKey> = {
  [Locale.en]: {},
  [Locale.zh_CN]: {},
  [Locale.zh_TW]: {},
}

export const LOCALES = Object.keys(LOCALE_LABEL_MAP) as Locale[]
