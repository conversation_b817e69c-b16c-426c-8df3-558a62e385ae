import { useState } from 'react'

import type { BittensorAccount } from '@/atoms'
import { api, ApiError } from '@/lib/api'

interface WalletAuthResult {
  token: string
  user: {
    id: string
    address: string
    name?: string
    email?: string
    created_at: string
    updated_at: string
  }
  expires_in: number
}

export function useWalletAuth() {
  const [isAuthenticating, setIsAuthenticating] = useState(false)
  const [authError, setAuthError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string | null>(null)

  const authenticateWallet = async (account: BittensorAccount, existingSigner?: any): Promise<WalletAuthResult> => {
    setIsAuthenticating(true)
    setAuthError(null)
    setDebugInfo(null)

    try {
      // 1. 尝试从后端获取签名用的随机数
      let serverNonce: string | null = null
      
      try {
        console.log('🎲 Getting nonce from server for address:', account.address)
        const nonceResponse = await api.auth.getNonce({ address: account.address })
        
        console.log('📡 Nonce API response:', nonceResponse)
        
        // 检查两种可能的响应格式
        let nonceFromResponse: string | undefined
        if (nonceResponse.success) {
          // 格式 1: 数据在 data 字段中 (包装格式)
          if (nonceResponse.data?.nonce) {
            nonceFromResponse = nonceResponse.data.nonce
          }
          // 格式 2: 数据直接在响应中 (ApiResponse 格式)
          else if ((nonceResponse as any).nonce) {
            nonceFromResponse = (nonceResponse as any).nonce
          }
        }
        
        if (nonceFromResponse) {
          serverNonce = nonceFromResponse
          console.log('✅ Received complete message from server:', {
            nonce: serverNonce,
            length: serverNonce.length,
            preview: `${serverNonce.substring(0, 100)  }...`,
            source: nonceResponse.data?.nonce ? 'data.nonce' : 'direct.nonce'
          })
        } else {
          console.warn('⚠️ Nonce API returned unsuccessful response:', {
            success: nonceResponse.success,
            data: nonceResponse.data,
            directNonce: (nonceResponse as any).nonce,
            error: nonceResponse.error || 'No error message',
            code: (nonceResponse as any).code
          })
        }
      } catch (nonceError) {
        console.warn('⚠️ Failed to get nonce from server, will use fallback method:', nonceError)
        
        // 如果 nonce API 不存在或有其他错误，记录详细信息
        if (nonceError instanceof Error) {
          console.warn('Nonce error details:', {
            message: nonceError.message,
            name: nonceError.name,
            stack: nonceError.stack
          })
        }
      }

      // 2. 生成要签名的消息
      let message: string
      
      if (serverNonce) {
        // 服务器返回的 nonce 实际上是完整的待签名消息
        message = serverNonce
        console.log('🔐 Using server-provided message for signing:', message)
      } else {
        // 回退到客户端生成的消息格式
        const timestamp = Date.now()
        const clientNonce = Math.random().toString(36).substring(2, 15)
        message = `DogeMine Login Request
Timestamp: ${timestamp}
Nonce: ${clientNonce}
Address: ${account.address}
Domain: ${window.location.hostname}`
        console.log('🔄 Using client-generated message as fallback:', message)
      }
      
      // 3. 获取钱包签名器 - 优先使用已存在的签名器
      let signer
      if (existingSigner && existingSigner.signer) {
        console.log('🔗 Using existing signer connection')
        signer = existingSigner.signer
      } else {
        console.log('🔌 Creating new signer connection')
        signer = await getWalletSigner()
      }
      
      // 4. 对消息进行签名 - 这里会弹出密码输入框
      console.log('✍️ Requesting signature from wallet - user will be prompted for password...')
      const signResult = await signer.signRaw({
        address: account.address,
        data: message,
        type: 'bytes'
      })

      console.log('✅ Signature received:', signResult)

      // 5. 构建认证请求
      const authRequest = {
        address: account.address,
        signature: signResult.signature,
        message,
        source: 'bittensor-wallet'
      }

      console.log('🚀 Sending authentication request to backend:', {
        address: authRequest.address,
        signature: authRequest.signature,
        messagePreview: `${authRequest.message.substring(0, 100)  }...`,
        messageLength: authRequest.message.length,
        source: authRequest.source,
        fullMessage: authRequest.message  // 完整消息用于调试
      })

      // 6. 发送到后端验证
      const response = await api.auth.walletAuth(authRequest)

      if (!response.success) {
        console.error('❌ Authentication failed. Request vs Response:', {
          request: {
            address: authRequest.address,
            signature: authRequest.signature,
            messageLength: authRequest.message.length,
            messagePreview: `${authRequest.message.substring(0, 100)  }...`,
            source: authRequest.source
          },
          response: {
            success: response.success,
            message: response.message,
            error: response.error,
            token: (response as any).token
          }
        })
        throw new Error(response.message || 'Authentication failed')
      }

      console.log('🎉 Wallet authentication successful:', response)

      // 7. 处理响应数据 - 后端直接返回 token 而不是在 data 字段中
      let authData: WalletAuthResult
      if (response.data) {
        // 标准格式：数据在 data 字段中
        authData = response.data
      } else if ((response as any).token) {
        // 后端直接返回格式：token 直接在响应中
        authData = {
          token: (response as any).token,
          user: (response as any).user || {
            id: '',
            address: account.address,
            name: account.name,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          expires_in: (response as any).expires_in || 3600
        }
      } else {
        throw new Error('Invalid response format')
      }

      // 8. 保存认证 token
      if (authData.token) {
        // 使用 API 封装的方法设置 token
        const { setAuthToken } = await import('@/lib/api')
        setAuthToken(authData.token)
        console.log('💾 Authentication token saved')
      }

      return authData
    } catch (error) {
      console.error('❌ Wallet authentication failed:', error)
      
      let errorMessage = 'Authentication failed'
      
      if (error instanceof ApiError) {
        switch (error.status) {
          case 400: {
            errorMessage = 'Invalid signature or request format'
            break
          }
          case 401: {
            errorMessage = 'Signature verification failed'
            break
          }
          case 403: {
            errorMessage = 'Account not authorized'
            break
          }
          case 429: {
            errorMessage = 'Too many authentication attempts. Please try again later.'
            break
          }
          case 500: {
            errorMessage = 'Server error. Please try again.'
            break
          }
          default: {
            errorMessage = error.message || 'Authentication failed'
          }
        }
      } else if (error instanceof Error) {
        if (error.message.includes('User rejected')) {
          errorMessage = 'Signature was cancelled by user'
        } else if (error.message.includes('disconnected port')) {
          errorMessage = 'Wallet connection lost. Please refresh and try again.'
        } else {
          errorMessage = error.message
        }
      }
      
      setAuthError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setIsAuthenticating(false)
    }
  }

  const getWalletSigner = async () => {
    // 获取钱包扩展
    const walletExtension = 
      window.injectedWeb3?.['bittensor-wallet'] ||
      window.injectedWeb3?.['bittensor'] ||
      window.bittensorWallet ||
      Object.entries(window.injectedWeb3 || {})
        .find(([key]) => key.toLowerCase().includes('bittensor'))?.[1]

    if (!walletExtension) {
      throw new Error('Bittensor wallet extension not found')
    }

    console.log('🔌 Using existing wallet connection (no password prompt)...')
    // 使用相同的应用名称连接，钱包应该记住之前的授权
    const signerWrapper = await walletExtension.enable('DogeMine')
    
    if (!signerWrapper.signer) {
      throw new Error('Wallet signer not available')
    }

    return signerWrapper.signer
  }

  const logout = async () => {
    try {
      // 调用后端登出接口
      const response = await api.auth.logout()
      if (!response.success && response.code === 404) {
        console.log('Backend logout endpoint not implemented (404)')
      } else if (!response.success) {
        console.warn('Backend logout failed:', response.error)
      }
    } catch (error) {
      // 处理其他类型的错误（网络错误等）
      console.warn('Backend logout failed:', error)
    } finally {
      // 无论后端是否成功，都清除本地 token
      const { clearAuthToken } = await import('@/lib/api')
      clearAuthToken()
      console.log('🚪 User logged out')
    }
  }

  const verifyAuth = async () => {
    try {
      const response = await api.auth.getUserInfo()
      
      if (response.success && response.data) {
        return response.data
      } else if (!response.success && (response as any).code === 404) {
        // 接口不存在，假设认证有效（基于 token 存在）
        console.log('getUserInfo endpoint not implemented (404), assuming auth is valid based on token')
        return typeof window !== 'undefined' && localStorage.getItem('auth-token') !== null
      } else if (!response.success && (response as any).code === 401) {
        // Token 过期或无效，清除本地 token
        const { clearAuthToken } = await import('@/lib/api')
        clearAuthToken()
        return false
      }
      
      return false
    } catch (error) {
      if (error instanceof ApiError && error.status === 401) {
          // Token 过期或无效，清除本地 token
          const { clearAuthToken } = await import('@/lib/api')
          clearAuthToken()
        }
      return false
    }
  }

  // 测试 API 连接状态
  const testApiConnection = async (address: string) => {
    try {
      setDebugInfo('Testing API connection...')
      console.log('🔍 Testing API connection for nonce endpoint')
      
      const response = await api.auth.getNonce({ address })
      
      // 检查两种可能的响应格式
      const hasNonceInData = !!response.data?.nonce
      const hasNonceDirect = !!(response as any).nonce
      const nonceContent = response.data?.nonce || (response as any).nonce
      
      const debugResult = {
        success: response.success,
        hasData: !!response.data,
        hasNonceInData,
        hasNonceDirect,
        nonceSource: hasNonceInData ? 'data.nonce' : hasNonceDirect ? 'direct.nonce' : 'none',
        noncePreview: nonceContent ? `${nonceContent.substring(0, 100)  }...` : 'No nonce found',
        responseKeys: response.data ? Object.keys(response.data) : [],
        directKeys: Object.keys(response).filter(key => !['success', 'data', 'message', 'error'].includes(key)),
        errorInfo: response.error || 'No error',
        statusCode: (response as any).code || 'No status code'
      }
      
      console.log('🔍 API connection test result:', debugResult)
      setDebugInfo(`API Test: ${JSON.stringify(debugResult, null, 2)}`)
      
      return debugResult
    } catch (error) {
      console.error('🔍 API connection test failed:', error)
      const errorInfo = {
        error: error instanceof Error ? error.message : 'Unknown error',
        type: error instanceof Error ? error.constructor.name : 'Unknown',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      }
      setDebugInfo(`API Test Failed: ${JSON.stringify(errorInfo, null, 2)}`)
      return errorInfo
    }
  }

  return {
    authenticateWallet,
    logout,
    verifyAuth,
    testApiConnection,
    isAuthenticating,
    authError,
    debugInfo,
    clearError: () => {
      setAuthError(null)
      setDebugInfo(null)
    }
  }
}