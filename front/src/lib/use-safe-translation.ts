import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

// 防止 Hydration 错误的安全翻译 Hook
export function useSafeTranslation() {
  const { t, i18n } = useTranslation()
  const [isClient, setIsClient] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsClient(true)
    
    // 等待 i18n 语言加载完成
    const checkLoaded = () => {
      if (i18n.isInitialized && i18n.hasLoadedNamespace('translation')) {
        setIsLoaded(true)
      } else {
        // 如果还没有加载完成，监听 loaded 事件
        const onLoaded = () => {
          setIsLoaded(true)
          i18n.off('loaded', onLoaded)
        }
        i18n.on('loaded', onLoaded)
        
        // 设置超时 fallback，避免无限等待
        setTimeout(() => {
          setIsLoaded(true)
          i18n.off('loaded', onLoaded)
        }, 2000)
      }
    }
    
    checkLoaded()
  }, [i18n])

  // 服务端渲染时返回 fallback，客户端等待加载完成后返回翻译
  const safeT = (key: string, fallback?: string) => {
    if (!isClient || !isLoaded) {
      return fallback || key
    }
    return t(key)
  }

  return { t: safeT, i18n, isClient: isClient && isLoaded }
} 