import i18n from 'i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import Backend from 'i18next-http-backend'
import { initReactI18next } from 'react-i18next'

import { DEFAULT_LOCALE } from '@/constants'

// 只在客户端初始化 i18next
if (typeof window !== 'undefined') {
  i18n
    .use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      lng: DEFAULT_LOCALE,
      fallbackLng: DEFAULT_LOCALE,
      debug: process.env.NODE_ENV === 'development',
      
      backend: {
        loadPath: '/locales/{{lng}}.json',
      },
      
      interpolation: {
        escapeValue: false, // not needed for react as it escapes by default
      },
      
      detection: {
        order: ['path', 'localStorage', 'navigator'],
        caches: ['localStorage'],
      },
      
      react: {
        useSuspense: false, // 避免服务端渲染问题
      },
    })
} else {
  // 服务端简单初始化
  i18n
    .use(initReactI18next)
    .init({
      lng: DEFAULT_LOCALE,
      fallbackLng: DEFAULT_LOCALE,
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
      // 服务端直接返回 key，避免 hydration 不匹配
      returnObjects: false,
      returnNull: false,
      returnEmptyString: false,
    })
}

 
export {default} from 'i18next'