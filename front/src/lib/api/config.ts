// API 配置文件
export const API_CONFIG = {
  // 开发环境
  development: {
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://18.139.113.94:8000', // 开发环境 API 地址
    timeout: 10000,
  },
  // 生产环境
  production: {
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.dogemine.com', // 生产环境 API 地址
    timeout: 15000,
  },
  // 测试环境（可选）
  test: {
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://test-api.dogemine.com',
    timeout: 10000,
  }
} as const

// 获取当前环境配置
export function getApiConfig() {
  const env = process.env.NODE_ENV || 'development'
  return API_CONFIG[env as keyof typeof API_CONFIG] || API_CONFIG.development
}

// API 响应的通用格式
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

// 请求配置接口
export interface RequestConfig {
  headers?: Record<string, string>
  timeout?: number
  withAuth?: boolean
  signal?: AbortSignal
}

// 错误类型
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public response?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}
