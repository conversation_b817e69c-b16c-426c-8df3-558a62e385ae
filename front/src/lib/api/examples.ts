/**
 * 网络请求封装使用示例
 * 
 * 本文件展示了如何在项目中使用网络请求封装
 * 请根据实际需求调整 API 接口
 */

import type { BittensorAccount } from '@/atoms'

import { api, ApiError,httpClient } from './index'

// ==================== 基础使用示例 ====================

// 1. 基础请求示例
export async function basicRequestExample() {
  try {
    // GET 请求
    const response = await httpClient.get('/users')
    console.log('用户列表：', response.data)

    // POST 请求
    const createResponse = await httpClient.post('/users', {
      name: '<PERSON>',
      email: '<EMAIL>'
    })
    console.log('创建用户：', createResponse.data)

    // 带自定义配置的请求
    const customResponse = await httpClient.get('/protected-data', {
      headers: { 'X-Custom-Header': 'value' },
      timeout: 5000,
      withAuth: true // 自动添加 Authorization 头
    })
    console.log('受保护的数据：', customResponse.data)

  } catch (error) {
    if (error instanceof ApiError) {
      console.error('API 错误：', error.message, '状态码：', error.status)
    } else {
      console.error('未知错误：', error)
    }
  }
}

// ==================== 钱包认证示例 ====================

export async function walletAuthExample(account: BittensorAccount) {
  try {
    // 1. 生成签名消息
    const timestamp = Date.now()
    const message = `DogeMine Login Request\nTimestamp: ${timestamp}\nAddress: ${account.address}`
    
    // 2. 这里需要实际的钱包签名逻辑
    // const signature = await signMessage(message, account)
    const signature = 'mock_signature' // 示例用
    
    // 3. 调用认证 API
    const authResponse = await api.auth.walletAuth({
      address: account.address,
      signature,
      message,
      source: 'bittensor-wallet'
    })

    // 4. 保存 token
    if (authResponse.success && authResponse.data) {
      httpClient.setAuthToken(authResponse.data.token)
      console.log('认证成功：', authResponse.data.user)
      return authResponse.data
    }

  } catch (error) {
    console.error('钱包认证失败：', error)
    throw error
  }
}

// ==================== 矿工管理示例 ====================

export async function minerManagementExample() {
  try {
    // 获取矿工列表
    const minersResponse = await api.miner.getMiners({
      page: 1,
      limit: 10,
      status: 'active'
    })
    
    if (minersResponse.success && minersResponse.data) {
      console.log('矿工列表：', minersResponse.data.miners)
      console.log('总数：', minersResponse.data.total)
    }

    // 获取特定矿工详情
    const minerDetailResponse = await api.miner.getMinerDetail('miner_id_123')
    
    if (minerDetailResponse.success && minerDetailResponse.data) {
      console.log('矿工详情：', minerDetailResponse.data)
    }

    // 获取矿工收益
    const earningsResponse = await api.miner.getMinerEarnings('miner_id_123', {
      start_date: '2024-01-01',
      end_date: '2024-01-31'
    })
    
    if (earningsResponse.success && earningsResponse.data) {
      console.log('矿工收益：', earningsResponse.data.earnings)
    }

  } catch (error) {
    console.error('矿工管理操作失败：', error)
  }
}

// ==================== 验证者管理示例 ====================

export async function validatorManagementExample() {
  try {
    // 获取验证者列表
    const validatorsResponse = await api.validator.getValidators()
    
    if (validatorsResponse.success && validatorsResponse.data) {
      console.log('验证者列表：', validatorsResponse.data.validators)
    }

    // 获取奖励统计
    const rewardsResponse = await api.validator.getValidatorRewards()
    
    if (rewardsResponse.success && rewardsResponse.data) {
      console.log('昨日奖励：', rewardsResponse.data.yesterday)
      console.log('总奖励：', rewardsResponse.data.total)
    }

  } catch (error) {
    console.error('验证者管理操作失败：', error)
  }
}

// ==================== 错误处理示例 ====================

export async function errorHandlingExample() {
  try {
    const response = await api.auth.getUserInfo()
    console.log('用户信息：', response.data)

  } catch (error) {
    if (error instanceof ApiError) {
      // 根据错误状态码处理
      switch (error.status) {
        case 401: {
          console.log('认证失败，需要重新登录')
          // 清除 token 并跳转到登录页
          httpClient.clearAuthToken()
          window.location.href = '/login'
          break
        }
          
        case 403: {
          console.log('权限不足')
          break
        }
          
        case 404: {
          console.log('资源不存在')
          break
        }
          
        case 500: {
          console.log('服务器错误')
          break
        }
          
        default: {
          console.log('请求失败：', error.message)
        }
      }
    } else {
      console.error('未知错误：', error)
    }
  }
}

// ==================== 文件上传示例 ====================

export async function fileUploadExample(file: File) {
  try {
    const uploadResponse = await httpClient.upload('/upload', file, {
      headers: {
        'X-Upload-Type': 'avatar'
      }
    })
    
    if (uploadResponse.success) {
      console.log('文件上传成功：', uploadResponse.data)
      return uploadResponse.data
    }

  } catch (error) {
    console.error('文件上传失败：', error)
    throw error
  }
}

// ==================== React Hook 使用示例 ====================

export function useApiExample() {
  // 在 React 组件中使用的示例
  const fetchUserData = async () => {
    try {
      const response = await api.auth.getUserInfo()
      if (response.success) {
        // 更新 state
        console.log('获取用户数据成功：', response.data)
      }
    } catch (error) {
      console.error('获取用户数据失败：', error)
    }
  }

  const handleWalletAuth = async (account: BittensorAccount, signature: string, message: string) => {
    try {
      const response = await api.auth.walletAuth({
        address: account.address,
        signature,
        message,
        source: 'bittensor-wallet'
      })
      
      if (response.success && response.data) {
        // 保存 token
        httpClient.setAuthToken(response.data.token)
        console.log('钱包认证成功')
        return response.data
      }
    } catch (error) {
      console.error('钱包认证失败：', error)
      throw error
    }
  }

  return {
    fetchUserData,
    handleWalletAuth
  }
}