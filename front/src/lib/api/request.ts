import type { ApiResponse, RequestConfig} from './config';
import { ApiError,getApiConfig } from './config'

class HttpClient {
  private baseURL: string
  private timeout: number
  private defaultHeaders: Record<string, string>

  constructor() {
    const config = getApiConfig()
    this.baseURL = config.baseURL
    this.timeout = config.timeout
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  // 获取认证 token
  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('auth-token')
  }

  // 构建完整 URL
  private buildURL(endpoint: string): string {
    // 如果 endpoint 已经是完整 URL，直接返回
    if (endpoint.startsWith('http://') || endpoint.startsWith('https://')) {
      return endpoint
    }
    
    // 确保 endpoint 以/开头
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
    return `${this.baseURL}${normalizedEndpoint}`
  }

  // 构建请求头
  private buildHeaders(config?: RequestConfig): Record<string, string> {
    const headers = { ...this.defaultHeaders }

    // 添加自定义头
    if (config?.headers) {
      Object.assign(headers, config.headers)
    }

    // 添加认证头
    if (config?.withAuth !== false) {
      const token = this.getAuthToken()
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
    }

    return headers
  }

  // 处理响应
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    let responseData: any

    try {
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json()
      } else {
        responseData = await response.text()
      }
    } catch {
      responseData = null
    }

    // 请求成功
    if (response.ok) {
      // 如果响应数据已经符合 ApiResponse 格式，直接返回
      if (responseData && typeof responseData === 'object' && 'success' in responseData) {
        return responseData as ApiResponse<T>
      }
      
      // 否则包装成标准格式
      return {
        success: true,
        data: responseData,
        message: 'Request successful'
      }
    }

    // 请求失败
    const errorMessage = responseData?.message || responseData?.error || `HTTP Error: ${response.status}`
    
    // 对于某些可选的认证接口，404 错误返回默认响应而不是抛出异常
    if (response.status === 404) {
      const url = response.url || ''
      const optionalAuthEndpoints = ['/auth/logout', '/auth/user', '/auth/refresh']
      
      if (optionalAuthEndpoints.some(endpoint => url.includes(endpoint))) {
        console.log(`⚠️ Optional auth endpoint not found (404): ${url}`)
        return {
          success: false,
          error: errorMessage,
          code: response.status
        } as ApiResponse<T>
      }
    }
    
    throw new ApiError(
      errorMessage,
      response.status,
      responseData?.code,
      responseData
    )
  }

  // 通用请求方法
  private async request<T>(
    method: string,
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    const url = this.buildURL(endpoint)
    const headers = this.buildHeaders(config)
    const timeout = config?.timeout || this.timeout

    // 创建 AbortController 用于超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    // 如果传入了 signal，也要监听
    if (config?.signal) {
      config.signal.addEventListener('abort', () => controller.abort())
    }

    try {
      const requestOptions: RequestInit = {
        method,
        headers,
        signal: controller.signal,
      }

      // 添加请求体（GET 和 DELETE 通常不需要 body）
      if (data && !['GET', 'DELETE'].includes(method.toUpperCase())) {
        if (data instanceof FormData) {
          // FormData 情况下，移除 Content-Type 让浏览器自动设置
          delete headers['Content-Type']
          requestOptions.body = data
        } else {
          requestOptions.body = JSON.stringify(data)
        }
      }

      console.log(`🌐 API Request: ${method} ${url}`, {
        headers,
        data: data && !(data instanceof FormData) ? data : '[FormData]'
      })

      const response = await fetch(url, requestOptions)
      
      clearTimeout(timeoutId)
      
      const result = await this.handleResponse<T>(response)
      
      console.log(`✅ API Response: ${method} ${url}`, result)
      
      return result
    } catch (error) {
      clearTimeout(timeoutId)
      
      console.error(`❌ API Error: ${method} ${url}`, error)
      
      if (error instanceof ApiError) {
        throw error
      }
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new ApiError('Request timeout', 408, 'TIMEOUT')
        }
        throw new ApiError(error.message, 0, 'NETWORK_ERROR')
      }
      
      throw new ApiError('Unknown error occurred', 0, 'UNKNOWN_ERROR')
    }
  }

  // GET 请求
  async get<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('GET', endpoint, undefined, config)
  }

  // POST 请求
  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('POST', endpoint, data, config)
  }

  // PUT 请求
  async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', endpoint, data, config)
  }

  // PATCH 请求
  async patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('PATCH', endpoint, data, config)
  }

  // DELETE 请求
  async delete<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', endpoint, undefined, config)
  }

  // 上传文件
  async upload<T>(endpoint: string, file: File | FormData, config?: RequestConfig): Promise<ApiResponse<T>> {
    const formData = file instanceof FormData ? file : new FormData()
    if (file instanceof File) {
      formData.append('file', file)
    }
    
    return this.request<T>('POST', endpoint, formData, config)
  }

  // 设置认证 token
  setAuthToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth-token', token)
    }
  }

  // 清除认证 token
  clearAuthToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth-token')
    }
  }

  // 获取当前 baseURL（用于调试）
  getBaseURL(): string {
    return this.baseURL
  }
}

// 创建单例实例
export const httpClient = new HttpClient()

// 导出便捷方法
export const { get, post, put, patch, delete: del, upload, setAuthToken, clearAuthToken } = httpClient