
import type { ApiResponse } from './config'
import { httpClient } from './request'

// 用户相关接口
export interface User {
  id: string
  address: string
  name?: string
  email?: string
  created_at: string
  updated_at: string
}

// 钱包认证请求
export interface WalletAuthRequest {
  address: string
  signature: string
  message: string
  source: string
}

// 钱包认证响应
export interface WalletAuthResponse {
  token: string
  user: User
  expires_in: number
}

// Nonce 请求接口
export interface NonceRequest {
  address: string
}

// Nonce 响应接口
export interface NonceResponse {
  nonce: string  // 完整的待签名消息
  message?: string  // 可选的提示信息
}

// Miner 数据接口
export interface Miner {
  id: string
  status: string
  hashrate1h: string
  hashrate24h: string
  shares1h: string
  shares24h: string
  node: string
  stake: string
  score: string
}

// Validator 数据接口
export interface Validator {
  id: string
  status: string
  hashrate1h: string
  hashrate24h: string
  shares1h: string
  shares24h: string
  score: string
}

// Earnings 数据接口
export interface Earning {
  time: string
  worker: string
  earning: string
  walletAddress: string
  hash: string
}

// ==================== 认证相关 API ====================

export const authApi = {
  // 获取签名用的随机数
  getNonce: (data: NonceRequest): Promise<ApiResponse<NonceResponse>> =>
    httpClient.post('/api/v1/accounts/nonce', data, { withAuth: false }),

  // 钱包认证登录
  walletAuth: (data: WalletAuthRequest): Promise<ApiResponse<WalletAuthResponse>> =>
    httpClient.post('/api/v1/accounts/auth', data, { withAuth: false }),

  // 刷新 token
  refreshToken: (): Promise<ApiResponse<{ token: string; expires_in: number }>> =>
    httpClient.post('/auth/refresh'),

  // 登出
  logout: (): Promise<ApiResponse<void>> =>
    httpClient.post('/api/v1/accounts/disconnect'),

  // 获取用户信息
  getUserInfo: (): Promise<ApiResponse<User>> =>
    httpClient.get('/auth/user'),
}

// ==================== 矿工相关 API ====================

export const minerApi = {
  // 获取矿工列表
  getMiners: (params?: { page?: number; limit?: number; status?: string }): Promise<ApiResponse<{ miners: Miner[]; total: number }>> =>
    httpClient.get('/miners', { headers: { 'X-Query-Params': JSON.stringify(params || {}) } }),

  // 获取矿工详情
  getMinerDetail: (id: string): Promise<ApiResponse<Miner>> =>
    httpClient.get(`/miners/${id}`),

  // 获取矿工收益
  getMinerEarnings: (id: string, params?: { start_date?: string; end_date?: string }): Promise<ApiResponse<{ earnings: Earning[]; total: number }>> =>
    httpClient.get(`/miners/${id}/earnings`, { headers: { 'X-Query-Params': JSON.stringify(params || {}) } }),

  // 更新矿工节点
  updateMinerNode: (id: string, data: { node_id: string }): Promise<ApiResponse<Miner>> =>
    httpClient.put(`/miners/${id}/node`, data),
}

// ==================== 验证者相关 API ====================

export const validatorApi = {
  // 获取验证者列表
  getValidators: (params?: { page?: number; limit?: number; status?: string }): Promise<ApiResponse<{ validators: Validator[]; total: number }>> =>
    httpClient.get('/validators', { headers: { 'X-Query-Params': JSON.stringify(params || {}) } }),

  // 获取验证者详情
  getValidatorDetail: (id: string): Promise<ApiResponse<Validator>> =>
    httpClient.get(`/validators/${id}`),

  // 获取验证者收益
  getValidatorEarnings: (id: string, params?: { start_date?: string; end_date?: string; type?: string }): Promise<ApiResponse<{ earnings: Earning[]; total: number }>> =>
    httpClient.get(`/validators/${id}/earnings`, { headers: { 'X-Query-Params': JSON.stringify(params || {}) } }),

  // 获取验证者奖励统计
  getValidatorRewards: (): Promise<ApiResponse<{
    yesterday: { xxx: number; ltc: number; doge: number; total: number }
    total: { xxx: number; ltc: number; doge: number; total: number }
  }>> =>
    httpClient.get('/validators/rewards'),

  // 更新验证者节点
  updateValidatorNode: (id: string, data: { node_id: string }): Promise<ApiResponse<Validator>> =>
    httpClient.put(`/validators/${id}/node`, data),
}

// 仪表盘数据接口
export interface DashboardOverview {
  hashrate_24h: string
  shares_24h: string
  hashrate_1h: string
  shares_1h: string
  total_staked: string
  tao_earnings: string
  doge_earnings: string
  ltc_earnings: string
  active_miners: number
}

// 账户接口
export interface Account {
  name: string
  source: string
  address: string
  selected: boolean
}

export interface AccountsResponse {
  accounts: Account[]
}

// ==================== 仪表板相关 API ====================

export const dashboardApi = {
  // 获取概览数据
  getOverview: (): Promise<ApiResponse<DashboardOverview>> =>
    httpClient.get('/api/v1/dashboard'),

  // 获取图表数据
  getChartData: (type: 'hashrate' | 'earnings' | 'rewards', range?: string): Promise<ApiResponse<{
    range: string
    unit: string
    data: Array<{
      timestamp: number
      hashrate: number
    }>
  }>> =>
    httpClient.get(`/api/v1/dashboard/hashrate-history?range=${range || '24h'}`),

  // 获取账户列表
  getAccounts: (): Promise<ApiResponse<AccountsResponse>> =>
    httpClient.get('/api/v1/dashboard/accounts'),
}

// ==================== 钱包相关 API ====================

export const walletApi = {
  // 获取钱包列表
  getWallets: (): Promise<ApiResponse<Array<{ address: string; type: string; name?: string }>>> =>
    httpClient.get('/wallets'),

  // 添加钱包
  addWallet: (data: { address: string; type: string; name?: string }): Promise<ApiResponse<void>> =>
    httpClient.post('/wallets', data),

  // 删除钱包
  removeWallet: (address: string): Promise<ApiResponse<void>> =>
    httpClient.delete(`/wallets/${address}`),

  // 更新钱包
  updateWallet: (address: string, data: { name?: string }): Promise<ApiResponse<void>> =>
    httpClient.put(`/wallets/${address}`, data),
}

// ==================== 通知相关 API ====================

export const notificationApi = {
  // 获取通知设置
  getSettings: (): Promise<ApiResponse<{
    email: string
    emailEnabled: boolean
    webhookUrl?: string
    webhookEnabled: boolean
  }>> =>
    httpClient.get('/notifications/settings'),

  // 更新通知设置
  updateSettings: (data: {
    email?: string
    emailEnabled?: boolean
    webhookUrl?: string
    webhookEnabled?: boolean
  }): Promise<ApiResponse<void>> =>
    httpClient.put('/notifications/settings', data),

  // 测试通知
  testNotification: (type: 'email' | 'webhook'): Promise<ApiResponse<void>> =>
    httpClient.post(`/notifications/test/${type}`),
}

// 导出所有 API
export const api = {
  auth: authApi,
  miner: minerApi,
  validator: validatorApi,
  dashboard: dashboardApi,
  wallet: walletApi,
  notification: notificationApi,
}
