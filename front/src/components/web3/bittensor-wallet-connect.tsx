'use client'

import { use<PERSON>tom } from 'jotai'
import { useEffect,useState } from 'react'

import type {BittensorAccount} from '@/atoms';
import { bittensorAccountAtom } from '@/atoms'
import { useWalletAuth } from '@/lib/hooks/use-wallet-auth'
import { cn } from '@/lib/utils/cn'

import { BittensorWalletDropdown } from './bittensor-wallet-dropdown'

// Bittensor 钱包扩展接口
interface BittensorExtension {
  name: string
  version: string
  enable: (appName: string) => Promise<BittensorSigner>
}

// Bittensor 签名器接口
interface BittensorSigner {
  accounts: {
    get: () => Promise<BittensorAccount[]>
  }
  signer: {
    signPayload: (payload: any) => Promise<any>
    signRaw: (raw: any) => Promise<any>
  }
}

// 声明全局 window 对象扩展
declare global {
  interface Window {
    // 常见的 Polkadot/Substrate 钱包扩展格式
    injectedWeb3?: {
      'bittensor-wallet'?: BittensorExtension
      'bittensor'?: BittensorExtension
      'polkadot-js'?: BittensorExtension
      'talisman'?: BittensorExtension
      'subwallet-js'?: BittensorExtension
      [key: string]: BittensorExtension | undefined
    }
    // 直接的钱包对象
    bittensorWallet?: any
  }
}

interface BittensorWalletConnectProps {
  isCollapsed?: boolean
  className?: string
  isMobile?: boolean
  onConnect?: (account: BittensorAccount) => void
  onDisconnect?: () => void
  initialAccount?: BittensorAccount | null
  variant?: 'default' | 'landing' // 新增变体 props
}



export function BittensorWalletConnect({
  isCollapsed = false,
  className,
  isMobile = false,
  onConnect,
  onDisconnect,
  initialAccount = null,
  variant = 'default'
}: BittensorWalletConnectProps) {
  const [account, setAccount] = useAtom(bittensorAccountAtom)
  const [isConnecting, setIsConnecting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [availableWallets, setAvailableWallets] = useState<string[]>([])
  const [retryCount, setRetryCount] = useState(0)
  const [showAccountSelector, setShowAccountSelector] = useState(false)
  const [availableAccounts, setAvailableAccounts] = useState<BittensorAccount[]>([])
  const [pendingSigner, setPendingSigner] = useState<BittensorSigner | null>(null)
  const [forceReauth, setForceReauth] = useState(false)
  const [showAccountGuide, setShowAccountGuide] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)
  const [isRestoring, setIsRestoring] = useState(true) // 添加恢复状态
  const [isHydrated, setIsHydrated] = useState(false) // 客户端水合状态
  
  // 钱包认证相关
  const { authenticateWallet, logout, isAuthenticating, authError, clearError } = useWalletAuth()

  // 检查 Bittensor 钱包是否已安装
  const checkInstalledWallets = () => {
    if (typeof window === 'undefined') return []
    
    const wallets = []
    
    // 检查 Bittensor Wallet 扩展 - 检查多种可能的注入方式
    if (
      window.injectedWeb3?.['bittensor-wallet'] || 
      window.injectedWeb3?.['bittensor'] || 
      window.bittensorWallet || 
      // 检查是否有任何包含 bittensor 的注入对象
      Object.keys(window.injectedWeb3 || {}).some(key => key.toLowerCase().includes('bittensor'))
    ) {
      wallets.push('bittensor-wallet')
    }
    
    // 检查其他支持 Substrate 的钱包
    if (window.injectedWeb3?.['polkadot-js']) {
      wallets.push('polkadot-js')
    }
    
    if (window.injectedWeb3?.['talisman']) {
      wallets.push('talisman')
    }

    if (window.injectedWeb3?.['subwallet-js']) {
      wallets.push('subwallet-js')
    }
    
    // 打印调试信息到控制台
    console.log('Available wallet extensions:', window.injectedWeb3 ? Object.keys(window.injectedWeb3) : 'none')
    
    return wallets
  }

  // 连接指定的钱包
  const connectWallet = async (walletName?: string, isRetry = false, isAutoReconnect = false) => {
    console.log('🔌 Starting connectWallet with params:', { walletName, isRetry, isAutoReconnect })
    
    const installedWallets = checkInstalledWallets()
    
    if (installedWallets.length === 0) {
      setError('No compatible wallet found. Please install Bittensor Wallet.')
      return
    }

    const targetWallet = walletName || installedWallets[0]
    
    // 确保在每次连接尝试时重置相关状态
    if (!isAutoReconnect) {
      setShowAccountSelector(false)
      setAvailableAccounts([])
      setPendingSigner(null)
    }
    
    setIsConnecting(true)
    setError(null)

    try {
      let walletExtension: BittensorExtension | undefined

      // 尝试不同的方式获取钱包扩展
      if (targetWallet === 'bittensor-wallet') {
        // 按优先级尝试不同的注入方式
        walletExtension = 
          window.injectedWeb3?.['bittensor-wallet'] ||
          window.injectedWeb3?.['bittensor'] ||
          window.bittensorWallet ||
          // 查找任何包含 bittensor 的扩展
          Object.entries(window.injectedWeb3 || {})
            .find(([key]) => key.toLowerCase().includes('bittensor'))?.[1]
      } else {
        walletExtension = window.injectedWeb3?.[targetWallet]
      }

      if (!walletExtension) {
        console.log('Available extensions:', window.injectedWeb3 ? Object.keys(window.injectedWeb3) : 'none')
        throw new Error(`Wallet extension not found. Available: ${window.injectedWeb3 ? Object.keys(window.injectedWeb3).join(', ') : 'none'}`)
      }

      console.log('Connecting to wallet extension:', walletExtension)

      // 对于强制重新授权或重试连接，使用唯一的应用名称来绕过缓存
      const appName = (forceReauth || isRetry) ? 
        `DogeMine-${Date.now()}-${Math.random().toString(36).substr(2, 9)}` : 
        'DogeMine'
      console.log('Requesting wallet connection with app name:', appName)
      
      // 添加更多调试信息
      console.log('Connection context:', {
        forceReauth,
        isRetry,
        isAutoReconnect,
        targetWallet,
        retryCount
      })

      // 如果是强制重新授权，重置状态
      if (forceReauth) {
        setForceReauth(false)
        console.log('Force re-authentication requested')
      }

      // 请求连接钱包 - 仅获取可用账户，不需要密码
      console.log('🔌 Calling walletExtension.enable() - getting available accounts without password prompt')
      const signer = await walletExtension.enable(appName)
      console.log('✅ Wallet enabled successfully, getting available accounts...')
      
      // 添加小延迟，让钱包有时间加载账户
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // 获取所有可用的账户列表（无需密码，只是获取账户信息）
      const accounts = await signer.accounts.get()
      console.log('Available accounts found:', accounts)
      
      if (accounts.length === 0) {
        // 对于自动重连，可能是权限问题，静默失败
        if (isAutoReconnect) {
          console.log('Auto-reconnect failed: no accounts accessible')
          return
        }
        
        // 用户主动连接但没有获得账户访问权限
        // 这通常意味着用户需要在钱包扩展中选择要共享的账户
        console.log('No accounts accessible - user needs to select accounts to share in wallet extension')
        setError('请在钱包扩展中选择要共享给此网站的账户，然后重试连接。')
        setIsConnecting(false)
        
        // 显示详细的用户指导
        setShowAccountGuide(true)
        return
      }

      // 总是显示账户选择器让用户确认，选择后才进行密码认证
      console.log('Showing account selector - user will authenticate after selection')
      setAvailableAccounts(accounts)
      setPendingSigner(signer)
      setShowAccountSelector(true)
      setIsConnecting(false)
      
    } catch (err) {
      console.error('Failed to connect Bittensor wallet:', err)
      
      // 检查是否是端口断开错误
      if (err instanceof Error && err.message.includes('disconnected port')) {
        setError('Wallet connection lost. Please refresh the page and try again.')
      } else if (err instanceof Error && err.message.includes('User rejected')) {
        setError('Connection cancelled by user.')
      } else if (err instanceof Error && err.message.includes('Connection cancelled')) {
        setError('Permission denied. Please approve access to your wallet accounts and try again.')
      } else if (err instanceof Error && err.message.includes('No accounts found')) {
        setError(err.message)
      } else {
        setError(err instanceof Error ? err.message : 'Failed to connect wallet')
      }
    } finally {
      setIsConnecting(false)
    }
  }

  // 选择账户并连接
  const selectAccount = async (selectedAccount: BittensorAccount) => {
    console.log('User selected account for authentication:', selectedAccount)
    
    // 保存当前的签名器引用，用于认证
    const currentSigner = pendingSigner
    
    // 清理选择器状态（但不设置账户状态，等认证成功后再设置）
    setShowAccountSelector(false)
    setAvailableAccounts([])
    setForceReauth(false)
    setRetryCount(0) // 重置重试计数
    
    try {
      console.log('🔐 Starting wallet authentication process - user will be prompted for password...')
      
      // 进行钱包认证，这里会要求用户输入密码进行签名
      const authResult = await authenticateWallet(selectedAccount, currentSigner)
      console.log('🎉 Authentication successful:', authResult)
      
      // 认证成功后才设置账户状态
      setAccount(selectedAccount)
      
      // 保存到 localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('bittensor-account', JSON.stringify(selectedAccount))
      }
      
      // 清除之前的错误
      setError(null)
      clearError()
      
      onConnect?.(selectedAccount)
    } catch (error) {
      console.error('❌ Authentication failed:', error)
      
      // 认证失败，确保不设置账户状态
      setAccount(null)
      if (typeof window !== 'undefined') {
        localStorage.removeItem('bittensor-account')
      }
      
      // 显示认证错误
      const errorMessage = error instanceof Error ? error.message : '钱包认证失败，请重试'
      setError(`认证失败：${errorMessage}`)
    } finally {
      // 清理 pending signer 引用
      setPendingSigner(null)
    }
  }

  // 断开连接
  const disconnectWallet = async () => {
    try {
      // 先进行后端登出
      await logout()
    } catch (error) {
      console.warn('Backend logout failed:', error)
    } finally {
      // 无论后端是否成功，都清除本地状态
      setAccount(null)
      
      // 清除 localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('bittensor-account')
      }
      
      // 清理选择器状态
      setShowAccountSelector(false)
      setAvailableAccounts([])
      setPendingSigner(null)
      setForceReauth(false)
      
      // 清除错误状态
      setError(null)
      clearError()
      
      onDisconnect?.()
    }
  }

  // 取消账户选择
  const cancelAccountSelection = () => {
    setShowAccountSelector(false)
    setAvailableAccounts([])
    setPendingSigner(null)
    setIsConnecting(false)
    setForceReauth(false)
  }

  // 客户端水合检测
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // 组件初始化时启动恢复流程
  useEffect(() => {
    if (isHydrated && !hasInitialized) {
      setHasInitialized(true)
      
      console.log('🚀 Initializing BittensorWalletConnect component after hydration')
      
      // 从 localStorage 检查是否有保存的账户
      try {
        const savedAccount = localStorage.getItem('bittensor-account')
        if (savedAccount) {
          const parsedAccount = JSON.parse(savedAccount)
          console.log('🔄 Found saved account, attempting to verify connection:', parsedAccount.address)
          
          // 不要立即设置账户状态，先验证连接
          setTimeout(() => {
            attemptReconnect(parsedAccount)
          }, 200)
        } else {
          // 没有保存的账户
          console.log('🔄 No saved account found')
          setIsRestoring(false)
        }
      } catch (error) {
        console.error('Failed to restore account from localStorage:', error)
        localStorage.removeItem('bittensor-account')
        setIsRestoring(false)
      }
    }
  }, [isHydrated, hasInitialized])

  // 同步 localStorage 与当前账户状态
  useEffect(() => {
    if (typeof window !== 'undefined' && hasInitialized && account) {
      // 只在有账户时更新 localStorage
      localStorage.setItem('bittensor-account', JSON.stringify(account))
    }
  }, [account, hasInitialized])

  // 同步外部账户状态和自动重连
  useEffect(() => {
    if (initialAccount !== account) {
      setAccount(initialAccount)
      
      // 如果有初始账户但当前没有连接，尝试自动重连
      if (initialAccount && !account) {
        attemptReconnect(initialAccount)
      }
    }
  }, [initialAccount])

  // 尝试自动重连
  const attemptReconnect = async (savedAccount: BittensorAccount) => {
    try {
      console.log('🔄 Attempting auto-reconnect for account:', savedAccount.address)
      
      const installedWallets = checkInstalledWallets()
      if (installedWallets.length === 0) {
        console.log('No wallets available for reconnection')
        // 延迟一段时间后再次尝试，可能钱包扩展还在加载
        setTimeout(() => {
          const walletsRetry = checkInstalledWallets()
          if (walletsRetry.length > 0) {
            console.log('🔄 Retrying auto-reconnect after wallet loaded')
            attemptReconnect(savedAccount)
          } else {
            // 钱包扩展真的不可用，但保持 UI 状态
            console.log('🔄 Wallet extension not available, keeping saved state')
            setIsRestoring(false)
          }
        }, 2000)
        return
      }

      // 尝试获取钱包扩展
      const targetWallet = 'bittensor-wallet'
      
      const walletExtension:BittensorExtension | undefined =
        window.injectedWeb3?.['bittensor-wallet'] ||
        window.injectedWeb3?.['bittensor'] ||
        window.bittensorWallet ||
        Object.entries(window.injectedWeb3 || {})
          .find(([key]) => key.toLowerCase().includes('bittensor'))?.[1]

      if (!walletExtension) {
        console.log('Wallet extension not found for reconnection, retrying in 2s...')
        // 延迟重试，钱包扩展可能还在注入过程中
        setTimeout(() => {
          attemptReconnect(savedAccount)
        }, 2000)
        return
      }

      // 静默重连，不显示加载状态
      const signer = await walletExtension.enable('DogeMine')
      
      // 给钱包一些时间来准备账户
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const accounts = await signer.accounts.get()
      console.log('🔄 Auto-reconnect accounts found:', accounts.length)
      
      // 无论如何都要结束恢复状态
      setIsRestoring(false)
      
      // 如果没有获得任何账户权限，清除状态
      if (accounts.length === 0) {
        console.log('Auto-reconnect: no accounts accessible, clearing state')
        setAccount(null)
        localStorage.removeItem('bittensor-account')
        return
      }
      
      // 检查保存的账户是否仍然存在
      const matchingAccount = accounts.find(acc => acc.address === savedAccount.address)
      if (matchingAccount) {
        console.log('✅ Auto-reconnect successful:', matchingAccount.address)
        
        // 设置账户状态（总是设置，确保状态同步）
        setAccount(matchingAccount)
        
        // 更新 localStorage 中的账户信息
        if (typeof window !== 'undefined') {
          localStorage.setItem('bittensor-account', JSON.stringify(matchingAccount))
        }
        
        onConnect?.(matchingAccount)
      } else {
        // 账户不存在，清除状态
        console.log('Saved account no longer accessible, clearing state')
        setAccount(null)
        localStorage.removeItem('bittensor-account')
      }
    } catch (error) {
      // 自动重连失败，清除状态并结束恢复
      console.log('Auto-reconnect failed:', error)
      setIsRestoring(false)
      setAccount(null)
      localStorage.removeItem('bittensor-account')
    }
  }

  // 检查已安装的钱包
  useEffect(() => {
    const checkWallets = () => {
      try {
        const wallets = checkInstalledWallets()
        setAvailableWallets(wallets)
        
        // 如果有保存的账户但当前没有连接，且钱包可用，尝试重连
        if (wallets.length > 0 && !account && typeof window !== 'undefined') {
          const savedAccount = localStorage.getItem('bittensor-account')
          if (savedAccount) {
            try {
              const parsedAccount = JSON.parse(savedAccount)
              console.log('🔄 Found saved account during wallet check, attempting reconnect')
              setTimeout(() => attemptReconnect(parsedAccount), 1000)
            } catch (error) {
              console.error('Error parsing saved account during wallet check:', error)
            }
          }
        }
      } catch (error) {
        console.warn('Error checking wallets:', error)
      }
    }

    // 初始检查
    checkWallets()

    // 定期检查（钱包扩展可能需要时间加载）
    const interval = setInterval(checkWallets, 1000)
    
    // 5 秒后停止检查
    setTimeout(() => clearInterval(interval), 5000)

    // 监听扩展注入事件
    const handleInjectedWeb3 = () => {
      setTimeout(checkWallets, 100)
    }

    window.addEventListener('web3-injected', handleInjectedWeb3)

    // 监听页面可见性变化，当页面重新获得焦点时重新检查钱包
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setTimeout(checkWallets, 100)
        
        // 页面重新获得焦点时，检查是否需要恢复连接状态
        if (typeof window !== 'undefined') {
          const savedAccount = localStorage.getItem('bittensor-account')
          if (savedAccount && !account) {
            try {
              const parsedAccount = JSON.parse(savedAccount)
              console.log('🔄 Page became visible, attempting to verify connection')
              setTimeout(() => attemptReconnect(parsedAccount), 500)
            } catch (error) {
              console.error('Error restoring account on visibility change:', error)
              localStorage.removeItem('bittensor-account')
            }
          }
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      clearInterval(interval)
      window.removeEventListener('web3-injected', handleInjectedWeb3)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [account])

  // 如果没有可用的钱包，显示安装提示
  if (availableWallets.length === 0) {
    return (
      <div className={cn("relative", className)}>
        <div
          className={cn(
            'flex cursor-pointer items-center justify-center transition-all duration-200',
            // 默认变体样式
            variant === 'default' && [
              'border-2 border-purple-300 bg-gradient-to-r from-purple-100 to-indigo-100 px-4 py-2 hover:border-purple-400 hover:from-purple-200 hover:to-indigo-200',
              isCollapsed ? 'h-[48px] w-[48px] rounded-full bg-gradient-to-br from-purple-200 to-purple-300' : 'h-[48px] min-w-[200px] rounded-2xl',
              isMobile && 'h-[36px] min-w-[163px] rounded-xl'
            ],
            // 落地页变体样式
            variant === 'landing' && [
              'h-[48px] w-[320px] rounded-lg px-2 text-white hover:opacity-90'
            ]
          )}
          style={variant === 'landing' ? { backgroundColor: '#141413' } : undefined}
          onClick={() => window.open('https://chromewebstore.google.com/detail/bittensor-wallet/bdgmdoedahdcjmpmifafdhnffjinddgc', '_blank')}
        >
          {!isCollapsed && (
            <div className={cn(
              "flex items-center",
              variant === 'default' ? "gap-2" : "gap-3"
            )}>
              <div className={cn(
                "rounded-full",
                variant === 'landing' ? "size-2 bg-[#F89E1F]" : "size-3 border border-purple-300 bg-purple-500"
              )} />
              {variant === 'landing' && (
                <div className="text-white">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 8V7C19 5.9 18.1 5 17 5H3C1.9 5 1 5.9 1 7V17C1 18.1 1.9 19 3 19H17C18.1 19 19 18.1 19 17V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    <path d="M16 12H22C23.1 12 24 11.1 24 10V10C24 8.9 23.1 8 22 8H16C14.9 8 14 8.9 14 10V10C14 11.1 14.9 12 16 12Z" fill="currentColor"/>
                  </svg>
                </div>
              )}
              <span className={cn(
                "font-semibold",
                variant === 'landing' ? "text-base text-white" : "text-sm text-purple-800"
              )}>
                Install Bittensor Wallet
              </span>
            </div>
          )}
          {isCollapsed && (
            <div className="size-3 rounded-full border border-purple-300 bg-purple-500" />
          )}
        </div>
      </div>
    )
  }

  // 获取钱包显示名称
  const getWalletDisplayName = (walletName: string) => {
    const names: Record<string, string> = {
      'bittensor-wallet': 'Bittensor Wallet',
      'polkadot-js': 'Polkadot{.js}',
      'talisman': 'Talisman',
      'subwallet-js': 'SubWallet'
    }
    return names[walletName] || walletName
  }

  // 计算显示状态，避免 SSR 水合闪烁
  const displayState = {
    isConnected: account && !isRestoring && !isAuthenticating && isHydrated,
    isLoading: isConnecting || isRestoring || isAuthenticating || !isHydrated,
    showAsConnected: account !== null && !isAuthenticating && isHydrated, // 只有在水合完成后才显示真实状态
    showNeutralState: !isHydrated // 水合前显示中性状态
  }

  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(
          'flex cursor-pointer items-center',
          // 默认变体样式
          variant === 'default' && [
            'justify-between rounded-xl bg-white/80 p-2 hover:bg-[#F0EEE6]',
            isCollapsed ? 'h-[48px] w-[48px] rounded-full' : 'h-[48px] w-[232px]',
            isMobile && 'h-[36px] w-[163px] rounded-md border border-[#1F1E1D26]',
            isDropdownOpen && 'bg-[#F0EEE6]'
          ],
          // 落地页变体样式
          variant === 'landing' && [
            'h-[48px] w-[320px] justify-center rounded-lg px-2',
            'text-white hover:opacity-90'
          ]
        )}
        style={variant === 'landing' ? { backgroundColor: '#141413' } : undefined}
        onClick={displayState.showNeutralState ? undefined : 
          displayState.showAsConnected ? () => setIsDropdownOpen(!isDropdownOpen) : () => {
          console.log('🔗 User clicked main connect button - resetting states')
          // 清除错误和重置状态
          setError(null)
          setIsConnecting(false)
          setShowAccountSelector(false)
          setAvailableAccounts([])
          setPendingSigner(null)
          setRetryCount(0)
          setForceReauth(false)
          
          connectWallet()
        }}
      >
        <div className={cn(
          "flex items-center",
          variant === 'default' ? "gap-[12px]" : "gap-3"
        )}>
          {/* 状态指示器 - 对于 landing variant 使用橙色 */}
          <div 
            className={cn(
              "flex-shrink-0 rounded-full",
              isCollapsed || isMobile ? "size-3" : "size-4",
              variant === 'landing' ? "size-2 bg-[#F89E1F]" :
              displayState.showNeutralState ? "bg-gray-300" :
              displayState.showAsConnected ? "bg-green-500" : 
              displayState.isLoading ? "bg-yellow-500" : "bg-gray-300"
            )}
          />
          
          {!isCollapsed && (
            <>
              {/* Wallet 图标 */}
              <div className={cn(
                variant === 'landing' ? "text-white" : "text-gray-700"
              )}>
                <svg 
                  width={variant === 'landing' ? "18" : (isMobile ? "12" : "24")} 
                  height={variant === 'landing' ? "18" : (isMobile ? "12" : "24")} 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M19 8V7C19 5.9 18.1 5 17 5H3C1.9 5 1 5.9 1 7V17C1 18.1 1.9 19 3 19H17C18.1 19 19 18.1 19 17V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <path d="M16 12H22C23.1 12 24 11.1 24 10V10C24 8.9 23.1 8 22 8H16C14.9 8 14 8.9 14 10V10C14 11.1 14.9 12 16 12Z" fill="currentColor"/>
                </svg>
              </div>
              
              {/* 连接状态文本 */}
              <div className="flex flex-col items-start text-left">
                <span className={cn(
                  "font-medium",
                  variant === 'landing' ? "text-white" : "text-gray-800",
                  isMobile ? "text-sm" : "text-base"
                )}>
                  {displayState.showNeutralState ? 'Connect Wallet' :
                   isRestoring && account ? (account.name || 'Bittensor Account') :
                   isAuthenticating ? 'Authenticating...' :
                   isConnecting ? (retryCount > 0 ? `Retrying... (${retryCount}/2)` : 'Connecting...') :
                   account ? (account.name || 'Bittensor Account') : 'Connect Wallet'}
                </span>
                {account && account.balance && !isMobile && !isRestoring && !isAuthenticating && isHydrated && (
                  <span className={cn(
                    "text-xs",
                    variant === 'landing' ? "text-white/70" : "text-gray-500"
                  )}>
                    {account.balance} TAO
                  </span>
                )}
              </div>
            </>
          )}
        </div>

        {/* 下拉箭头 - 仅在默认变体中显示 */}
        {!isCollapsed && variant === 'default' && (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 9L12 15 18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        )}
      </div>
      
      {/* 错误提示 */}
      {(error || authError) && (
        <div className="absolute left-0 top-full z-50 mt-2 max-w-sm rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-600">
          <div className="mb-1 font-medium">{authError ? 'Authentication Failed' : 'Connection Failed'}</div>
          <div className="mb-2">{authError || error}</div>
          
          {(error || authError || '').includes('Permission denied') && (
            <div className="mb-3 text-xs text-red-500">
              <strong>Next steps:</strong>
              <ul className="mt-1 list-inside list-disc space-y-1">
                <li>Click "Try Again" below to reconnect</li>
                <li>Make sure to allow popup windows for this site</li>
                <li>Select at least one account in the wallet popup</li>
                <li>Click "Approve" or "Allow" to grant access</li>
              </ul>
              <div className="mt-2 rounded border border-blue-200 bg-blue-50 p-2 text-blue-700">
                💡 <strong>Troubleshooting tips:</strong>
                <br />• If popup doesn't appear: Check popup blocker settings
                <br />• If "Try Again" fails repeatedly: Click "Debug & Test" to check wallet status
                <br />• Manual fix: Open Bittensor Wallet extension manually, then try connecting
                <br />• Last resort: Use "Force Refresh" to completely reset the page
              </div>
            </div>
          )}
          
          {(error || authError || '').includes('No accounts found') && (
            <div className="mb-3 text-xs text-red-500">
              <strong>Solutions:</strong>
              <ul className="mt-1 list-inside list-disc space-y-1">
                <li>Open your Bittensor Wallet extension</li>
                <li>Create a new account if you don't have one</li>
                <li>Make sure your wallet is unlocked</li>
                <li>Grant permission when prompted</li>
              </ul>
            </div>
          )}
          
          {(error || authError || '').includes('connection lost') && (
            <div className="mb-3 text-xs text-red-500">
              <strong>Solutions:</strong>
              <ul className="mt-1 list-inside list-disc space-y-1">
                <li>Refresh the page (Ctrl+F5 or Cmd+R)</li>
                <li>Restart your browser</li>
                <li>Disable and re-enable the Bittensor Wallet extension</li>
              </ul>
            </div>
          )}
          
          <div className="flex flex-wrap gap-2">
            {(error && (error.includes('No accounts found') || error.includes('Permission denied'))) && (
              <>
                <button 
                  onClick={async () => {
                    console.log('🔄 User clicked Try Again - performing complete reset')
                    
                    // 1. 清除所有 React 状态
                    setError(null)
                    setIsConnecting(false)
                    setShowAccountSelector(false)
                    setAvailableAccounts([])
                    setPendingSigner(null)
                    setRetryCount(0)
                    
                    // 2. 清除 localStorage 中的钱包状态
                    if (typeof window !== 'undefined') {
                      localStorage.removeItem('bittensor-account')
                      console.log('🧹 Cleared localStorage wallet data')
                    }
                    
                    // 3. 尝试重置钱包扩展连接（多种方法）
                    try {
                      const wallets = checkInstalledWallets()
                      if (wallets.length > 0) {
                        const walletExtension = 
                          window.injectedWeb3?.['bittensor-wallet'] ||
                          window.injectedWeb3?.['bittensor'] ||
                          window.bittensorWallet ||
                          Object.entries(window.injectedWeb3 || {})
                            .find(([key]) => key.toLowerCase().includes('bittensor'))?.[1]
                        
                        if (walletExtension) {
                          console.log('🔌 Attempting to reset wallet extension connection')
                          
                          // 方法 1: 用多个不同的应用名尝试重置
                          const resetNames = [
                            `DogeMine-Reset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                            `Reset-${Math.random().toString(36).substr(2, 16)}`,
                            `Clear-${Date.now()}`,
                            'Disconnect'
                          ]
                          
                          for (const resetAppName of resetNames) {
                            try {
                              console.log('Trying reset with name:', resetAppName)
                              await walletExtension.enable(resetAppName)
                              console.log('Reset attempt completed with:', resetAppName)
                              break
                            } catch (resetErr) {
                              console.log('Reset attempt failed for:', resetAppName, resetErr)
                            }
                          }
                        }
                      }
                    } catch (resetError) {
                      console.log('⚠️ Extension reset attempt failed:', resetError)
                    }
                    
                    // 4. 设置强制重新授权
                    setForceReauth(true)
                    
                    // 5. 延迟后开始新的连接尝试
                    setTimeout(() => {
                      console.log('🚀 Starting completely fresh connection attempt')
                      connectWallet()
                    }, 500)
                  }}
                  className="rounded bg-red-100 px-2 py-1 text-xs text-red-700 hover:bg-red-200"
                >
                  Try Again
                </button>
                
                <button 
                  onClick={() => {
                    console.log('🔍 Running wallet diagnostic...')
                    
                    console.log('=== WALLET DIAGNOSTIC REPORT ===')
                    console.log('Available wallets:', checkInstalledWallets())
                    console.log('injectedWeb3 keys:', window.injectedWeb3 ? Object.keys(window.injectedWeb3) : 'none')
                    console.log('Current account:', account)
                    console.log('Error state:', error)
                    console.log('Is connecting:', isConnecting)
                    console.log('Show selector:', showAccountSelector)
                    console.log('Available accounts:', availableAccounts.length)
                    console.log('Pending signer:', !!pendingSigner)
                    console.log('Force reauth:', forceReauth)
                    console.log('Retry count:', retryCount)
                    console.log('LocalStorage account:', localStorage.getItem('bittensor-account'))
                    console.log('=== END DIAGNOSTIC ===')
                    
                    // 尝试直接调用钱包扩展（用于测试）
                    const wallets = checkInstalledWallets()
                    if (wallets.length > 0) {
                      console.log('🧪 Testing direct wallet call...')
                      const walletExtension = 
                        window.injectedWeb3?.['bittensor-wallet'] ||
                        window.injectedWeb3?.['bittensor'] ||
                        window.bittensorWallet ||
                        Object.entries(window.injectedWeb3 || {})
                          .find(([key]) => key.toLowerCase().includes('bittensor'))?.[1]
                      
                      if (walletExtension) {
                        const testAppName = `Test-${Date.now()}`
                        walletExtension.enable(testAppName)
                          .then(() => console.log('✅ Direct wallet test successful'))
                          .catch((err: any) => console.log('❌ Direct wallet test failed:', err))
                      }
                    }
                    
                    alert('Diagnostic complete. Check console for details. If a wallet popup appeared, the extension is working.')
                  }}
                  className="rounded bg-purple-100 px-2 py-1 text-xs text-purple-700 hover:bg-purple-200"
                  title="Run diagnostic and test wallet directly"
                >
                  Debug & Test
                </button>
                
                <button 
                  onClick={() => {
                    console.log('🔄 Force refresh requested')
                    // 清除所有可能的本地存储
                    if (typeof window !== 'undefined') {
                      localStorage.removeItem('bittensor-account')
                      sessionStorage.clear()
                    }
                    window.location.reload()
                  }}
                  className="rounded bg-amber-100 px-2 py-1 text-xs text-amber-700 hover:bg-amber-200"
                  title="Refresh the entire page to reset all wallet states"
                >
                  Force Refresh
                </button>
              </>
            )}
            <button 
              onClick={() => {
                setError(null)
                clearError()
              }}
              className="text-xs text-red-400 underline hover:text-red-600"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}
      
      {/* Bittensor 钱包下拉菜单 */}
      {account && isDropdownOpen && (
        <BittensorWalletDropdown
          isOpen={true}
          onClose={() => setIsDropdownOpen(false)}
          address={account.address}
          displayName={account.name}
          displayBalance={account.balance}
          isCollapsed={isCollapsed}
          isMobile={isMobile}
          account={account}
          onDisconnect={disconnectWallet}
        />
      )}

      {/* 账户选择器 */}
      {showAccountSelector && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="mx-4 w-full max-w-md rounded-xl bg-white p-6 shadow-xl">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Select Account</h3>
              <p className="text-sm text-gray-600">Choose an account to connect. You'll be prompted to enter your wallet password to complete authentication.</p>
            </div>
            
            <div className="space-y-2">
              {availableAccounts.map((acc, index) => (
                <div
                  key={acc.address}
                  onClick={() => selectAccount(acc)}
                  className="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 p-3 transition-colors hover:border-gray-300 hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {acc.name || `Account ${index + 1}`}
                    </div>
                    <div className="font-mono text-sm text-gray-500">
                      {acc.address.slice(0, 8)}...{acc.address.slice(-8)}
                    </div>
                    {acc.balance && (
                      <div className="text-sm text-gray-600">
                        {acc.balance} TAO
                      </div>
                    )}
                  </div>
                  <div className="ml-3">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 12L11 14 15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 flex gap-3">
              <button
                onClick={cancelAccountSelection}
                className="flex-1 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 账户指导模态框 */}
      {showAccountGuide && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="mx-4 w-full max-w-lg rounded-xl bg-white p-6 shadow-xl">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">需要在钱包中选择账户</h3>
              <p className="mt-2 text-sm text-gray-600">
                钱包扩展已连接，但您需要在钱包中选择要共享给此网站的账户。
              </p>
            </div>
            
            <div className="space-y-4">
              <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                <h4 className="mb-2 font-medium text-blue-900">请按照以下步骤操作：</h4>
                <ol className="list-inside list-decimal space-y-2 text-sm text-blue-800">
                  <li>点击浏览器工具栏中的 Bittensor 钱包扩展图标</li>
                  <li>在钱包扩展中，找到账户管理或网站权限设置</li>
                  <li>确保至少选择了一个账户与此网站共享</li>
                  <li>如果没有账户，请先在钱包中创建或导入账户</li>
                  <li>完成后，点击下方的"重试连接"按钮</li>
                </ol>
              </div>
              
              <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                <h4 className="mb-2 font-medium text-yellow-900">常见问题：</h4>
                <ul className="list-inside list-disc space-y-1 text-sm text-yellow-800">
                  <li>确保钱包扩展已完全加载</li>
                  <li>检查是否有账户存在于您的钱包中</li>
                  <li>尝试刷新浏览器页面后重新连接</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-6 flex gap-3">
              <button
                onClick={() => {
                  setShowAccountGuide(false)
                  setError(null)
                }}
                className="flex-1 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => {
                  setShowAccountGuide(false)
                  setError(null)
                  connectWallet()
                }}
                className="flex-1 rounded-lg bg-orange-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-orange-600"
              >
                重试连接
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}