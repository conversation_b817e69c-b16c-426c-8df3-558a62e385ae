'use client'

import { useState } from 'react'

import { useWalletConnection } from '@/lib/hooks/use-wallet-connection'
import { cn } from '@/lib/utils/cn'

import { BittensorWalletConnect } from './bittensor-wallet-connect'
import { WalletConnectButton } from './wallet-connect-button'

interface MultiWalletConnectProps {
  isCollapsed?: boolean
  className?: string
  isMobile?: boolean
}

type WalletType = 'evm' | 'bittensor'

export function MultiWalletConnect({
  isCollapsed = false,
  className,
  isMobile = false
}: MultiWalletConnectProps) {
  const { 
    isEvmConnected, 
    isBittensorConnected, 
    isAnyWalletConnected,
    connectBittensorWallet,
    disconnectBittensorWallet 
  } = useWalletConnection()
  
  const [selectedWalletType, setSelectedWalletType] = useState<WalletType>('evm')
  const [isWalletSelectorOpen, setIsWalletSelectorOpen] = useState(false)

  // 检查是否有任何钱包连接
  const hasAnyWalletConnected = isAnyWalletConnected

  // 如果是折叠状态，显示钱包类型选择器
  if (isCollapsed) {
    return (
      <div className={cn("relative", className)}>
        <div
          className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-white/80 hover:bg-[#F0EEE6]"
          onClick={() => setIsWalletSelectorOpen(!isWalletSelectorOpen)}
        >
          <div className={cn(
            "size-3 rounded-full",
            hasAnyWalletConnected ? "bg-green-500" : "bg-gray-300"
          )} />
        </div>

        {/* 钱包类型选择下拉菜单 */}
        {isWalletSelectorOpen && (
          <div className="absolute left-0 top-full z-50 mt-2 w-64 rounded-lg border border-gray-200 bg-white shadow-lg">
            <div className="p-4">
              <div className="mb-3 flex items-center justify-between">
                <span className="font-medium text-gray-800">Select Wallet Type</span>
                <button
                  onClick={() => setIsWalletSelectorOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-2">
                <div
                  onClick={() => {
                    setSelectedWalletType('evm')
                    setIsWalletSelectorOpen(false)
                  }}
                  className="cursor-pointer rounded-md border border-gray-200 p-3 hover:bg-gray-50"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex size-8 items-center justify-center rounded-full bg-blue-100">
                      <span className="text-sm font-medium text-blue-600">EVM</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">EVM Wallets</div>
                      <div className="text-sm text-gray-500">MetaMask, WalletConnect, etc.</div>
                    </div>
                  </div>
                </div>
                
                <div
                  onClick={() => {
                    setSelectedWalletType('bittensor')
                    setIsWalletSelectorOpen(false)
                  }}
                  className="cursor-pointer rounded-md border border-gray-200 p-3 hover:bg-gray-50"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex size-8 items-center justify-center rounded-full bg-purple-100">
                      <span className="text-sm font-medium text-purple-600">TAO</span>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-800">Bittensor Wallet</div>
                      <div className="text-sm text-gray-500">
                        {isBittensorConnected ? 'Connected to Bittensor' : 'Native Bittensor support'}
                      </div>
                    </div>
                    {isBittensorConnected && (
                      <div className="size-2 rounded-full bg-green-500" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  // 非折叠状态，显示钱包类型选择器和对应的钱包连接组件
  return (
    <div className={cn("space-y-3", className)}>
      {/* 钱包类型切换 */}
      <div className="flex rounded-lg bg-gray-100 p-1">
        <button
          onClick={() => setSelectedWalletType('evm')}
          className={cn(
            "flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors",
            selectedWalletType === 'evm'
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          )}
        >
          <div className="flex items-center justify-center gap-2">
            <div className="size-3 rounded-full bg-blue-500" />
            EVM Wallets
          </div>
        </button>
        <button
          onClick={() => setSelectedWalletType('bittensor')}
          className={cn(
            "flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors",
            selectedWalletType === 'bittensor'
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          )}
        >
          <div className="flex items-center justify-center gap-2">
            <div className="size-3 rounded-full bg-purple-500" />
            Bittensor
          </div>
        </button>
      </div>

      {/* 对应的钱包连接组件 */}
      {selectedWalletType === 'evm' ? (
        <WalletConnectButton 
          isCollapsed={false}
          isMobile={isMobile}
        />
      ) : (
        <BittensorWalletConnect 
          isCollapsed={false}
          isMobile={isMobile}
          onConnect={connectBittensorWallet}
          onDisconnect={disconnectBittensorWallet}
        />
      )}

    </div>
  )
}