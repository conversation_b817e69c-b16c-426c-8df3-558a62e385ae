'use client'

import { ConnectButton } from '@rainbow-me/rainbowkit'
import { useEffect, useRef,useState } from 'react'
import { useAccount, useDisconnect } from 'wagmi'

import { cn } from '@/lib/utils/cn'

import { WalletDropdown } from './wallet-dropdown'

// 导出 Bittensor 钱包连接组件
export { BittensorWalletConnect } from './bittensor-wallet-connect'

interface WalletConnectButtonProps {
  isCollapsed?: boolean
  className?: string
  isMobile?: boolean
}

export function WalletConnectButton({ 
  isCollapsed = false,
  className,
  isMobile = false
}: WalletConnectButtonProps) {
  const { disconnect } = useDisconnect()
  const { address } = useAccount()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const buttonRef = useRef<HTMLDivElement>(null)
  
  // 点击外部关闭下拉菜单
  const handleButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(!isMenuOpen);
  };
  
  // 当点击页面其他区域时关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };
    
    if (isMenuOpen) {
      document.addEventListener('click', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isMenuOpen]);
  
  // 移动端样式类
  const mobileStyles = "";
  
  return (
    <div 
      className={cn("relative", isMobile ? mobileStyles : "", className)} 
      ref={buttonRef}
    >
      <ConnectButton.Custom>
        {({
          account,
          chain,
          openAccountModal,
          openChainModal,
          openConnectModal,
          authenticationStatus,
          mounted,
        }) => {
          // 注意：如果你的应用不使用认证，你可以移除所有认证相关的逻辑
          const ready = mounted && authenticationStatus !== 'loading'
          const connected =
            ready &&
            account &&
            chain &&
            (!authenticationStatus || authenticationStatus === 'authenticated')
          
          // 如果是折叠状态或非移动端
          if (isCollapsed) {
            return (
              <div className="relative">
                <div
                  className={cn(
                    'flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-white/80 hover:bg-[#F0EEE6]',
                    !ready && 'opacity-0',
                  )}
                  onClick={(e) => {
                    if (connected) {
                      handleButtonClick(e);
                    } else {
                      openConnectModal();
                    }
                  }}
                >
                  {connected ? (
                    <div className="size-3 rounded-full bg-green-500" />
                  ) : (
                    <div className="size-3 rounded-full bg-gray-300" />
                  )}
                </div>
                
                {/* 钱包下拉菜单 */}
                {connected && isMenuOpen && (
                  <WalletDropdown
                    isOpen={true}
                    onClose={() => setIsMenuOpen(false)}
                    address={account.address}
                    displayName={account.displayName}
                    displayBalance={account.displayBalance}
                    isCollapsed={isCollapsed}
                    openAccountModal={openAccountModal}
                    openChainModal={openChainModal}
                    isMobile={isMobile}
                  />
                )}
              </div>
            )
          }
          
          // 移动端特定布局
          if (isMobile) {
            return (
              <div className="relative">
                <div
                  className={cn(
                    'flex h-[36px] w-[163px] cursor-pointer items-center justify-between rounded-md border border-[#1F1E1D26]  px-2 hover:bg-[#F0EEE6]',
                    !ready && 'opacity-0',
                    isMenuOpen && 'bg-[#F0EEE6]'
                  )}
                  onClick={(e) => {
                    if (connected) {
                      handleButtonClick(e);
                    } else {
                      openConnectModal();
                    }
                  }}
                >
                  <div className="flex items-center gap-[8px]">
                    {/* Status indicator */}
                    <div 
                      className={cn(
                        "size-3 flex-shrink-0 rounded-full",
                        connected ? "bg-green-500" : "bg-gray-300"
                      )}
                     />
                     
                    {/* Mobile Wallet icon */}
                    <div className="text-gray-800">
                      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9.99992 3.33331H2.66659C2.31297 3.33331 1.97383 3.44369 1.72378 3.64015C1.47373 3.83662 1.33325 4.10309 1.33325 4.38093V9.61905C1.33325 9.89685 1.47373 10.1633 1.72378 10.3598C1.97383 10.5562 2.31297 10.6666 2.66659 10.6666H9.99992C10.3535 10.6666 10.6927 10.5562 10.9427 10.3598C11.1928 10.1633 11.3333 9.89685 11.3333 9.61905V4.38093C11.3333 4.10309 11.1928 3.83662 10.9427 3.64015C10.6927 3.44369 10.3535 3.33331 9.99992 3.33331Z" stroke="#141413" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M1.33325 4.33331V3.33331M1.33325 3.33331V2.38093C1.33325 2.10309 1.455 1.83662 1.67171 1.64015C1.88841 1.44369 2.18233 1.33331 2.48881 1.33331H8.84439C9.15085 1.33331 9.44479 1.44369 9.66145 1.64015C9.87819 1.83662 9.99992 2.10309 9.99992 2.38093V3.33331H1.33325Z" stroke="#141413" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M11.3333 6V8.66667H8.66659C8.31299 8.66667 7.97385 8.5262 7.72379 8.27613C7.47372 8.02607 7.33325 7.68693 7.33325 7.33333C7.33325 6.97973 7.47372 6.6406 7.72379 6.39053C7.97385 6.14047 8.31299 6 8.66659 6H11.3333Z" stroke="#141413" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    
                    {/* Account or connect text */}
                    <span className="text-sm font-medium text-gray-800">
                      {connected ? `${account.displayName}` : 'Connect Wallet'}
                    </span>
                  </div>
                  
                  {/* Chain indicator or down arrow */}
                  {connected ? (
                    <div className="flex items-center gap-2">
                      {chain.hasIcon && (
                        <div
                          className="size-3 overflow-hidden rounded-full"
                          style={{
                            background: chain.iconBackground,
                          }}
                        >
                          {chain.iconUrl && (
                            <img
                              alt={chain.name ?? 'Chain icon'}
                              src={chain.iconUrl}
                              className="size-3"
                            />
                          )}
                        </div>
                      )}
                    </div>
                  ) : null}
                </div>
                
                {/* 钱包下拉菜单 */}
                {connected && isMenuOpen && account && (
                  <WalletDropdown
                    isOpen={true}
                    onClose={() => setIsMenuOpen(false)}
                    address={account.address}
                    displayName={account.displayName}
                    displayBalance={account.displayBalance}
                    isCollapsed={isCollapsed}
                    openAccountModal={openAccountModal}
                    openChainModal={openChainModal}
                    isMobile={isMobile}
                  />
                )}
              </div>
            );
          }
          
          // 默认布局 (PC 端)
          return (
            <div className="relative">
              <div
                className={cn(
                  'flex h-[48px] w-[232px] cursor-pointer items-center justify-between rounded-xl bg-white/80 p-2 hover:bg-[#F0EEE6]',
                  !ready && 'opacity-0',
                  isMenuOpen && 'bg-[#F0EEE6]'
                )}
                onClick={(e) => {
                  if (connected) {
                    handleButtonClick(e);
                  } else {
                    openConnectModal();
                  }
                }}
              >
                <div className="flex items-center gap-[12px]">
                  {/* Status indicator */}
                  <div 
                    className={cn(
                      "size-4 flex-shrink-0 rounded-full",
                      connected ? "bg-green-500" : "bg-gray-300"
                    )}
                   />
                  
                  {/* Wallet icon */}
                  <div className="text-gray-700">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19 8V7C19 5.9 18.1 5 17 5H3C1.9 5 1 5.9 1 7V17C1 18.1 1.9 19 3 19H17C18.1 19 19 18.1 19 17V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M16 12H22C23.1 12 24 11.1 24 10V10C24 8.9 23.1 8 22 8H16C14.9 8 14 8.9 14 10V10C14 11.1 14.9 12 16 12Z" fill="currentColor"/>
                    </svg>
                  </div>
                  
                  {/* Account or connect text */}
                  <div className="flex flex-col items-start text-left">
                    <span className="text-base font-medium text-gray-800">
                      {connected ? `${account.displayName}` : 'Connect Wallet'}
                    </span>
                  </div>
                </div>
                
                {/* Chain indicator or down arrow */}
                {connected ? (
                  <div className="flex items-center gap-2">
                    {chain.hasIcon && (
                      <div
                        className="size-4 overflow-hidden rounded-full"
                        style={{
                          background: chain.iconBackground,
                        }}
                      >
                        {chain.iconUrl && (
                          <img
                            alt={chain.name ?? 'Chain icon'}
                            src={chain.iconUrl}
                            className="size-4"
                          />
                        )}
                      </div>
                    )}
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 9L12 15 18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                ) : (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 9L12 15 18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
              </div>
              
              {/* 钱包下拉菜单 */}
              {connected && isMenuOpen && account && (
                <WalletDropdown
                  isOpen={true}
                  onClose={() => setIsMenuOpen(false)}
                  address={account.address}
                  displayName={account.displayName}
                  displayBalance={account.displayBalance}
                  isCollapsed={isCollapsed}
                  openAccountModal={openAccountModal}
                  openChainModal={openChainModal}
                  isMobile={isMobile}
                />
              )}
            </div>
          )
        }}
      </ConnectButton.Custom>
    </div>
  )
} 