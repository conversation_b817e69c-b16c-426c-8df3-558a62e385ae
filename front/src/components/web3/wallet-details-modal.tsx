'use client'

import { useDisconnect } from 'wagmi'


interface WalletDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  address: string
  ethBalance?: string
}

export function WalletDetailsModal({
  isOpen,
  onClose,
  address,
  ethBalance = '0 ETH'
}: WalletDetailsModalProps) {
  const { disconnect } = useDisconnect()
  
  if (!isOpen) return null
  
  const handleDisconnect = () => {
    disconnect()
    onClose()
  }
  
  const handleCopyAddress = () => {
    if (address) {
      navigator.clipboard.writeText(address)
      // 可以添加一个提示，表示地址已复制
    }
  }
  
  return (
    <div className="absolute inset-x-4 bottom-[60px] z-50 rounded-lg bg-white p-4 shadow-lg">
      <div className="space-y-3">
        {/* 账户列表 */}
        <div className="rounded-lg bg-[#F7F5EE] p-2">
          <div className="mb-2 flex items-center justify-between p-2">
            <div>
              <p className="font-medium">ccbaka(talisman)</p>
              <p className="text-sm text-gray-500">5Gnzm...cbsul</p>
            </div>
          </div>
          
          <div className="mb-2 flex items-center justify-between rounded-lg bg-white p-2">
            <div>
              <p className="font-medium">ccbaka(@opentensor/bittensor extension)</p>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-500">5Iams...vjius</p>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M9 9L12 12L15 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 3V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          
          <div className="flex items-center justify-between p-2">
            <div>
              <p className="font-medium">ccbaka(polkadot)</p>
              <p className="text-sm text-gray-500">5a02k...gsoo2</p>
            </div>
          </div>
        </div>
        
        {/* 断开连接按钮 */}
        <button 
          className="flex w-full items-center justify-center rounded-lg border border-gray-300 bg-white py-2 font-medium text-gray-800 hover:bg-gray-50"
          onClick={handleDisconnect}
        >
          <svg className="mr-2" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16 17L21 12L16 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Disconnect
        </button>
      </div>
    </div>
  )
} 