'use client'

import { useEffect, useState } from 'react'

import { cn } from '@/lib/utils/cn'

// Bittensor 账户接口
interface BittensorAccount {
  address: string
  name?: string
  balance?: string
  type?: 'sr25519' | 'ed25519'
}

// 定义账户类型
interface Account {
  address: string;
  displayName: string;
  isActive?: boolean;
  walletName?: string;
}

interface BittensorWalletDropdownProps {
  isOpen: boolean
  onClose: () => void
  address: string
  displayName?: string
  displayBalance?: string
  isCollapsed?: boolean
  isMobile?: boolean
  account?: BittensorAccount
  onDisconnect?: () => void
}

export function BittensorWalletDropdown({
  isOpen,
  onClose,
  address,
  displayName = '',
  displayBalance = '0 TAO',
  isCollapsed = false,
  isMobile = false,
  account,
  onDisconnect
}: BittensorWalletDropdownProps) {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isHovered, setIsHovered] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  
  // 初始化当前连接的账户
  useEffect(() => {
    const walletName = 'Bittensor Wallet';
    setAccounts([{ 
      address, 
      displayName: displayName || account?.name || `Account ${formatAddress(address)}`,
      walletName,
      isActive: true
    }]);
  }, [address, displayName, account]);
  
  if (!isOpen) return null
  
  const handleDisconnect = () => {
    onDisconnect?.()
    onClose()
  }
  
  const handleCopyAddress = (addr: string) => {
    if (addr) {
      navigator.clipboard.writeText(addr)
        .then(() => {
          setCopySuccess(true);
          // 2 秒后重置复制状态
          setTimeout(() => {
            setCopySuccess(false);
          }, 2000);
        })
        .catch(err => {
          console.error('复制失败：', err);
        });
    }
  }
  
  // 格式化地址显示
  const formatAddress = (addr: string) => {
    if (!addr) return '';
    return `${addr.substring(0, 6)}...${addr.substring(addr.length - 6)}`;
  };
  
  // 获取当前账户
  const currentAccount = accounts[0] || {};
  // 显示钱包名称优先级：1. 钱包名称 2. 显示名称 3. 格式化地址
  const displayWalletInfo = currentAccount.walletName || displayName || formatAddress(address);
  
  // 根据设备类型设置下拉菜单的位置
  const dropdownPosition = isMobile ? {
    top: '1%',
    left: '32%',
    padding: '8px 8px 12px 8px'
  } : {
    top: '65%',
    left: isCollapsed ? '70px' : '256px',
    padding: '8px 8px 12px 8px'
  };
  
  return (
    <div 
      className="fixed z-50 h-[300px] w-[280px] rounded-md border border-[#1F1E1D26] bg-[#F7F5EE] shadow-[0px_4px_6px_-4px_rgba(0,0,0,0.1),0px_10px_15px_-3px_rgba(0,0,0,0.1)]"
      style={dropdownPosition}
    >
      <div className="flex h-full flex-col gap-4">
        {/* 账户列表 */}
        <div className="flex-1 overflow-auto">
          {/* 当前活跃账户信息 */}
          <div 
            className={cn(
              "mb-2 flex items-center justify-between rounded-lg p-2 transition-colors duration-200",
              isHovered ? "bg-[#F0EEE6]" : ""
            )}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div>
              <p className="font-medium">{displayWalletInfo}</p>
              <div className="flex items-center gap-2">
                <p 
                  className="cursor-pointer text-sm text-gray-500" 
                  onClick={() => handleCopyAddress(address)}
                >
                  {formatAddress(address)}
                </p>
                {copySuccess && (
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                          strokeLinejoin="round"/>
                  </svg>
                )}
                <button
                  className={cn(
                    "text-gray-500 hover:text-gray-700",
                    !isHovered && "hidden"
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyAddress(address);
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15"
                        stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M9 9L12 12L15 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                          strokeLinejoin="round"/>
                    <path d="M12 3V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                          strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
            {copySuccess && (
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                      strokeLinejoin="round"/>
              </svg>
            )}
          </div>
        </div>
        
        {/* 断开连接按钮 */}
        <button 
          className="flex w-full scale-100 transform items-center justify-center rounded-lg border border-gray-300 bg-white py-2 font-medium text-gray-800 transition-transform duration-300 hover:scale-105 hover:bg-gray-50"
          onClick={handleDisconnect}
        >
          <svg className="mr-2" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16 17L21 12L16 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Disconnect
        </button>
      </div>
    </div>
  )
}