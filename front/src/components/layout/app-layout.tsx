'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useParams, usePathname } from 'next/navigation'
import type { JSX,ReactNode} from 'react';
import { useState} from 'react'

import { Button } from '@/components/ui'
import { BittensorWalletConnect } from '@/components/web3/bittensor-wallet-connect'
import { cn } from '@/lib/utils/cn'

interface AppLayoutProps {
  children: ReactNode
}

type NavItem = {
  label: string
  href: string
  icon: React.ReactNode
}

export function AppLayout({ children }: AppLayoutProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const params = useParams()
  const locale = params.locale as string
  const pathname = usePathname()

  const navItems: ({ icon: JSX.Element; label: string; href: string } | {
    icon: JSX.Element;
    label: string;
    href: string
  } | { icon: JSX.Element; label: string; href: string } | { icon: JSX.Element; label: string; href: string })[] = [
    {
      label: 'Dashboard',
      href: `/${locale}/dashboard`,
      icon: <img src={"/images/dashboard-home.svg"} className="size-5" />,
    },
    {
      label: 'Miner',
      href: `/${locale}/miner`,
      icon:  <img src={"/images/miner-icon.svg"} className="size-5" />,
    },
    {
      label: 'Validator',
      href: `/${locale}/validator`,
      icon:  <img src={"/images/validator-icon.svg"} className="size-5" />,
    },
    {
      label: 'Stake',
      href: `/${locale}/stake`,
      icon:  <img src={"/images/state-icon.svg"} className="size-5" />,
    },
  ]
  
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <div className="flex min-h-screen flex-col">
      {/* Mobile Header */}
      <header className="flex h-16 items-center justify-between border-b bg-white px-4">
        <Link href={`/${locale}`} className="flex items-center">
          <Image
            src="/images/design/dogemine-logo.svg"
            alt="Dogemine Logo"
            width={110}
            height={32}
            priority
            className="h-8 w-auto"
          />
        </Link>
        <Button
          onClick={toggleMenu} 
          className="p-2 text-gray-600 hover:text-gray-900"
          aria-label="Toggle menu"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="24" 
            height="24" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            {isMenuOpen ? (
              <>
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </>
            ) : (
              <>
                <line x1="4" y1="12" x2="20" y2="12" />
                <line x1="4" y1="6" x2="20" y2="6" />
                <line x1="4" y1="18" x2="20" y2="18" />
              </>
            )}
          </svg>
        </Button>
      </header>

      {/* Mobile Navigation Menu - Full screen overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 flex flex-col bg-[#F7F5EE]">
          {/* Mobile Menu Header */}
          <div className="relative flex h-16 items-center border-b px-4">
            <Link href={`/${locale}`} className="flex items-center">
              <Image
                src="/images/design/dogemine-logo.svg"
                alt="Dogemine Logo"
                width={110}
                height={32}
                priority
                className="h-8 w-auto"
              />
            </Link>
            
            {/* 钱包连接按钮 - 按照提供的样式属性设置 */}
            <div 
              className="absolute" 
              style={{ 
                top: '22%',
                left: '45%',
              }}
            >
              <BittensorWalletConnect isMobile={true} />
            </div>
            
            {/* 关闭按钮 */}
            <button 
              onClick={toggleMenu} 
              className="absolute right-4 p-2 text-gray-600 hover:text-gray-900"
              aria-label="Close menu"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="24" 
                height="24" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>
          </div>
          
          {/* Mobile Menu Content */}
          <nav className="flex flex-1 flex-col p-4">
            {navItems.map((item) => {
              const isActive = pathname.startsWith(item.href)
              
              return (
                <Link 
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'flex h-12 items-center rounded-lg px-4 text-gray-800 transition-colors hover:bg-[#F0EEE6]',
                    isActive && 'bg-[#F0EEE6] font-medium text-primary'
                  )}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="flex items-center gap-3">
                    {item.icon}
                    <span>{item.label}</span>
                  </div>
                </Link>
              )
            })}
          </nav>
        </div>
      )}

      {/* Main Content */}
      <main className={cn("flex-1", isMenuOpen ? "overflow-hidden" : "")}>
        {children}
      </main>
    </div>
  )
} 