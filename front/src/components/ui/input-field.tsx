import * as React from 'react'
import type { RefCallBack } from 'react-hook-form'

import { cn, ny } from '@/lib/utils'

import { IconButton } from './icon-button'
import { Icons } from './icons'

export type InputFieldProps = React.InputHTMLAttributes<HTMLInputElement> & {
  LeftIcon?: React.ReactNode
  RightIcon?: React.ReactNode
  status?: 'error'
  clean?: boolean
  onChangeValue?: (value: string) => void
}

const InputField = ({
  ref,
  className,
  LeftIcon,
  RightIcon,
  status,
  type,
  onChangeValue,
  value,
  clean = true,
  onChange,
  ...props
}: InputFieldProps & {
  ref?: React.RefObject<HTMLInputElement> | RefCallBack
}) => {
  const isError = status === 'error'

  const handleClear = () => {
    onChange?.({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>)
    onChangeValue?.('')
  }

  return (
    <div
      className={cn(
        'flex h-10 w-full items-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground ring-offset-background',
        'focus-within:text-primary focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
        'has-[input:disabled]:text-muted-foreground has-[input:disabled]:ring-0',
        isError && !props.disabled
          ? [
              'border-red-500',
              'hover:border-red-500 hover:ring-red-500',
              'focus-within:ring-red-500',
            ]
          : '',
        className,
      )}
    >
      {LeftIcon}

      <input
        type={type}
        value={value}
        className={ny(
          'h-full flex-1 bg-transparent text-sm leading-[22px] outline-none transition-colors',
          'placeholder:text-muted-foreground',
          'file:border-0 file:bg-transparent file:text-sm file:font-medium',
          'disabled:cursor-not-allowed',
          {
            'text-red-500': isError && !props.disabled,
          },
        )}
        ref={ref}
        onChange={(event) => {
          onChange?.(event)
          onChangeValue?.(event.target.value)
        }}
        {...props}
      />

      {clean && value && (
        <IconButton variant="none" size="none" onClick={handleClear}>
          <Icons.X size={16} />
        </IconButton>
      )}

      {RightIcon}
    </div>
  )
}
InputField.displayName = 'Input'

export { InputField }
