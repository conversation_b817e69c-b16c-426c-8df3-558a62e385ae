import { Slot, Slottable } from '@radix-ui/react-slot'
import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'
import type { Url } from 'next/dist/shared/lib/router/router'
import Link from 'next/link'
import * as React from 'react'

import { cn } from '@/lib/utils/cn'

const iconButtonVariants = cva(
  [
    'inline-flex items-center justify-center transition-all whitespace-nowrap rounded-md text-sm font-medium',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
  ],
  {
    variants: {
      variant: {
        none: '',
        default: 'text-gray-700 hover:text-gray-900',
        outline: [
          'border  text-gray-700 ',
          'hover:bg-gray-100 hover:text-gray-900',
        ],
        ghost: ['hover:bg-gray-100 text-gray-700 hover:text-gray-900'],
      },
      size: {
        none: '',
        default: 'size-8 rounded-sm',
        sm: 'size-6 rounded-sm',
        md: 'size-10 rounded-sm',
      },
      sty: {
        default: '',
      },
    },
    compoundVariants: [{ variant: 'outline', sty: 'default', class: '' }],
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
)

export interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof iconButtonVariants> {
  asChild?: boolean
  href?: Url
  target?: React.HTMLAttributeAnchorTarget | undefined
  replace?: boolean | undefined
}

const IconButton = ({
  ref,
  className,
  variant,
  size,
  disabled = false,
  type = 'button',
  asChild = false,
  children,
  href,
  target,
  replace,
  ...props
}: IconButtonProps & { ref?: React.RefObject<HTMLButtonElement> }) => {
  const Comp = asChild ? Slot : 'button'

  const Button = (
    <Comp
      className={cn(
        iconButtonVariants({
          variant,
          size,
          className: cn({}, className),
        }),
      )}
      ref={ref}
      type={type}
      disabled={disabled}
      {...props}
    >
      <Slottable>{children}</Slottable>
    </Comp>
  )

  if (href && !disabled) {
    return (
      <Link href={href} replace={replace} target={target}>
        {Button}
      </Link>
    )
  }

  return <>{Button}</>
}
IconButton.displayName = 'Button'

export { IconButton, iconButtonVariants }
