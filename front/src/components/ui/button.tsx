import { Slot, Slottable } from '@radix-ui/react-slot'
import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'
import type { Url } from 'next/dist/shared/lib/router/router'
import Link from 'next/link'
import * as React from 'react'

import { cn } from '@/lib/utils'

import { Icons } from './icons'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        none: '',
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline:
          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary:
          'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-blue-500 underline-offset-4 hover:underline',
      },
      size: {
        none: '',
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
)

interface IconProps {
  LeftIcon?: React.ReactNode
  RightIcon?: React.ReactNode
}

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  block?: boolean

  href?: Url
  target?: React.HTMLAttributeAnchorTarget | undefined
  replace?: boolean | undefined
}

const Button = ({
  ref,
  className,
  variant,
  size,
  asChild = false,
  loading = false,
  block = false,
  children,
  LeftIcon,
  RightIcon,
  disabled = false,
  type = 'button',
  href,
  target,
  replace,
  ...props
}: ButtonProps & IconProps & { ref?: React.RefObject<HTMLButtonElement> }) => {
  const Comp = asChild ? Slot : 'button'

  const Button = (
    <Comp
      className={cn(
        buttonVariants({
          variant,
          size,
          className: cn(
            {
              'flex w-full flex-1': block,
            },
            className,
          ),
        }),
      )}
      ref={ref}
      type={type}
      disabled={disabled || loading}
      {...props}
    >
      {(LeftIcon || loading) && (
        <div className="mr-1">
          {LeftIcon ? (
            LeftIcon
          ) : loading ? (
            <Icons.Spinner className="size-4 animate-spin" />
          ) : null}
        </div>
      )}
      <Slottable>{children}</Slottable>
      {RightIcon && <div>{RightIcon}</div>}
    </Comp>
  )

  if (href && !disabled) {
    return (
      <LinkButton
        href={href}
        block={block}
        target={target}
        replace={replace}
        className={className}
        variant={variant}
        size={size}
        LeftIcon={LeftIcon}
        RightIcon={RightIcon}
      >
        {children}
      </LinkButton>
    )
  }

  return <>{Button}</>
}
Button.displayName = 'Button'

export type LinkButtonProps = React.ComponentPropsWithoutRef<typeof Link> &
  VariantProps<typeof buttonVariants> &
  IconProps & { block?: boolean; disabled?: boolean }

const LinkButton = ({
  ref,
  className,
  variant,
  size,
  block = false,
  children,
  LeftIcon,
  RightIcon,
  disabled = false,
  ...props
}: LinkButtonProps & { ref?: React.RefObject<HTMLAnchorElement> }) => {
  const Button = (
    <Link
      className={cn(
        buttonVariants({
          variant,
          size,
          className: cn(
            {
              'flex w-full flex-1': block,
            },
            className,
          ),
        }),
      )}
      data-disabled={disabled}
      ref={ref}
      {...props}
    >
      {LeftIcon && <div className="mr-1">{LeftIcon ? LeftIcon : null}</div>}
      <Slottable>{children}</Slottable>
      {RightIcon && <div>{RightIcon}</div>}
    </Link>
  )

  return <>{Button}</>
}
LinkButton.displayName = 'LinkButton'

export { Button, buttonVariants, LinkButton }
