import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'
import type { Url } from 'next/dist/shared/lib/router/router'
import Link from 'next/link'
import * as React from 'react'

import { cn } from '@/lib/utils/cn'

const textLinkVariants = cva(['text-interactive-link'], {
  variants: {
    variant: {
      underline: ['underline decoration-from-font underline-offset-2', ''],
      text: '',
    },
  },
  defaultVariants: {
    variant: 'underline',
  },
})

export interface TestLinkProps
  extends Omit<React.ComponentProps<typeof Link>, 'href'>,
    VariantProps<typeof textLinkVariants> {
  href?: Url
}

const TextLink = ({
  className,
  variant,
  href,
  children,
  ...props
}: React.PropsWithChildren<TestLinkProps>) => {
  if (href) {
    return (
      <Link
        className={cn(
          textLinkVariants({
            variant,
            className: cn('', className),
          }),
        )}
        href={href}
        {...props}
      >
        {children}
      </Link>
    )
  }

  return (
    <span
      className={cn(
        textLinkVariants({
          variant,
          className: cn('', className),
        }),
      )}
      {...props}
    >
      {children}
    </span>
  )
}

TextLink.displayName = 'TextLink'

export { TextLink, textLinkVariants }
