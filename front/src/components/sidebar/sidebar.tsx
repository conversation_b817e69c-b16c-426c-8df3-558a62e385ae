'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useParams, usePathname } from 'next/navigation'
import type {JSX} from 'react';
import { useState} from 'react'

import { Button } from '@/components/ui'
import { Icons } from '@/components/ui/icons'
import {BittensorWalletConnect} from '@/components/web3/bittensor-wallet-connect'
import { cn } from '@/lib/utils'

interface SidebarProps {
  className?: string
  isMobile?: boolean
}

type NavItem = {
  label: string
  href: string
  icon: React.ReactNode
}

export function Sidebar({ className, isMobile = false }: SidebarProps) {
  const pathname = usePathname()
  const params = useParams()
  const locale = params.locale as string
  const [isOpen, setIsOpen] = useState(!isMobile)

  const navItems: ({ icon: JSX.Element; label: string; href: string } | {
    icon: JSX.Element;
    label: string;
    href: string
  } | { icon: JSX.Element; label: string; href: string } | { icon: JSX.Element; label: string; href: string })[] = [
    {
      label: 'Dashboard',
      href: `/${locale}/dashboard`,
      icon: <img src={"/images/dashboard-home.svg"} className="size-5" />,
    },
    {
      label: 'Miner',
      href: `/${locale}/miner`,
      icon:  <img src={"/images/miner-icon.svg"} className="size-5" />,
    },
    {
      label: 'Validator',
      href: `/${locale}/validator`,
      icon:  <img src={"/images/validator-icon.svg"} className="size-5" />,
    },
    {
      label: 'Stake',
      href: `/${locale}/stake`,
      icon:  <img src={"/images/state-icon.svg"} className="size-5" />,
    },
  ]

  const handleToggle = () => {
    setIsOpen(!isOpen)
  }

  return (
    <aside
      className={cn(
        'flex h-full flex-col border-r border-[#E8E6DC] bg-[#F7F5EE] pb-4 pt-3 transition-all',
        isMobile
          ? isOpen
            ? 'w-[256px] px-2'
            : 'w-[70px] px-1'
          : isOpen
            ? 'w-[256px] px-2'
            : 'w-[70px] px-1',
        className
      )}
    >
      {/* Logo section */}
      <div className="flex h-16 items-center justify-between px-4">
        <Link href={`/${locale}`} className="flex items-center gap-2">
          <Image
            src={isOpen ? "/images/design/dogemine-logo.svg" : "/images/dogemine-logo.svg"}
            alt="Dogemine Logo"
            width={100}
            height={40}
            priority
            className="transition-all duration-300"
          />
        </Link>
        {isOpen && (
          <Button variant="ghost" size="icon" onClick={handleToggle} className="ml-auto">
            <img src={"/images/avatar-placeholder.svg"} className="size-4" />
          </Button>
        )}
        {!isOpen && (
          <Button variant="ghost" size="icon" onClick={handleToggle} className="ml-auto">
            <Icons.ChevronRight className="size-4" />
          </Button>
        )}
      </div>

      {/* Navigation links */}
      <nav className="flex flex-1 flex-col gap-1 p-2">
        {navItems.map((item) => {
          const isActive = pathname.startsWith(item.href)

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex h-10 items-center rounded-lg px-3 text-gray-600 transition-colors hover:bg-[#F0EEE6] dark:text-gray-300 dark:hover:bg-[#F0EEE6]',
                isActive && 'bg-[#F0EEE6] font-medium text-primary dark:bg-[#F0EEE6] dark:text-primary',
                isOpen ? 'justify-start' : 'justify-center'
              )}
            >
              <div className={cn('flex items-center gap-3', isOpen ? 'w-full' : '')}>
                {item.icon}
                {isOpen && <span>{item.label}</span>}
              </div>
            </Link>
          )
        })}
      </nav>

      {/* Web3 wallet connect button */}
      <div className="relative mt-auto border-t bg-white/50 p-2">
        <BittensorWalletConnect
          isCollapsed={!isOpen}
          className="wallet-connect-wrapper"
        />
      </div>
    </aside>
  )
}
