'use client'

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import {
  Grid<PERSON>omponent,
  LegendComponent,
  TooltipComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { UniversalTransition } from 'echarts/features'
import { Canvas<PERSON>enderer } from 'echarts/renderers'
import type { EChartsReactProps } from 'echarts-for-react'
import ReactEChartsCore from 'echarts-for-react/lib/core'
import { cloneDeep, merge } from 'lodash-es'
import { useMemo } from 'react'

import { cn } from '@/lib/utils'

type EChartsProps = Omit<EChartsReactProps, 'echarts'> & {}

echarts.use([
  TooltipComponent,
  GridComponent,
  CanvasRenderer,
  UniversalTransition,
  LineChart,
  BarChart,
  PieChart,
  LegendComponent,
])

const EChartOption: echarts.EChartsCoreOption = {
  // series: [
  //   {
  //     showSymbol: false,
  //     symbol: 'none',
  //     smooth: true,
  //     lineStyle: {
  //       color: '#ffffff',
  //     },
  //   },
  // ],
  // tooltip: {
  //   confine: true,
  //   trigger: 'axis',
  //   backgroundColor: '#23262f',
  //   borderColor: '#23262f',
  //   borderRadius: 8,
  //   padding: 10,
  //   textStyle: {
  //     fontSize: 12,
  //     color: '#ffffff',
  //   },
  // },
  // xAxis: {
  //   type: 'category',
  //   axisTick: {
  //     lineStyle: {
  //       color: '#ffffff',
  //     },
  //   },
  //   axisLine: {
  //     lineStyle: {
  //       color: '#ffffff',
  //     },
  //   },
  //   axisLabel: {
  //     interval: 0,
  //     color: '#ffffff',
  //   },
  // },
  // yAxis: {
  //   type: 'value',
  //   axisLabel: {
  //     color: '#ffffff',
  //   },
  //   splitLine: {
  //     lineStyle: {
  //       type: 'dashed',
  //       color: 'rgba(255, 255, 255, 0.3)',
  //     },
  //   },
  // },
  // grid: {
  //   top: '20px',
  //   left: '16px',
  //   right: '16px',
  //   bottom: '20px',
  //   containLabel: true,
  // },
}

export function ECharts({ option, className, ...props }: EChartsProps) {
  const mergedOption = useMemo(
    () => merge(cloneDeep(EChartOption), option),
    [option],
  )

  return (
    <ReactEChartsCore
      echarts={echarts}
      option={mergedOption}
      className={cn('!h-full', className)}
      {...props}
    />
  )
}
