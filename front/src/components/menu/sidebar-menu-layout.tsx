'use client'

import type { MenuGroup } from './menu'

interface SidebarMenuLayoutProps {
  children: React.ReactNode
  menuList: MenuGroup[]
}

export function SidebarMenuLayout({ children, menuList }: SidebarMenuLayoutProps) {
  return (
    <div className="flex min-h-screen">
      <aside className="w-64 border-r bg-gray-50">
        <nav className="p-4">
          {menuList.map((group, index) => (
            <div key={index}>
              {group.groupLabel && (
                <h3 className="mb-2 text-sm font-semibold text-gray-500">
                  {group.groupLabel}
                </h3>
              )}
              <ul className="space-y-1">
                {group.menus.map((menu, menuIndex) => (
                  <li key={menuIndex}>
                    <a
                      href={menu.href}
                      className={`flex items-center gap-2 rounded-md px-3 py-2 text-sm ${
                        menu.active 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {menu.icon && <menu.icon size={16} />}
                      {menu.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </nav>
      </aside>
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
} 