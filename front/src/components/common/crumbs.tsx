'use client'

import Link from 'next/link'

export interface CrumbItem {
  label: string
  href?: string
}

interface CrumbsProps {
  items: CrumbItem[]
  className?: string
}

export function Crumbs({ items, className }: CrumbsProps) {
  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`}>
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            <span className="mx-2 text-gray-400">/</span>
          )}
          {item.href ? (
            <Link 
              href={item.href}
              className="text-blue-600 hover:text-blue-800"
            >
              {item.label}
            </Link>
          ) : (
            <span className="text-gray-600">
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  )
} 