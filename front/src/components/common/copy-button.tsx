'use client'

import { useState } from 'react'

import { Button } from '@/components/ui/button'
import { Icons } from '@/components/ui/icons'
import { useSafeTranslation } from '@/lib/use-safe-translation'

interface CopyButtonProps {
  text: string
  className?: string
}

export function CopyButton({ text, className }: CopyButtonProps) {
  const [copied, setCopied] = useState(false)
  const { t } = useSafeTranslation()

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text:', err)
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleCopy}
      className={className}
    >
      {copied ? (
        <Icons.Check className="size-4" />
      ) : (
        <Icons.Clipboard className="size-4" />
      )}
      {copied ? t('components.copyButton.copied', 'Copied') : t('components.copyButton.copy', 'Copy')}
    </Button>
  )
} 