'use client'

import { Globe } from 'lucide-react'
import { usePathname, useRouter } from 'next/navigation'
import * as React from 'react'
import { useTranslation } from 'react-i18next'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import type { Locale } from '@/constants'
import { LOCALE_LABEL_MAP, LOCALES } from '@/constants'
import { useSafeTranslation } from '@/lib/use-safe-translation'

export function LanguageChanger() {
  const router = useRouter()
  const currentPathname = usePathname()
  const { i18n } = useTranslation()
  const { t } = useSafeTranslation()
  const currentLocale = i18n.language as Locale

  function handleChange(newLocale: Locale) {
    // Change language in i18next
    i18n.changeLanguage(newLocale)

    // Update URL
    const segments = currentPathname.split('/')
    segments[1] = newLocale // Replace locale segment
    const newPath = segments.join('/')

    router.push(newPath)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Globe className="size-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90" />

          <span className="sr-only">{t('components.languageChanger.changeLanguage', 'Change language')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {LOCALES.map((locale) => {
          return (
            <DropdownMenuCheckboxItem
              key={locale}
              checked={currentLocale === locale}
              onClick={() => handleChange(locale)}
            >
              {LOCALE_LABEL_MAP[locale]}
            </DropdownMenuCheckboxItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
