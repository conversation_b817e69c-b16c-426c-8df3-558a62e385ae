'use client'

import 'react-toastify/dist/ReactToastify.css'
import './toastify.css'

import type { ToastContainerProps } from 'react-toastify'
import { Slide, ToastContainer } from 'react-toastify'

const ToastifyContainer = ({
  toastClassName,
  ...props
}: ToastContainerProps) => {
  return (
    <ToastContainer
      position="top-right"
      autoClose={5e3}
      newestOnTop={false}
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable={false}
      pauseOnHover
      transition={Slide}
      {...props}
    />
  )
}

export { ToastifyContainer }

export { toast as toastify } from 'react-toastify'
