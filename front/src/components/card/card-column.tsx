'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'

export interface CardColumnProps {
  title: string
  description?: string
  value: string | React.ReactNode
  action?: React.ReactNode
  className?: string
}

export function CardColumn({ 
  title, 
  description, 
  value, 
  action, 
  className 
}: CardColumnProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {description && (
          <p className="text-xs text-muted-foreground">
            {description}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="text-lg font-semibold">
            {value}
          </div>
          {action && (
            <div>
              {action}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 