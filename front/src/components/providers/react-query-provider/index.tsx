import { QueryClientProvider } from '@tanstack/react-query'
import type { PropsWithChildren } from 'react'

import { getQueryClient } from './get-query-client'

export const ReactQueryProvider = ({ children }: PropsWithChildren) => {
  const queryClient = getQueryClient()

  return (
    <QueryClientProvider client={queryClient}>
      {children}

      {/*<ReactQueryDevtools />*/}
    </QueryClientProvider>
  )
}
