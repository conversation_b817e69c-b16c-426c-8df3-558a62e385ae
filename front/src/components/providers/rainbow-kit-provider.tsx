'use client'

import '@rainbow-me/rainbowkit/styles.css'

import { getDefaultConfig, RainbowKitProvider } from '@rainbow-me/rainbowkit'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import type { ReactNode } from 'react'
import { define<PERSON>hain } from 'viem'
import { WagmiProvider } from 'wagmi'
import { arbitrum, base, mainnet, optimism, polygon } from 'wagmi/chains'

// 创建查询客户端
const queryClient = new QueryClient()

// 定义 Bittensor Subtensor EVM 链
const bittensorEVM = defineChain({
  id: 945,
  name: 'Subtensor EVM',
  nativeCurrency: {
    decimals: 18,
    name: 'TA<PERSON>',
    symbol: 'TAO',
  },
  rpcUrls: {
    default: {
      http: ['https://test.chain.opentensor.ai'],
      webSocket: ['wss://test.chain.opentensor.ai'],
    },
  },
  blockExplorers: {
    default: {
      name: 'TaoStats',
      url: 'https://taostats.io/subnets',
    },
  },
  testnet: true, // 目前是测试网
})

// 设置 RainbowKit 配置
const config = getDefaultConfig({
  appName: 'DogeMine',
  projectId: 'eae837d97edabd80c15d21e2a343220e',
  chains: [mainnet, polygon, optimism, arbitrum, base, bittensorEVM],
  ssr: true,
})

interface RainbowKitWrapperProps {
  children: ReactNode
}

export function RainbowKitWrapper({ children }: RainbowKitWrapperProps) {
  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <RainbowKitProvider>
          {children}
        </RainbowKitProvider>
      </QueryClientProvider>
    </WagmiProvider>
  )
} 