'use client'

import { Provider as NiceModalProvider } from '@ebay/nice-modal-react'
import type { PropsWithChildren } from 'react'

import { I18nProvider } from './i18n-provider'
import { JotaiStoreProvider } from './jotai-provider'
import { ThemeProvider } from './next-themes-provider'
import { RainbowKitWrapper } from './rainbow-kit-provider'
import { ReactQueryProvider } from './react-query-provider'

export type ProvidersProps = PropsWithChildren<{
  locale?: string
}>

export function Providers({ children, locale }: ProvidersProps) {
  return (
    <>
      <I18nProvider locale={locale}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          forcedTheme="light"
        >
          <RainbowKitWrapper>
            <ReactQueryProvider>
              <JotaiStoreProvider>
                <NiceModalProvider>
                  {children}
                </NiceModalProvider>
              </JotaiStoreProvider>
            </ReactQueryProvider>
          </RainbowKitWrapper>
        </ThemeProvider>
      </I18nProvider>
    </>
  )
}
