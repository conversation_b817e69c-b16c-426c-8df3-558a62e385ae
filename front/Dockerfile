# Stage 1: Build application from source
FROM coinflow/bun-base AS builder
WORKDIR /app

# 复制依赖文件（缓存关键点）
COPY package.json bun.lock ./

# 安装依赖（仅在依赖改变时重新运行）
RUN bun install --frozen-lockfile

# 复制源码（依赖层已缓存）
COPY . .

# 构建应用
RUN bun run build

# Stage 2: Production image
FROM coinflow/bun-base
WORKDIR /app

# 复制构建产物和静态文件
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME=0.0.0.0

# 启动应用
CMD ["bun", "run", "server.js"]
