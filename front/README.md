# Dogemine Mining Pool Interface

A modern, responsive web interface for the world's first mining pool that enables Scrypt miners to join Bittensor subnet. Built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Triple Mining Technology**: Mine LTC/DOGE while earning Alpha tokens through subnet validation
- **300% ROI Boost**: Increase your mining profitability per kWh
- **Modern UI/UX**: Clean, responsive design based on professional Figma designs
- **Internationalization**: Support for English and Simplified Chinese
- **Real-time Analytics**: Live mining statistics and earnings tracking
- **Mobile-First**: Optimized for all device sizes

## 🛠 Technology Stack

- **Framework**: [Next.js 15.3.5](https://nextjs.org) with App Router
- **Language**: [TypeScript 5](https://www.typescriptlang.org)
- **Styling**: [Tailwind CSS 3.4.17](https://tailwindcss.com)
- **Package Manager**: [Bun 1.2.18](https://bun.sh)
- **State Management**: [<PERSON><PERSON>](https://jotai.org) + [React Query](https://tanstack.com/query)
- **UI Components**: [Radix UI](https://www.radix-ui.com) + Custom Components
- **Internationalization**: [react-i18next](https://react.i18next.com)
- **Charts**: [ECharts](https://echarts.apache.org)
- **Icons**: [Lucide React](https://lucide.dev)

## 📦 Installation

### Prerequisites

- Node.js 20 or higher
- Bun 1.2.18 or higher

### Setup

1. Clone the repository:
```bash1
git clone <repository-url>
cd mining-pool-interface
```

2. Install dependencies:
```bash
bun install
```

3. Start the development server:
```bash
bun run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗 Project Structure

```
src/
├── app/[locale]/          # App Router pages with internationalization
│   ├── _comps/           # Page-specific components
│   ├── _layout/          # Layout components (header, footer)
│   ├── assets/           # Assets management pages
│   ├── earnings/         # Earnings tracking pages
│   ├── login/            # Authentication pages
│   ├── pool/             # Pool statistics pages
│   ├── setting/          # User settings pages
│   └── workers/          # Worker management pages
├── components/           # Reusable components
│   ├── ui/              # UI component library
│   ├── charts/          # Chart components
│   └── providers/       # Context providers
├── constants/           # Application constants
├── lib/                # Utility functions
├── locales/            # Internationalization files
└── styles/             # Global styles
```

## 🎨 Design System

The project follows a consistent design system with:

- **Colors**: Custom color palette with CSS variables
- **Typography**: Inter font family with responsive sizing
- **Spacing**: Consistent spacing scale using Tailwind
- **Components**: Reusable UI components based on Radix UI
- **Icons**: Lucide React icon library

## 🌍 Internationalization

The application supports:
- **English** (default)
- **Simplified Chinese**

To add new translations:
1. Update translation files in `src/locales/en.json` and `src/locales/zh-CN.json`
2. Use the `useTranslation` hook in components: `const { t } = useTranslation()`
3. Access translations with `t('key.path')`

## 📝 Available Scripts

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run start` - Start production server
- `bun run lint` - Run ESLint
- `bun run lint:fix` - Fix ESLint issues
- `bun run check:type` - Type checking
- `bun run lingui:extract` - Extract translation messages

## 🔧 Development Guidelines

### Code Style
- Use TypeScript for all new files
- Follow ESLint configuration
- Use Prettier for formatting
- Prefer functional components with hooks

### Component Guidelines
- Use React Server Components by default
- Add 'use client' only when necessary
- Export as named exports
- Use TypeScript interfaces for props

### State Management
- Use Jotai for client-side state
- Use React Query for server state
- Keep state close to usage

### Styling
- Use Tailwind CSS utility classes
- Follow mobile-first responsive design
- Use CSS variables for theming
- Prefer composition over custom CSS

## 🚀 Deployment

### Build for Production

```bash
bun run build
```

### Environment Variables

Create a `.env.local` file with required environment variables:

```env
# Add your environment variables here
NEXT_PUBLIC_API_URL=your_api_url
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

Built with ❤️ for the Dogemine community





docker run -d --rm --name dogemine-web \
  -p 8101:3000 \
  -v $(pwd)/data:/app/data \
  coinflow/dogemine-web:latest