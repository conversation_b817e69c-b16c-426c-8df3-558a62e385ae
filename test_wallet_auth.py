#!/usr/bin/env python3
"""
钱包认证 API 测试脚本
"""
import asyncio
import json
import httpx

API_BASE_URL = "http://localhost:8000"

async def test_wallet_auth_flow():
    """测试完整的钱包认证流程"""
    
    print("🧪 测试钱包认证 API 流程")
    
    # 测试地址 (示例)
    test_address = "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
    test_signature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12"
    
    async with httpx.AsyncClient() as client:
        try:
            # 步骤 1: 获取 nonce
            print("\n📋 步骤 1: 获取登录随机数")
            nonce_response = await client.post(
                f"{API_BASE_URL}/accounts/nonce",
                json={"address": test_address},
                headers={"Content-Type": "application/json"}
            )
            
            print(f"状态码: {nonce_response.status_code}")
            nonce_data = nonce_response.json()
            print(f"响应: {json.dumps(nonce_data, indent=2, ensure_ascii=False)}")
            
            if not nonce_data.get("success"):
                print("❌ 获取 nonce 失败")
                return
            
            nonce_message = nonce_data["nonce"]
            print("✅ 获取 nonce 成功")
            
            # 步骤 2: 钱包认证
            print("\n🔐 步骤 2: 钱包认证")
            auth_response = await client.post(
                f"{API_BASE_URL}/accounts/auth",
                json={
                    "address": test_address,
                    "signature": test_signature,
                    "message": nonce_message,
                    "source": "polkadot-js"
                },
                headers={"Content-Type": "application/json"}
            )
            
            print(f"状态码: {auth_response.status_code}")
            auth_data = auth_response.json()
            print(f"响应: {json.dumps(auth_data, indent=2, ensure_ascii=False)}")
            
            if not auth_data.get("success"):
                print("❌ 钱包认证失败")
                return
            
            token = auth_data["token"]
            print("✅ 钱包认证成功")
            
            # 步骤 3: 测试认证 token
            print("\n🎫 步骤 3: 测试认证 Token")
            protected_response = await client.get(
                f"{API_BASE_URL}/dashboard/overview",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            print(f"状态码: {protected_response.status_code}")
            if protected_response.status_code == 200:
                print("✅ Token 验证成功")
                dashboard_data = protected_response.json()
                print(f"仪表板数据: {json.dumps(dashboard_data, indent=2, ensure_ascii=False)}")
            elif protected_response.status_code == 401:
                print("❌ Token 验证失败 - 未授权")
            else:
                print(f"⚠️ 意外的响应状态: {protected_response.status_code}")
                print(f"响应内容: {protected_response.text}")
            
            # 步骤 4: 测试断开连接
            print("\n🔌 步骤 4: 测试断开连接")
            disconnect_response = await client.post(
                f"{API_BASE_URL}/accounts/disconnect",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            print(f"状态码: {disconnect_response.status_code}")
            disconnect_data = disconnect_response.json()
            print(f"响应: {json.dumps(disconnect_data, indent=2, ensure_ascii=False)}")
            
            if disconnect_data.get("success"):
                print("✅ 断开连接成功")
            else:
                print("❌ 断开连接失败")
                
        except httpx.ConnectError:
            print("❌ 无法连接到 API 服务器")
            print("请确保 API 服务器正在运行:")
            print("cd hash && python api_server.py")
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")

async def test_invalid_scenarios():
    """测试无效场景"""
    
    print("\n🧪 测试无效场景")
    
    async with httpx.AsyncClient() as client:
        try:
            # 测试无效地址
            print("\n📋 测试: 无效地址")
            response = await client.post(
                f"{API_BASE_URL}/accounts/nonce",
                json={"address": "invalid_address"},
                headers={"Content-Type": "application/json"}
            )
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            
            # 测试无效 token
            print("\n🎫 测试: 无效 Token")
            response = await client.get(
                f"{API_BASE_URL}/dashboard/overview",
                headers={"Authorization": "Bearer invalid_token"}
            )
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    print("🚀 开始钱包认证 API 测试")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_wallet_auth_flow())
    asyncio.run(test_invalid_scenarios())
    
    print("\n" + "=" * 50)
    print("✅ 测试完成!")
    print("\n📖 使用指南:")
    print("1. 启动 API 服务器: cd hash && python api_server.py")
    print("2. 前端集成请参考: hash/docs/wallet_auth_guide.md")
    print("3. 生产环境请实现真正的签名验证逻辑")