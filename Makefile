# TAO Hash Project Makefile
# 项目管理和测试命令

.PHONY: help install test test-unit test-integration test-api clean setup-dev docker-up docker-down

# 默认目标
help:
	@echo "TAO Hash Project Commands:"
	@echo ""
	@echo "Development:"
	@echo "  install          - Install project dependencies"
	@echo "  setup-dev        - Setup development environment"
	@echo "  clean            - Clean build artifacts and cache"
	@echo ""
	@echo "Testing:"
	@echo "  test             - Run all tests"
	@echo "  test-unit        - Run unit tests only"
	@echo "  test-integration - Run integration tests with Docker"
	@echo "  test-api         - Run API tests"
	@echo ""
	@echo "Docker:"
	@echo "  docker-up        - Start Docker services for testing"
	@echo "  docker-down      - Stop Docker services"
	@echo ""
	@echo "API Server:"
	@echo "  api-start        - Start API server"
	@echo "  api-test         - Test API endpoints"

# 安装依赖
install:
	@echo "📦 Installing dependencies..."
	cd hash && source venv/bin/activate && python -m pip install -e .[api,dev]
	@echo "✅ Dependencies installed"

# 设置开发环境
setup-dev:
	@echo "🔧 Setting up development environment..."
	cd hash && python3 -m venv venv
	@echo "📦 Installing dependencies..."
	cd hash && source venv/bin/activate && python -m pip install -e .[api,dev]
	@echo "✅ Development environment ready"
	@echo "💡 Activate with: cd hash && source venv/bin/activate"

# 清理
clean:
	@echo "🧹 Cleaning build artifacts..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@echo "✅ Cleaned"

# 启动Docker服务
docker-up:
	@echo "🐳 Starting Docker services..."
	cd hash/tests/integration_tests && docker-compose up -d
	@echo "⏳ Waiting for services to be ready..."
	sleep 10
	@echo "✅ Docker services started"

# 停止Docker服务
docker-down:
	@echo "🐳 Stopping Docker services..."
	cd hash/tests/integration_tests && docker-compose down
	@echo "✅ Docker services stopped"

# 运行单元测试
test-unit:
	@echo "🧪 Running unit tests..."
	cd hash && source venv/bin/activate && python -m pytest tests/unit_tests/ -v
	@echo "✅ Unit tests completed"

# 运行集成测试（包含Docker启动/停止）
test-integration:
	@echo "🧪 Running integration tests with Docker..."
	@$(MAKE) docker-up
	@echo "🧪 Running tests..."
	cd hash && source venv/bin/activate && python -m pytest tests/integration_tests/ -v --tb=short || ($(MAKE) docker-down && exit 1)
	@$(MAKE) docker-down
	@echo "✅ Integration tests completed"

# 运行集成测试（不启动Docker）
test-integration-no-docker:
	@echo "🧪 Running integration tests without Docker..."
	cd hash && source venv/bin/activate && python run_tests.py integration --no-docker
	@echo "✅ Integration tests completed"

# 运行API测试（自动启动/停止服务器）
test-api:
	@echo "🧪 Running API tests with server management..."
	cd hash && source venv/bin/activate && python run_tests.py api
	@echo "✅ API tests completed"

# 运行所有测试
test: test-unit test-integration test-api

# 启动API服务器
api-start:
	@echo "🚀 Starting API server..."
	cd hash && source venv/bin/activate && python start_api.py

# 快速API测试（不启动Docker）
api-test-quick:
	@echo "🧪 Running quick API tests..."
	cd hash && source venv/bin/activate && python -c "import subprocess; import time; \
	p = subprocess.Popen(['python', 'start_api.py']); \
	time.sleep(3); \
	subprocess.run(['python', 'test_api.py']); \
	p.terminate()"

# 开发模式：启动API并运行测试
dev-test:
	@echo "🔧 Development test mode..."
	@$(MAKE) docker-up
	@echo "🚀 Starting API server in background..."
	cd hash && python start_api.py &
	@echo "⏳ Waiting for API server..."
	sleep 5
	@echo "🧪 Running integration tests..."
	cd hash && python -m pytest tests/integration_tests/test_api_auth.py -v --tb=short || true
	@echo "🛑 Stopping services..."
	pkill -f "start_api.py" || true
	@$(MAKE) docker-down
	@echo "✅ Development test completed"
