
#### 接口设计

##### 1、首页

###### 1、获取仪表盘数据

根据用户ID获取矿池首页仪表盘的各项统计数据，包括算力、收益、活跃矿工数等。

 **请求：**

- URL: /api/dashboard
- 方法: GET
- 请求参数: userId

 **返回值JSON：**

```JSON
{
  "hashrate_24h": "115.39 Ph/s",         // 24小时算力
  "shares_24h": "2.31T sh",              // 24小时份额
  "hashrate_1h": "62.38 Ph/s",           // 1小时算力
  "shares_1h": "52.29B sh",              // 1小时份额
  "total_staked": "115.39 Ph/s",         // 总质押
  "xx_price": "115.39 Ph/s",             // xx价格（如有具体含义请补充）
  "tao_earnings": "115.39 Ph/s",         // TAO收益
  "doge_earnings": "115.39 Ph/s",        // Doge收益
  "ltc_earnings": "115.39 Ph/s",         // LTC收益
  "active_miners": 158                   // 活跃矿工数
}
```

 **字段说明**

| 字段名        | 类型   | 说明           |
| ------------- | ------ | -------------- |
| hashrate_24h  | string | 24小时平均算力 |
| shares_24h    | string | 24小时份额     |
| hashrate_1h   | string | 1小时平均算力  |
| shares_1h     | string | 1小时份额      |
| total_staked  | string | 总质押         |
| xx_price      | string | xx价格         |
| tao_earnings  | string | TAO收益        |
| doge_earnings | string | Doge收益       |
| ltc_earnings  | string | LTC收益        |
| active_miners | int    | 活跃矿工数     |

###### 2、获取算力历史数据

 获取矿池算力历史曲线数据，用于折线图展示。

 **请求**

- URL: /api/hashrate/history
- 方法: GET
- 请求参数:

| 参数名 | 类型   | 必填 | 说明     | 示例值                |
| ------ | ------ | ---- | -------- | --------------------- |
| range  | string | 否   | 时间范围 | 24h/7d/30d（默认24h） |

 **返回值JSON：**

```JSON
{
  "range": "24h",
  "unit": "Ph/s",
  "data": [
    {"timestamp": 1719194400, "hashrate": 800.00},
    {"timestamp": 1719201600, "hashrate": 600.00},
    // ... 省略部分数据
    {"timestamp": 1719223200, "hashrate": 400.00}
  ]
}
```

 **字段说明**

| 字段名    | 类型   | 说明         |
| --------- | ------ | ------------ |
| range     | string | 时间范围     |
| unit      | string | 单位         |
| data      | array  | 历史数据数组 |
| timestamp | int    | 时间戳（秒） |
| hashrate  | float  | 算力值       |

###### 3、获取钱包列表

 获取当前用户所有可用的钱包账户及其来源（如 talisman、polkadot、扩展插件等）。

 **请求**

- URL: /api/accounts
- 方法: GET
- 请求参数: 无

**返回值JSON**

```JSON
{
  "accounts": [
    {
      "name": "ccbaka",
      "source": "talisman",
      "address": "5Gnzm...cbsul"
    },
    {
      "name": "ccbaka",
      "source": "@opentensor/bittensor extension",
      "address": "5lams...vjius",
      "selected": true
    },
    {
      "name": "ccbaka",
      "source": "polkadot",
      "address": "5a02k...gsoo2"
    }
  ]
}
```

###### 4、切换当前帐户

  切换当前使用的钱包账户。

  **请求**

- URL: /api/accounts/select
- 方法: POST
- 请求参数:

**返回值JSON**

```JSON
{
  "success": true,
  "selected_account": {
    "name": "ccbaka",
    "source": "@opentensor/bittensor extension",
    "address": "5lams...vjius"
  }
}
```

###### 5、断开连接

 断开当前钱包连接。

  请求

- URL: /api/accounts/disconnect
- 方法: POST
- 请求参数: 无

 返回值JSON

```Plain
{
  "success": true
}
```

##### 2、矿工界面

######  1、获取矿工列表

  获取当前账户下所有活跃矿工的算力、质押、得分和收益等信息。

  **请求**

- URL: /api/miners
- 方法: GET
- 请求参数:

| 参数名   | 类型   | 必填 | 说明               | 示例值 |
| -------- | ------ | ---- | ------------------ | ------ |
| search   | string | 否   | 按矿工地址模糊搜索 | 5Ee... |
| page     | int    | 否   | 页码               | 1      |
| pageSize | int    | 否   | 每页数量           | 10     |

 **返回值JSON**

```JSON
{
  "total": 6,
  "miners": [
    {
      "address": "5EewuHxax...",
      "status": "Active",
      "hashrate_1h": "320.05 Ph/s",
      "hashrate_24h": "320.05 Ph/s",
      "shares_1h": "66.78B",
      "shares_24h": "66.78B",
      "node": "node1",
      "stake": "500 TAO",
      "score": 98,
      "earning": "100 xxx"
    }
    // ... 其余矿工
  ]
}
```

 **字段说明**

| 字段名       | 类型   | 说明             |
| ------------ | ------ | ---------------- |
| total        | int    | 矿工总数         |
| miners       | array  | 矿工信息列表     |
| address      | string | 矿工地址         |
| status       | string | 状态（Active等） |
| hashrate_1h  | string | 1小时算力        |
| hashrate_24h | string | 24小时算力       |
| shares_1h    | string | 1小时份额        |
| shares_24h   | string | 24小时份额       |
| node         | string | 节点名称         |
| stake        | string | 质押数量         |
| score        | int    | 得分             |
| earning      | string | 收益             |

######  2、获取指定矿工信息

 获取指定矿工的实时算力、份额、历史数据等详细信息。

  **请求**

- URL: /api/miners/{address}
- 方法: GET
- 请求参数: 无

  **返回值JSON**

```JSON
{
  "address": "5dhds...k9a",
  "status": "Active",
  "node": "node1",
  "hashrate_5m": "$0.00/Hour",
  "hashrate_1h": "$0.00/Hour",
  "hashrate_24h": "$0.00/Hour",
  "hashrate_max": "$0.00/Hour",
  "shares_5m": "3.19T sh",
  "shares_1h": "3.19T sh",
  "shares_24h": "3.19T sh",
  "shares_total": "3.19T sh",
  "hashrate_history": [
    {"timestamp": 1719194400, "hashrate": 800.00},
    {"timestamp": 1719201600, "hashrate": 600.00}
    // ... 省略部分数据
  ],
  "shares_history": [
    {"timestamp": 1719194400, "shares": 400.00},
    {"timestamp": 1719201600, "shares": 800.00}
    // ... 省略部分数据
  ]
}
```

 **字段说明**

| 字段名           | 类型   | 说明             |
| ---------------- | ------ | ---------------- |
| address          | string | 矿工地址         |
| status           | string | 状态（Active等） |
| node             | string | 节点名称         |
| hashrate_5m      | string | 5分钟平均算力    |
| hashrate_1h      | string | 1小时平均算力    |
| hashrate_24h     | string | 24小时平均算力   |
| hashrate_max     | string | 最大算力         |
| shares_5m        | string | 5分钟份额        |
| shares_1h        | string | 1小时份额        |
| shares_24h       | string | 24小时份额       |
| shares_total     | string | 总份额           |
| hashrate_history | array  | 算力历史数据     |
| shares_history   | array  | 份额历史数据     |
| timestamp        | int    | 时间戳（秒）     |
| hashrate         | float  | 算力值           |
| shares           | float  | 份额值           |

######  3、获取可用节点

 获取当前可用的所有节点信息，用于节点切换下拉选择。

  **请求**

- URL: /api/nodes
- 方法: GET
- 请求参数: 无

  **返回值JSON**

```JSON
{
  "nodes": [
    {
      "name": "Node1",
      "url": "http://80bliu.axshare.com/?id=29r9ur矿工_T_en&g..."
    },
    {
      "name": "Node2",
      "url": "http://80bliu.axshare.com/?id=29r9ur矿工_T_en&g..."
    },
    {
      "name": "Node3",
      "url": "http://80bliu.axshare.com/?id=29r9ur矿工_T_en&g..."
    },
    {
      "name": "Node4",
      "url": "http://80bliu.axshare.com/?id=29r9ur矿工_T_en&g..."
    }
  ]
}
```

 **字段说明**

| 字段名 | 类型   | 说明     |
| ------ | ------ | -------- |
| name   | string | 节点名称 |
| url    | string | 节点地址 |

######  4、切换节点

 将指定矿工从当前节点切换到目标节点。

  **请求**

- URL: /api/miners/{address}/change-node
- 方法: POST
- 请求参数:

| 参数名    | 类型   | 必填 | 说明         |
| --------- | ------ | ---- | ------------ |
| address   | string | 是   | 矿工地址     |
| from_node | string | 是   | 当前节点名称 |
| to_node   | string | 是   | 目标节点名称 |

**返回值JSON**

```JSON
{
  "success": true,
  "message": "节点切换成功",
  "miner": {
    "address": "5dhds...k9a",
    "current_node": "Node4"
  }
}
```

######  5、获取矿工收益总览

 获取当前账户下所有矿工的今日、累计收益等统计信息。

  **请求**

- URL: /api/earnings/overview
- 方法: GET
- 请求参数: 无

**返回值JSON**

```Plain
{
  "today_rewards": "158.80 USD",
  "total_rewards": "158.80 USD"
}
```

 **字段说明**

| 字段名        | 类型   | 说明     |
| ------------- | ------ | -------- |
| today_rewards | string | 今日收益 |
| total_rewards | string | 累计收益 |

######  6、获取矿工收益列表

 获取所有矿工的收益明细。

  **请求**

- URL: /api/earnings/workers
- 方法: GET
- 请求参数: 无

**返回值JSON**

```JSON
{
  "workers": [
    {
      "address": "5EewuHxax...",
      "status": "Active",
      "node": "node1",
      "stake": "500 TAO",
      "score": 98,
      "earning": "100 xxx"
    }
    // ... 其余矿工
  ]
}
```

 **字段说明**

| 字段名  | 类型   | 说明     |
| ------- | ------ | -------- |
| address | string | 矿工地址 |
| status  | string | 状态     |
| node    | string | 节点     |
| stake   | string | 质押     |
| score   | int    | 得分     |
| earning | string | 收益     |

######  7、获取收益历史记录

 获取收益发放历史记录。

  **请求**

- URL: /api/earnings/history
- 方法: GET
- 请求参数: 无

  **返回值JSON**

```JSON
{
  "history": [
    {
      "time": "2025/6/6 11:11",
      "worker": "5EewuHxax...",
      "earning": "200xxx",
      "wallet_address": "0x4c6924...11a0fa",
      "hash": "0x4c6924...11a0fa"
    }
    // ... 其余记录
  ]
}
```

 **字段说明**

| 字段名         | 类型   | 说明     |
| -------------- | ------ | -------- |
| time           | string | 时间     |
| worker         | string | 矿工地址 |
| earning        | string | 收益     |
| wallet_address | string | 钱包地址 |
| hash           | string | 交易哈希 |

##### 3、验证者界面

######  1、获取验证者概览信息

 获取当前节点的验证者性能、网络参与情况、质押信息及下属矿工列表。

 **请求**

- URL: /api/validator/overview
- 方法: GET
- 请求参数: 无

 **返回值JSON**

```JSON
{
  "total_staked": "158.80 TAO",
  "staking_status": "Staking",
  "node": {
    "name": "Node1",
    "status": "Active"
  },
  "miner_count": 1024,
  "latency": "86ms",
  "cpu": "95.2%",
  "memory": "95.2%",
  "miners": [
    {
      "address": "5EewuHxax...",
      "status": "Active",
      "hashrate_1h": "320.05 Ph/s",
      "hashrate_24h": "320.05 Ph/s",
      "shares_1h": "66.78B",
      "shares_24h": "66.78B",
      "score": 98
    }
    // ... 其余矿工
  ]
}
```

**字段说明**

| 字段名         | 类型   | 说明       |
| -------------- | ------ | ---------- |
| total_staked   | string | 总质押     |
| staking_status | string | 质押状态   |
| node           | object | 节点信息   |
| name           | string | 节点名称   |
| status         | string | 节点状态   |
| miner_count    | int    | 矿工数量   |
| latency        | string | 网络延迟   |
| cpu            | string | CPU使用率  |
| memory         | string | 内存使用率 |
| miners         | array  | 矿工列表   |
| address        | string | 矿工地址   |
| status         | string | 矿工状态   |
| hashrate_1h    | string | 1小时算力  |
| hashrate_24h   | string | 24小时算力 |
| shares_1h      | string | 1小时份额  |
| shares_24h     | string | 24小时份额 |
| score          | int    | 得分       |

######  2、质押

- URL: /api/validator/stake
- 方法: POST
- 请求参数:

| 参数名 | 类型   | 必填 | 说明     |
| ------ | ------ | ---- | -------- |
| amount | string | 是   | 质押数量 |

 **请求示例**

```Plain
{
  "amount": "100 TAO"
}
```

**返回值JSON**

```Plain
{
  "success": true,
  "message": "质押成功"
}
```

######  3、解质押

- URL: /api/validator/unstake
- 方法: POST
- 请求参数:

| 参数名 | 类型   | 必填 | 说明     |
| ------ | ------ | ---- | -------- |
| amount | string | 是   | 解押数量 |

 **请求示例**

```Plain
{
  "amount": "50 TAO"
}
```

**返回值JSON**

```Plain
{
  "success": true,
  "message": "解押成功"
}
```

######  4、获取指定验证者详情

 获取指定验证者的实时算力、份额、历史数据等详细信息。

  **请求**

- URL: /api/validator/{address}/detail
- 方法: GET
- 请求参数:

| 参数名  | 类型   | 必填 | 说明       | 示例值   |
| ------- | ------ | ---- | ---------- | -------- |
| address | string | 是   | 验证者地址 | 5dhds... |

**返回值JSON**

```Bash
{
  "address": "5dhds...k9a",
  "status": "Active",
  "node": "Node1",
  "hashrate_5m": "$0.00/Hour",
  "hashrate_1h": "$0.00/Hour",
  "hashrate_24h": "$0.00/Hour",
  "hashrate_max": "$0.00/Hour",
  "shares_5m": "3.19T sh",
  "shares_1h": "3.19T sh",
  "shares_24h": "3.19T sh",
  "shares_total": "3.19T sh",
  "hashrate_history": [
    {"timestamp": 1719194400, "hashrate": 800.00},
    {"timestamp": 1719201600, "hashrate": 600.00}
    // ... 省略部分数据
  ],
  "shares_history": [
    {"timestamp": 1719194400, "shares": 400.00},
    {"timestamp": 1719201600, "shares": 800.00}
    // ... 省略部分数据
  ]
}
```

**字段说明**

| 字段名           | 类型   | 说明             |
| ---------------- | ------ | ---------------- |
| address          | string | 验证者地址       |
| status           | string | 状态（Active等） |
| node             | string | 节点名称         |
| hashrate_5m      | string | 5分钟平均算力    |
| hashrate_1h      | string | 1小时平均算力    |
| hashrate_24h     | string | 24小时平均算力   |
| hashrate_max     | string | 最大算力         |
| shares_5m        | string | 5分钟份额        |
| shares_1h        | string | 1小时份额        |
| shares_24h       | string | 24小时份额       |
| shares_total     | string | 总份额           |
| hashrate_history | array  | 算力历史数据     |
| shares_history   | array  | 份额历史数据     |
| timestamp        | int    | 时间戳（秒）     |
| hashrate         | float  | 算力值           |
| shares           | float  | 份额值           |

######  5、获取验证者收益总览

 获取当前验证者的昨日、多币种、累计收益等统计信息。

  **请求**

- URL: /api/validator/earnings/overview
- 方法: GET
- 请求参数: 无

  **返回值JSON**

```JSON
{
  "yesterday_xxx_rewards": "158.80 USD",
  "yesterday_ltc_rewards": "158.80 USD",
  "yesterday_doge_rewards": "158.80 USD",
  "total_xxx_rewards": "300.08",
  "total_ltc_rewards": "200.00",
  "total_doge_rewards": "100.00",
  "total_rewards": "158.80 USD"
}
```

**字段说明**

| 字段名                 | 类型   | 说明         |
| ---------------------- | ------ | ------------ |
| yesterday_xxx_rewards  | string | 昨日xxx收益  |
| yesterday_ltc_rewards  | string | 昨日LTC收益  |
| yesterday_doge_rewards | string | 昨日Doge收益 |
| total_xxx_rewards      | string | 累计xxx收益  |
| total_ltc_rewards      | string | 累计LTC收益  |
| total_doge_rewards     | string | 累计Doge收益 |
| total_rewards          | string | 累计总收益   |

######  6、获取收益走势图

 获取多币种收益的历史曲线数据，用于折线图展示。

  **请求**

- URL: /api/validator/earnings/chart
- 方法: GET
- 请求参数:

| 参数名 | 类型   | 必填 | 说明     | 示例值                |
| ------ | ------ | ---- | -------- | --------------------- |
| range  | string | 否   | 时间范围 | 24h/7d/30d（默认24h） |

**返回值JSON**

```JSON
{
  "range": "24h",
  "unit": "USD",
  "data": [
    {
      "timestamp": 1719194400,
      "xxx": 800.00,
      "ltc": 600.00,
      "doge": 400.00
    }
    // ... 省略部分数据
  ]
}
```

**字段说明**

| 字段名    | 类型   | 说明         |
| --------- | ------ | ------------ |
| range     | string | 时间范围     |
| unit      | string | 单位         |
| data      | array  | 历史数据数组 |
| timestamp | int    | 时间戳（秒） |
| xxx       | float  | xxx收益      |
| ltc       | float  | LTC收益      |
| doge      | float  | Doge收益     |

######  7、获取收益记录

 获取收益发放历史记录。

  **请求**

- URL: /api/validator/earnings/history
- 方法: GET
- 请求参数:

| 参数名     | 类型   | 必填 | 说明                       |
| ---------- | ------ | ---- | -------------------------- |
| type       | string | 否   | 币种类型（可选，默认全部） |
| start_time | string | 否   | 开始时间                   |
| end_time   | string | 否   | 结束时间                   |

  **返回值JSON**

```JSON
{
  "history": [
    {
      "time": "2025/6/6 11:11",
      "avg_hashrate": "167.4PH/s",
      "earning": "200xxx",
      "wallet_address": "0x4c6924...11a0fa",
      "hash": "0x4c6924...11a0fa"
    }
    // ... 其余记录
  ]
}
```

  **字段说明**

| 字段名         | 类型   | 说明     |
| -------------- | ------ | -------- |
| time           | string | 时间     |
| avg_hashrate   | string | 平均算力 |
| earning        | string | 收益     |
| wallet_address | string | 钱包地址 |
| hash           | string | 交易哈希 |

######  8、获取验证者钱包列表  

  获取当前验证者已绑定的钱包列表（如LTC、Doge等）。

  **请求**

- URL: /api/validator/wallets
- 方法: GET
- 请求参数: 无

**返回值JSON**

```JSON
{
  "wallets": [
    {
      "coin": "LTC",
      "wallet_name": "My LTC Wallet",
      "address": "eg:0x4jkf2...93kaqp"
    },
    {
      "coin": "Doge",
      "wallet_name": "My Doge Wallet",
      "address": "eg:0x4jkf2...93kaqp"
    }
  ]
}
```

 **字段说明**

######  9、添加钱包

 为指定币种添加或绑定一个新钱包。

  **请求**

- URL: /api/validator/wallets/add
- 方法: POST
- 请求参数:

| 参数名      | 类型   | 必填 | 说明     |
| ----------- | ------ | ---- | -------- |
| coin        | string | 是   | 币种     |
| wallet_name | string | 是   | 钱包名称 |
| address     | string | 是   | 钱包地址 |

  **返回值JSON**

```json
{
  "success": true,
  "message": "钱包添加成功"
}
```

######  10、编辑钱包

  编辑已绑定的钱包信息（如钱包名称、地址）。

- URL: /api/validator/wallets/edit
- 方法: POST
- 请求参数:

| 参数名      | 类型   | 必填 | 说明                |
| ----------- | ------ | ---- | ------------------- |
| coin        | string | 是   | 币种（如LTC、Doge） |
| wallet_name | string | 否   | 新钱包名称          |
| address     | string | 否   | 新钱包地址          |

**返回值JSON**

```Plain
{
  "success": true,
  "message": "钱包信息已更新"
}
```

##### 4、质押界面

######  1、获取可质押验证者列表 

  **请求**

- URL: /api/stake/validators
- 方法: GET
- 请求参数:

| 参数名   | 类型   | 必填 | 说明                 | 示例值   |
| -------- | ------ | ---- | -------------------- | -------- |
| search   | string | 否   | 按验证者地址模糊搜索 | 5dhds... |
| page     | int    | 否   | 页码                 | 1        |
| pageSize | int    | 否   | 每页数量             | 10       |
| node     | string | 否   | 节点筛选（可选）     | Node1    |

**返回值JSON**

```JSON
{
  "total": 30,
  "page": 1,
  "pageSize": 10,
  "validators": [
    {
      "address": "5dhds...k9a",
      "status": "Active",
      "hashrate_24h": "320.05 Ph/s",
      "hashrate_1h": "320.05 Ph/s",
      "total_stake": "500 TAO",
      "weight": 98,
      "emission": 100
    }
    // ... 其余验证者
  ]
}
```

**字段说明**

| 字段名       | 类型   | 说明             |
| ------------ | ------ | ---------------- |
| total        | int    | 总验证者数       |
| page         | int    | 当前页码         |
| pageSize     | int    | 每页数量         |
| miners       | array  | 验证者信息列表   |
| address      | string | 验证者地址       |
| status       | string | 状态（Active等） |
| hashrate_24h | string | 24小时算力       |
| hashrate_1h  | string | 1小时算力        |
| total_stake  | string | 总质押           |
| weight       | int    | 权重             |
| emission     | int    | xxx产出          |

######  2、质押

 对指定验证者进行质押。

**请求**

- URL: /api/stake
- 方法: POST
- 请求参数:

| 参数名  | 类型   | 必填 | 说明       |
| ------- | ------ | ---- | ---------- |
| address | string | 是   | 验证者地址 |
| amount  | string | 是   | 质押数量   |

**请求示例**

```Plain
{
  "address": "5dhds...k9a",
  "amount": "100 TAO"
}
```

**返回值JSON**

```Plain
{
  "success": true,
  "message": "质押成功"
}
```

######  3、获取用户质押过验证者节点

 获取当前用户质押过的所有节点及其质押、收益、锁定状态等信息。

  **请求**

- URL: /api/stake/my-nodes
- 方法: GET
- 请求参数:

| 参数名 | 类型   | 必填 | 说明                                    | 示例值 |
| ------ | ------ | ---- | --------------------------------------- | ------ |
| status | string | 否   | 锁定状态筛选（Staking/Locked/Unlocked） |        |

  **返回值JSON**

```JSON
{
  "nodes": [
    {
      "address": "5dhds...k9a",
      "lock_status": "Staking",      // Staking | Locked | Unlocked
      "staked": "500 TAO",
      "earn": "100 xxx",
      "can_claim": true,
      "can_unstake": true,
      "can_stake": true,
      "can_withdraw": false
    },
    {
      "address": "5dhds...k9a",
      "lock_status": "Locked",
      "staked": "500 TAO",
      "earn": "100 xxx",
      "can_claim": true,
      "can_unstake": false,
      "can_stake": false,
      "can_withdraw": false
    },
    {
      "address": "5dhds...k9a",
      "lock_status": "Unlocked",
      "staked": "500 TAO",
      "earn": "100 xxx",
      "can_claim": true,
      "can_unstake": false,
      "can_stake": false,
      "can_withdraw": true
    }
    // ... 其余节点
  ]
}
```

**字段说明**

| 字段名       | 类型   | 说明                                |
| ------------ | ------ | ----------------------------------- |
| address      | string | 节点地址                            |
| lock_status  | string | 锁定状态（Staking/Locked/Unlocked） |
| staked       | string | 质押数量                            |
| earn         | string | 可领取收益                          |
| can_claim    | bool   | 是否可领取收益                      |
| can_unstake  | bool   | 是否可解押                          |
| can_stake    | bool   | 是否可追加质押                      |
| can_withdraw | bool   | 是否可提现                          |

######  4、领取收益

 领取指定节点的可领取收益。

  **请求**

- URL: /api/stake/claim
- 方法: POST
- 请求参数:

| 参数名  | 类型   | 必填 | 说明     |
| ------- | ------ | ---- | -------- |
| address | string | 是   | 节点地址 |

**返回值JSON**

```Plain
{
  "success": true,
  "message": "收益领取成功"
}
```

######  5、解质押

 对指定节点进行解押操作。

  **请求**

- URL: /api/stake/unstake
- 方法: POST
- 请求参数:

| 参数名  | 类型   | 必填 | 说明     |
| ------- | ------ | ---- | -------- |
| address | string | 是   | 节点地址 |

**返回值JSON**

```Plain
{
  "success": true,
  "message": "解押成功"
}
```

######  6、提现

 对已解锁节点进行提现操作。

  **请求**

- URL: /api/stake/withdraw
- 方法: POST
- 请求参数:

| 参数名  | 类型   | 必填 | 说明     |
| ------- | ------ | ---- | -------- |
| address | string | 是   | 节点地址 |

  **返回值JSON**

```Plain
{
  "success": true,
  "message": "提现成功"
}
```