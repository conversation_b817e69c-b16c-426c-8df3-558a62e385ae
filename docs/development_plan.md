# TaoHash 基于 Bittensor 的挖矿业务

---

## 1. 项目总览与目标

1. 搭建 **本地 Subtensor 区块链** + **测试网** 双环境，支撑日常开发。
2. 开发并完善 **TaoHash 子网**：协议、经济模型、奖励分配、监控运维。
3. 完成 **硬件矿工 → 矿池 → 子网 → 奖励** 的全链路闭环。
4. 输出完整文档、CI/CD、测试用例，确保其他成员可快速上手。

---

## 2. 迭代规划总览

| 模块 | 关键产出 |
|------|----------|
| A. Bittensor 基础 & 子网搭建 | 本地链 + 测试网接入指南 / 子网创建脚本 |
| B. 子网经济设计（代币发行 & 激励） | 经济模型文档 / 代币发行 PoC / 链上参数配置 |
| C. 硬件矿工与矿池接入 | taohash-proxy 最小化部署 / ASIC 接入指南 |
| D. 工作量度量与数据采集 | Share 数据管道 / ClickHouse Schema / API 原型 |
| E. 奖励计算、权重 & 分发 | 评分算法实现 / 权重上链脚本 / 单元测试 |
| F. 运维、监控与自动化 | Grafana Dashboard / Alert 体系 / 部署脚本 |
| G. 端到端测试 & 文档完善 | E2E 测试场景 / 开发者手册 / 交付物审计 |



---

## 3. 模块拆解与迭代任务

### A. Bittensor 基础 & 子网搭建

**任务 A1**
1. 阅读 [官方本地部署文档](https://docs.learnbittensor.org/local-build/deploy)。
2. 使用 Docker 启动 `subtensor-localnet`，编写一键脚本 `scripts/start_local_chain.sh`。
3. 通过 `btcli subnet list --network ws://127.0.0.1:9944` 验证区块链运行。
4. 产出：《本地链快速上手指南》markdown 文档。

**任务 A2**
1. 创建测试钱包，完成子网注册、矿工/验证者注册流程脚本化。
2. 在测试网 (`--subtensor.network test`) 重复上述流程，记录差异点。
3. 输出：《子网创建与注册流水线》文档 + CI Job（GitHub Actions）。

---

### B. 子网经济设计（代币发行 & 激励）

**任务 B1**
1. 梳理 TaoHash 现有经济参数：`VERSION_KEY`, `BLOCK_TIME`, `U16_MAX` 等。
2. 研究 Bittensor 代币发行机制与 `Dynamic TAO`。
3. 编写经济模型草案（Markdown + Mermaid 流程图）。

**任务 B2**
1. 基于本地链生成自定义创世文件，配置发行量、通胀率、奖励曲线。
2. 在本地链验证铸币与奖励事件是否符合预期。
3. 输出：《经济模型实现方案》+ 创世配置 JSON。

---

### C. 硬件矿工与矿池接入

**任务 C1**
1. 克隆 `taohash-proxy`，使用 `docker-compose` 最小化启动（仅 3331/3332 端口）。
2. 连接模拟矿机 (或 `cgminer --benchmark`) 验证 share 提交路径。
3. 产出：`docs/proxy_quickstart.md`，含常见矿机配置示例。

**任务 C2**
1. 集成 ASIC 真实设备或云算力，记录连接、功耗、Hashrate 数据。
2. 优化 proxy 配置热更新脚本；在 slot 变更时自动重载配置。
3. 输出：《矿机接入白皮书》+ Demo 视频（可选）。

---

### D. 工作量度量与数据采集

**任务 D1**
1. 设计 ClickHouse 表：`shares`, `workers`, `pools`。
2. 编写 Ingest 服务 (FastAPI) → 收集 `taohash-proxy` 导出的 JSON。
3. 单元测试覆盖 ETL 逻辑。

**任务 D2**
1. 实现指标 API（REST）：`/api/workers/stats`, `/api/pool/stats`。
2. 对接 Grafana Datasource，制作实时算力 Dashboard。
3. 输出：《数据管道与指标文档》+ Swagger 文档。

---

### E. 奖励计算、权重 & 分发

**任务 E1**
1. 阅读 `taohash/validator` 下的 `evaluate_miner_hashrate` 实现。
2. 设计工作量 → 价值 → 分数映射公式（支持移动平均）。
3. 实现 `MovingAverageScorer`，补充单元测试。

**任务 E2**
1. 编写上链权重脚本 (`btcli weights set ...`) 并在本地链验证。
2. 集成 Redis 用于持久化分数与快照。
3. 输出：《奖励分配技术方案》+ 完整 CI 测试。

---

### F. 运维、监控与自动化

**任务 F1**
1. 编写 `docker-compose` 一键部署：本地链 + proxy + validator + miner + Grafana。
2. 配置 Prometheus Exporter，监控区块高度、share TPS、CPU/内存。
3. Alertmanager 集成钉钉/Slack 警报。
4. 输出：《DevOps 手册》+ 监控仪表板 JSON。

---

### G. 端到端测试 & 文档完善

**任务 G1**
1. 使用 `pytest` + `playwright` 编写端到端测试：模拟矿机提交 → 评分 → 上链权重流程。
2. 审计所有模块 README，确保新人 clone 后 30 分钟可跑通。
3. 输出：`docs/README_CN.md`（新手入口）+ `tests/e2e/` 目录。

---

## 4. 团队协作与质量保证

1. **每日站会** ：同步进展、阻碍、计划。
2. **迭代回顾**：每完成一迭代，进行代码审查 + Demo + 文档检查。
3. **CI Policy**：
   - PR 必须通过单元测试、lint (`ruff`)、型检查 (`mypy`)。
   - 必须更新相关文档、CHANGELOG。
4. **知识共享**：内部 Wiki / Lunch & Learn，每周一主题。都使用github.com

---
