# CI/CD 流程文档

本文档描述了TAO Hash项目的CI/CD流程和相关配置。

## 概述

项目使用Woodpecker CI进行持续集成和部署，包含以下三个主要流程：

1. **测试流程** (`.woodpecker/docker-test.yml`)
2. **Docker构建流程** (`.woodpecker/docker.yml`) 
3. **清理流程** (`.woodpecker/clean-docker.yml`)

## 测试流程

### 单元测试
- **位置**: `hash/tests/unit_tests/`
- **运行环境**: Python 3.11-slim
- **依赖**: 自动安装项目依赖
- **命令**: `python -m pytest tests/unit_tests/ -v --tb=short`

### 集成测试
- **位置**: `hash/tests/integration_tests/`
- **运行环境**: Docker-in-Docker (Alpine + Python)
- **依赖服务**: Redis (通过Docker Compose)
- **管理脚本**: `run_integration_tests.py`
- **特性**: 自动启动/停止依赖服务，包含健康检查

### 运行测试

```bash
# 触发测试流程
woodpecker-cli exec --event manual .woodpecker/docker-test.yml
```

## Docker构建流程

### 构建的镜像

1. **TAO Hash核心服务** (`coinflow/taohash`)
   - 标签: `latest`, `{commit-sha}`
   - 运行模式: 默认

2. **TAO Hash矿工服务** (`coinflow/taohash-miner`)
   - 标签: `latest-miner`, `{commit-sha}-miner`
   - 运行模式: 矿工

3. **TAO Hash验证者服务** (`coinflow/taohash-validator`)
   - 标签: `latest-validator`, `{commit-sha}-validator`
   - 运行模式: 验证者

4. **主应用服务** (`coinflow/mining-pool`)
   - 保留原有功能

### Docker镜像特性

- **多架构支持**: `linux/amd64`, `linux/arm64`
- **构建缓存**: 使用本地缓存优化构建速度
- **运行模式**: 通过`TAOHASH_MODE`环境变量控制
- **健康检查**: 内置健康检查端点
- **安全**: 使用非root用户运行

### 本地开发

```bash
# 构建镜像
cd hash
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 清理流程

### 自动清理内容

1. **停止测试容器**: `taohash-test-redis`
2. **清理Docker Compose服务**: 集成测试相关
3. **清理网络**: 移除未使用的网络
4. **清理卷**: 移除未使用的卷
5. **状态报告**: 显示清理后的容器和网络状态

### 手动触发清理

```bash
# 清理所有遗留资源
woodpecker-cli exec --event manual .woodpecker/clean-docker.yml
```

## 环境变量

### CI环境变量

- `CI_WORKSPACE`: Woodpecker工作目录
- `CI_COMMIT_SHA`: Git提交哈希
- `DOCKERHUB_USERNAME`: Docker Hub用户名 (Secret)
- `DOCKERHUB_TOKEN`: Docker Hub令牌 (Secret)

### 应用环境变量

- `TAOHASH_MODE`: 运行模式 (`default`|`miner`|`validator`)
- `PYTHONPATH`: Python模块路径
- `PYTHONUNBUFFERED`: Python输出缓冲控制

## 文件结构

```
.woodpecker/
├── docker-test.yml       # 测试流程
├── docker.yml           # Docker构建流程  
└── clean-docker.yml     # 清理流程

hash/
├── Dockerfile           # 主Docker文件
├── docker-compose.yml   # 本地开发环境
└── tests/
    ├── unit_tests/      # 单元测试
    └── integration_tests/
        ├── docker-compose.yml    # 测试依赖服务
        └── run_integration_tests.py  # 测试管理脚本
```

## 最佳实践

### 开发流程

1. 编写代码和测试
2. 本地运行测试: `cd hash && python -m pytest tests/`
3. 本地测试Docker构建: `docker-compose build && docker-compose up`
4. 提交代码并触发CI流程

### 故障排除

#### 测试失败
- 检查单元测试日志
- 确认集成测试依赖服务状态
- 验证测试环境配置

#### 构建失败
- 检查Dockerfile语法
- 验证依赖项安装
- 确认Docker Hub凭据

#### 清理问题
- 手动运行清理流程
- 检查Docker服务状态
- 重启Docker服务

## 监控和维护

### 定期检查

- Docker镜像大小优化
- 依赖项安全更新
- 缓存清理
- 性能监控

### 升级策略

- 测试环境先行
- 渐进式部署
- 回滚方案准备
- 监控部署状态