when:
  event: manual

steps:
  cleanup-integration-services:
    image: docker:24-dind
    privileged: true
    environment:
      - DOCKER_TLS_CERTDIR=""
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    commands:
      - |
        echo "=== 清理集成测试Docker服务 ==="
        
        # 检查并停止集成测试容器
        echo "停止集成测试相关容器..."
        docker stop taohash-test-redis || echo "Redis测试容器未运行"
        docker rm taohash-test-redis || echo "Redis测试容器不存在"
        
        # 清理集成测试docker-compose服务
        echo "清理集成测试docker-compose服务..."
        cd /drone/src/hash/tests/integration_tests
        docker-compose down -v --remove-orphans || echo "docker-compose服务已清理或不存在"
        
        # 清理相关网络
        echo "清理测试网络..."
        docker network prune -f
        
        # 清理未使用的卷
        echo "清理未使用的卷..."
        docker volume prune -f
        
        # 显示清理后状态
        echo "=== 清理完成，当前容器状态 ==="
        docker ps -a | grep taohash || echo "没有发现taohash相关容器"
        
        echo "=== 当前网络状态 ==="
        docker network ls | grep integration || echo "没有发现integration相关网络"