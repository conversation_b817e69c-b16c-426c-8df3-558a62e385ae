when:
  event: manual

volumes:
  docker_cache:
    driver: local

steps:
  build-docker-taohash:
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      repo: coinflow/taohash
      dockerfile: hash/Dockerfile
      context: hash
      platforms:
        - linux/amd64
        - linux/arm64
      tags:
        - latest
        - ${CI_COMMIT_SHA:0:8}
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local,src=/cache'
      cache_to: 
        - 'type=local,dest=/cache,mode=max'

  build-docker-taohash-miner:
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      repo: coinflow/taohash-miner
      dockerfile: hash/Dockerfile
      context: hash
      platforms:
        - linux/amd64
        - linux/arm64
      tags:
        - latest-miner
        - ${CI_COMMIT_SHA:0:8}-miner
      build_args:
        - TAOHASH_MODE=miner
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local,src=/cache'
      cache_to:
        - 'type=local,dest=/cache,mode=max'

  build-docker-taohash-validator:
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      repo: coinflow/taohash-validator
      dockerfile: hash/Dockerfile
      context: hash
      platforms:
        - linux/amd64
        - linux/arm64
      tags:
        - latest-validator
        - ${CI_COMMIT_SHA:0:8}-validator
      build_args:
        - TAOHASH_MODE=validator
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local,src=/cache'
      cache_to:
        - 'type=local,dest=/cache,mode=max'

  build-docker-pool:
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      repo: coinflow/mining-pool
      dockerfile: Dockerfile
      context: .
      platforms:
        - linux/amd64
        - linux/arm64
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local,src=/cache'
      cache_to: 
        - 'type=local,dest=/cache,mode=max'