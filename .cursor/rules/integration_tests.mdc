# Integration Tests Rules
# 集成测试规则和最佳实践

## 测试结构
- 将集成测试放在 `tests/integration_tests/` 目录下
- 每个模块创建对应的测试文件，如 `test_api_auth.py`
- 使用 pytest 作为测试框架
- 测试文件以 `test_` 开头命名

## 测试环境
- 使用 Docker Compose 启动外部依赖服务（Redis、数据库等）
- 使用真实的服务实例进行测试，而不是 mock
- 每个测试用例之间应该是独立的，可以并行运行
- 测试前后进行环境清理

## API 集成测试
- 测试完整的 HTTP 请求/响应周期
- 验证状态码、响应格式、数据完整性
- 测试认证和授权流程
- 测试错误场景和边界条件
- 使用真实的请求数据

## 钱包认证测试
- 模拟真实的钱包签名过程
- 测试多种钱包类型（Substrate、Ethereum）
- 验证 nonce 生成和验证机制
- 测试 token 生成和验证
- 测试会话管理和过期机制

## 测试数据管理
- 使用固定的测试数据进行可重复测试
- 测试地址和私钥应该是专门为测试生成的
- 不使用真实的主网数据
- 测试完成后清理生成的数据

## 异步测试
- 使用 pytest-asyncio 处理异步测试
- 正确管理事件循环
- 测试异步服务调用
- 处理并发场景

## 错误处理测试
- 测试各种错误条件
- 验证错误消息格式
- 测试系统在错误状态下的恢复能力
- 测试超时和重试机制

## 性能测试
- 包含基本的性能验证
- 测试响应时间阈值
- 验证并发处理能力
- 监控资源使用情况

## CI/CD 集成
- 确保测试可以在 CI 环境中运行
- 提供清晰的测试输出和报告
- 失败时提供详细的诊断信息
- 支持测试结果缓存

## 测试命名约定
- 测试类使用 `Test` 前缀
- 测试方法使用描述性名称
- 使用 `test_功能_场景_预期结果` 格式
- 例如：`test_wallet_auth_valid_signature_returns_token`

## 断言最佳实践
- 使用明确的断言消息
- 验证关键字段的存在和格式
- 测试数据的完整性和一致性
- 使用 pytest 的断言助手

## 测试文档
- 每个测试用例添加清晰的文档字符串
- 说明测试的目的和预期行为
- 记录测试数据的来源和含义
- 提供故障排除指南